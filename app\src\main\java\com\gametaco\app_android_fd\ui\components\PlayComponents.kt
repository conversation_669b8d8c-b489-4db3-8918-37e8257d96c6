package com.gametaco.app_android_fd.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.gametaco.app_android_fd.LocalAlertDialogManager
import com.gametaco.app_android_fd.data.entity.APIGamesResponseGame
import com.gametaco.app_android_fd.data.entity.APITournament
import com.gametaco.app_android_fd.data.entity.APITournamentState
import com.gametaco.app_android_fd.data.entity.APIWalletResponse
import com.gametaco.app_android_fd.data.entity.EntryFee
import com.gametaco.app_android_fd.manager.ActivityManager
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.FDWebviewPage
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.ui.popups.AlertDialogTextCancel
import com.gametaco.app_android_fd.ui.screens.GeoAlertModel
import com.gametaco.app_android_fd.ui.screens.GeoAlertType
import com.gametaco.app_android_fd.ui.theme.ShadowFade
import com.gametaco.app_android_fd.utils.formatEST
import com.gametaco.app_android_fd.utils.toDate
import com.gametaco.app_android_fd.utils.toDollarWithCommaString
import com.gametaco.app_android_fd.viewmodel.LoginViewModel
import com.gametaco.app_android_fd.viewmodel.PlayViewModel
import com.gametaco.utilities.ResourceState
import com.gametaco.utilities.STR
import kotlinx.coroutines.delay
import resources.R
import toDp
import java.util.Date

@Composable
fun ContestGroup(
    title: String,
    subtitle: String,
    icon:Int? = null,
    contestLists: List<@Composable ()->Unit>
){
    ContentBox(
        titleComposable = {
            Column(
                verticalArrangement = Arrangement.spacedBy(0.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 12.dp, bottom = 0.dp)
            ){
                val textPadding = PaddingValues(start = 12.dp,end = 12.dp)

                Column (verticalArrangement = Arrangement.spacedBy(12.dp), horizontalAlignment = Alignment.Start,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 12.dp)) {

                    Row(horizontalArrangement = Arrangement.Start, 
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(start = 12.dp)
                    ) {
                        val game = contestLists.getOrNull(0)
                        Image(
                            painter = painterResource(id = icon ?: R.drawable.ic_seasonal_checklist),
                            alignment = Alignment.CenterStart,
                            contentDescription = null,
                            modifier = Modifier
                                .size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = title,
                            style = MaterialTheme.typography.titleMedium,
                            color = Color.White,
//                            modifier = Modifier.padding(textPadding)
                        )
                    }

                    Text(
                        text = subtitle,
                        style = MaterialTheme.typography.headlineMedium,
                        color = Color.White,
                        modifier = Modifier.padding(textPadding)
                    )
                }

                for (list in contestLists){
                    list()
                }
            }
        },
        titlePaddingValues = PaddingValues(0.dp),
        bodyPaddingValues = PaddingValues(0.dp),
        boarderSize = 0.dp,
        background = Color(0x400A0A0A)
    )
}

@Composable
fun ContestList(
    title: String,
    subtitle: String? = null,
    listItems: List<@Composable ()->Unit>,
    showGuestBlock:Boolean = false,
    noFunds:Boolean = false,
){
    Column(
    ){
        Box(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(0.dp)
            ){
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(20.dp)
                        .background(
                            Color(if (noFunds) 0xFF6A6F73 else 0x4C0A0A0A),
                        )
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.onPrimary,
                        modifier = Modifier
                            .align(Alignment.CenterStart)
                            .padding(start = 12.dp)
                    )
                }

                var size by remember {
                    mutableStateOf(IntSize(0,0))
                }
                Box(modifier = Modifier.onSizeChanged {
                    size = it
                }){
                    Column {
                        for (index in listItems.indices){
                            if (index != 0){
                                HorizontalDivider(
                                    modifier = Modifier.padding(start = 0.dp)
                                )
                            }

                            listItems[index]()
                        }
                    }
                    if(showGuestBlock){
                        GuestBlockComponent(
                            title = STR(R.string.join_now_to_unlock_all_contests),
                            desc = STR(R.string.once_you_join_you_can_play_for_cash),
                            label = STR(R.string.join_now_and_unlock_all_contests),
                            onClick = { LoginViewModel.instance.fanduelSignupFromGuestMode() },
                            modifier = Modifier.height(size.toDp().height)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ContestListItem(
    tournament:APITournament,
    wallet:APIWalletResponse,
    onInfoButton: ()->Unit,
    titlesOnTop: Boolean = false,
    gameId : String?,
    lobbySelection: String? = null,
    showNoFounds:Boolean = false,
){
    val noFunds = (getTournamentState(tournament,wallet) == APITournamentState.fundsIneligibility)
            && (wallet.balance.toDouble() == 0.0) && (wallet.bonus_balance.toDouble() == 0.0)

    Column() {
        if(lobbySelection == null && tournament.is_free_entry == true){
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(20.dp)
                    .background(
                        Color(0xFF0070EB),
                    )
            ) {
                Row(horizontalArrangement = Arrangement.Start,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .padding(horizontal = 12.dp)
                        .align(Alignment.CenterStart)
                ) {
                    Image(
                        painter = painterResource(R.drawable.ic_free_entry),
                        alignment = Alignment.CenterStart,
                        contentDescription = null,
                        modifier = Modifier
                            .size(16.dp)
                    )
//                    Spacer(modifier = Modifier.width(2.dp))
                    val entry_number_str = if(tournament.free_entry_ticket_without_expiry_number > 1 && tournament.free_entry_ticket_expires_at == null) " (${tournament.free_entry_ticket_without_expiry_number} ENTRIES AVAILABLE)" else ""
                    Text(
                        text = "FREE ENTRY REWARDS!${entry_number_str}",
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.onPrimary,
                        modifier = Modifier
                            .align(Alignment.CenterVertically)
                            .padding(start = 12.dp)
                    )

                    val expires_at = tournament.free_entry_ticket_expires_at?.toDate()
                    if(expires_at != null){
                        val currentTimeMillis = remember { mutableStateOf(System.currentTimeMillis()) }

                        LaunchedEffect(Unit) {
                            while (true) {
                                currentTimeMillis.value = System.currentTimeMillis()
                                delay(60_000L) // 1 minute
                            }
                        }

                        val timeRemainSec = (expires_at.time - currentTimeMillis.value) / 1000
                        val timeRemainMins = timeRemainSec / 60
                        val hours = timeRemainMins / 60
                        val mins = timeRemainMins % 60

                        Box(modifier = Modifier.fillMaxSize()){
                            Text(
                                text = "EXPIRES IN ${hours}H ${mins}M",
                                style = MaterialTheme.typography.headlineSmall,
                                color = MaterialTheme.colorScheme.onPrimary,
                                modifier = Modifier
                                    .align(Alignment.CenterEnd)
                                    .padding(start = 12.dp)
                            )
                        }
                    }
                }
            }
        }
        if (showNoFounds) {
            Column(modifier = Modifier
                .fillMaxWidth()
                .background(color = Color(0xFFEAF4FF))) {
                NoFunds()
                HorizontalDivider(
                    modifier = Modifier
                        .fillMaxWidth(),
                    thickness = 1.dp,
                    color = Color(0xFFB0B7BF)
                )
            }
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(58.dp)
                .background(color = if (noFunds) Color(0xFFEAF4FF) else Color.White)
                .padding(12.dp, 6.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .padding(vertical = 4.dp)
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_info),
                    contentDescription = null,
                    tint = if (noFunds) MaterialTheme.colorScheme.outline else MaterialTheme.colorScheme.primary,
                    modifier = Modifier
                        .size(15.dp)
                        .align(Alignment.Center)
                        .clickable {
                            onInfoButton()
                        }
                )
            }
            var totalPrize = 0f
            for (prize in tournament.prizes) {
                totalPrize += prize.prize_amount.toFloat()*(prize.end - prize.start + 1)
            }

            val playersStr = tournament.maximum_slots?.toString() ?: "∞"
            val entryFee = tournament.entry_fee?.toDouble() ?: 0.0
            ContestInfo(
                title = STR(R.string.prize_pool_caps),
                value = totalPrize.toDollarWithCommaString(),
                width = 60.dp,
                titleOnTop = titlesOnTop,
                highlight = totalPrize >= 20
            )
            ContestInfo(
                title = STR(R.string.players_caps),
                value = playersStr,
                width = 48.dp,
                titleOnTop = titlesOnTop
            )
            ContestInfo(
                title = STR(R.string.entry_caps),
                value = if (entryFee > 0) entryFee.toDollarWithCommaString() else "FREE",
                width = 80.dp,
                titleOnTop = titlesOnTop,
                entry_waived = tournament.is_free_entry == true
            )
            Box(
                modifier = Modifier
                    .width(80.dp)
                    .height(34.dp)
            ) {
                ContestPlayButton(tournament, wallet, null, gameId = gameId, lobbySelection)
            }
        }
    }
}

@Composable
fun NoFunds() {
    Column(modifier = Modifier.padding(16.dp,12.dp)) {
        Text(text = "Ready to compete for cash?",
            textAlign = TextAlign.Center,
            color = Color(0xFF05285A),
            style = MaterialTheme.typography.labelMedium.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp
            ),
            overflow = TextOverflow.Visible,
            softWrap = false
        )
        Row(modifier = Modifier.padding(vertical = 2.dp)) {
            Text(modifier = Modifier
                .clickable() {
                    FDManager.instance.showWebviewFD(FDWebviewPage.Deposit)
                },
                text = "Deposit funds",
                color = Color(0xFF005FC8),
                style = MaterialTheme.typography.labelMedium.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 14.sp
                ),
            )
            Text(
                text = " to play paid contests",
                color = Color(0xFF05285A),
                style = MaterialTheme.typography.labelMedium.copy(
                    fontWeight = FontWeight.Normal,
                    fontSize = 14.sp
                ),
            )
        }
    }
}

@Composable
fun ContestPlayButton(tournament: APITournament,
                      wallet:APIWalletResponse,
                      subTitle: String? = null,
                      gameId: String?,
                      lobbySelection:String? = null){
    val tournamentState : APITournamentState = getTournamentState(tournament,wallet)
    val title = getActionButtonLabel(tournament,tournamentState)
    val alertDialogManager = LocalAlertDialogManager.current

    var enabled = true
    if (tournamentState == APITournamentState.playedGamesIneligibility) {
        enabled = false
    } else if (tournamentState == APITournamentState.fundsIneligibility && (wallet.balance.toDouble() == 0.0)) {
        enabled = false
    }

    if(enabled){
        CommonButton(
            title = title,
            subTitle = subTitle,
            autoSizeTile = true,
            fillColor = if(tournament.is_for_staff == true) Color(0xffffdb2f) else MaterialTheme.colorScheme.tertiary,
            textColor = if(tournament.is_for_staff == true) Color(0xff42554f) else MaterialTheme.colorScheme.onTertiary,
            onClick = {
                if(tournamentState == APITournamentState.fundsIneligibility) {
                    alertDialogManager.showDialogCustom(composeFunction = @Composable {
                        AlertDialogTextCancel(title = ActivityManager.instance.activity.getString(R.string.insufficient_funds_title),
                            message = ActivityManager.instance.activity.getString(R.string.insufficient_funds_msg),
                            aButtonText = ActivityManager.instance.activity.getString(R.string.make_a_deposit),
                            onAPressed = {
                                FDManager.instance.showWebviewFD(FDWebviewPage.Deposit)
                            },
                            bButtonText = ActivityManager.instance.activity.getString(R.string.cancel),
                            onBPressed = {
                            })
                    })

                    PlayViewModel.instance.logJoinFailedEvent(reason = "funds.ineligibility", tournament = tournament)
                }else if(tournamentState == APITournamentState.locationIneligibility){
                    GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_LOCATION)
                    PlayViewModel.instance.logJoinFailedEvent(reason = GeoAlertType.GEO_ALERT_LOCATION.name, tournament = tournament)
                }else if(tournamentState == APITournamentState.maximumEntriesIneligibility){
                    alertDialogManager.showDialog("Entry limit reached", "You’ve hit the limit of unmatched contests\n" +
                            "for this prize pool. Play a different contest until one of your open contests in this prize pool gets matched.", "Choose a different contest", {
                    })
                    PlayViewModel.instance.logJoinFailedEvent(reason = "entry.limit.reached", tournament = tournament)
                }else{
                    if(lobbySelection != null){
                        AnalyticsManager.instance.logEvent(
                            AnalyticsEvent(
                                analyticsEvent = AnalyticsEventName.Game_Selected.value,
                                properties = mapOf(
                                    "Game Name" to (tournament.game_name ?: "Unknown"),
                                    "Lobby Selection" to lobbySelection,
                                )
                            )
                        )
                    }

                    if(tournament.entry_id != null){//this is for unlimited tournaments, if entry_is is not null then we should re_enter the tournament:https://worldwinner.atlassian.net/browse/FX-2073
                        TournamentManager.instance.reEnterTournament(
                            tournamentInstanceEntryId = tournament.entry_id,
                            gameId = gameId?:"",
                            entryFee = if(tournament.is_free_entry == true) EntryFee("0.00",tournament.entryFee.string) else tournament.entryFee,
                        )
                    }else{
                        if(tournament.maximum_slots == null || tournament.maximum_slots > 2){
                            PreferencesManager.instance.setTournamentPrizesCache(tournament)
                        }
                        PlayViewModel.instance.joinTournament(tournament.id, gameId?:"", tournament)
                    }
                }
            },
        )
    }else{
        DisabledButton(
            title = title,
            subTitle = subTitle,
            textColor = Color(0xFFB0B7BF),
        )
    }
}
fun getTournamentState(tournament:APITournament,
                       wallet:APIWalletResponse):APITournamentState{
    var tournamentState : APITournamentState = APITournamentState.eligibility

    if(tournament.inaccessibility_criteria?.locationIneligibility != null){
        tournamentState = APITournamentState.locationIneligibility
    }else if(tournament.inaccessibility_criteria?.maximum_entries_ineligibility != null){
        tournamentState = APITournamentState.maximumEntriesIneligibility
    }else if(tournament.inaccessibility_criteria?.played_games_ineligibility != null){
        tournamentState = APITournamentState.playedGamesIneligibility
    }else{
        if(tournament.entry_fee != null){
            val entryFee = tournament.entry_fee.toDouble()
            if(entryFee > 0 && entryFee > wallet.balance.toDouble()){
                tournamentState = APITournamentState.fundsIneligibility
            }

        }
    }
    return tournamentState
}
fun getActionButtonLabel(tournament: APITournament,
                         tournamentState: APITournamentState):String{
    var title = "Play Now"

    if(tournament.entry_id != null){
        title = "Re-Enter"
    }else{
        when(tournamentState){
            APITournamentState.locationIneligibility -> {title = "More Info"}
            APITournamentState.maximumEntriesIneligibility -> {title = "Used"}
            else -> {
            }
        }
    }

    return title
}
@Composable
fun ContestInfo(
    titleOnTop: Boolean = false,
    title: String,
    value: String,
    width: Dp? = null,
    highlight:Boolean = false,
    entry_waived:Boolean = false,
){
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = if (width != null) Modifier.width(width) else Modifier
    ) {
        if (titleOnTop) {
            Box(
                modifier = Modifier
                    .height(12.dp)
                    .fillMaxWidth()
            ){
                Text(
                    text = title,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.outline,
                    style = MaterialTheme.typography.labelSmall,
                    modifier = Modifier.align(Alignment.Center),
                    overflow = TextOverflow.Visible,
                    softWrap = false
                )
            }
            Box(
                modifier = Modifier
                    .height(20.dp)
                    .fillMaxWidth()
            ){
                Row(horizontalArrangement = Arrangement.Center, verticalAlignment = Alignment.CenterVertically,modifier = Modifier.align(Alignment.Center)){
                    if(entry_waived){
                        Text(
                            text = "FREE",
                            textAlign = TextAlign.Center,
                            style = if(highlight) MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.Bold) else MaterialTheme.typography.bodyLarge,
                            overflow = TextOverflow.Visible,
                            softWrap = false,
                            color = Color(0xff128000)
                        )
                        Spacer(modifier = Modifier.width(2.dp))
                    }
                    Box{
                        Text(
                            text = value,
                            textAlign = TextAlign.Center,
                            style = if(highlight) MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.Bold) else MaterialTheme.typography.bodyLarge,
                            overflow = TextOverflow.Visible,
                            softWrap = false,
                            color = if(highlight) Color(0xff128000) else if(entry_waived) Color(0x33131433) else Color.Unspecified
                        )
                        if(entry_waived){
                            val textMeasurer = rememberTextMeasurer()
                            val textLayoutResult = textMeasurer.measure(
                                text = AnnotatedString(value),
                                style = MaterialTheme.typography.bodyLarge
                            )

                            val textWidth = with(LocalDensity.current) { textLayoutResult.size.width.toDp() }
                            val textHeight = with(LocalDensity.current) { textLayoutResult.size.height.toDp() }

                            Canvas(modifier = Modifier
                                .width(textWidth)
                                .height(textHeight)
                            ) {
                                val y = size.height / 2  // middle of the text
                                val lineHeight = size.height * 0.04f
                                drawRect(
                                    color = Color.Black,
                                    topLeft = Offset(0f, y - lineHeight / 2),
                                    size = Size(textLayoutResult.size.width.toFloat(), lineHeight)
                                )
                            }
                        }
                    }
                }
            }
        }
        else {
            Box(
                modifier = Modifier
                    .height(20.dp)
                    .fillMaxWidth()
            ){
                Row(horizontalArrangement = Arrangement.Center, verticalAlignment = Alignment.CenterVertically,modifier = Modifier.align(Alignment.Center)){
                    if(entry_waived){
                        Text(
                            text = "FREE",
                            textAlign = TextAlign.Center,
                            style = if(highlight) MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.Bold) else MaterialTheme.typography.bodyLarge,
                            overflow = TextOverflow.Visible,
                            softWrap = false,
                            color = Color(0xff128000)
                        )
                        Spacer(modifier = Modifier.width(2.dp))
                    }
                    Box{
                        Text(
                            text = value,
                            textAlign = TextAlign.Center,
                            style = if(highlight) MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.Bold) else MaterialTheme.typography.bodyLarge,
                            overflow = TextOverflow.Visible,
                            softWrap = false,
                            color = if(highlight) Color(0xff128000) else if(entry_waived) Color(0x33131433) else Color.Unspecified
                        )
                        if(entry_waived){
                            val textMeasurer = rememberTextMeasurer()
                            val textLayoutResult = textMeasurer.measure(
                                text = AnnotatedString(value),
                                style = MaterialTheme.typography.bodyLarge
                            )

                            val textWidth = with(LocalDensity.current) { textLayoutResult.size.width.toDp() }
                            val textHeight = with(LocalDensity.current) { textLayoutResult.size.height.toDp() }

                            Canvas(modifier = Modifier
                                .width(textWidth)
                                .height(textHeight)
                            ) {
                                val y = size.height / 2  // middle of the text
                                val lineHeight = size.height * 0.04f
                                drawRect(
                                    color = Color.Black,
                                    topLeft = Offset(0f, y - lineHeight / 2),
                                    size = Size(textLayoutResult.size.width.toFloat(), lineHeight)
                                )
                            }
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.height(4.dp))
            Box(
                modifier = Modifier
                    .height(12.dp)
                    .fillMaxWidth()
            ){
                Text(
                    text = title,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.outline,
                    style = MaterialTheme.typography.labelSmall,
                    modifier = Modifier.align(Alignment.Center),
                    overflow = TextOverflow.Visible,
                    softWrap = false
                )
            }
        }
    }
}

@Composable
fun ContestDetail(show: MutableState<Boolean>, data:APITournament?,wallet:APIWalletResponse, isGuest:Boolean, gameId:String?) {
    BottomSheetModal(
        title = "Contest Details",
        showBackButton = false,
        showDivider = true,
        expanded = show.value,
        closeAction = { show.value = false;
            AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Modal_Closed.value,
                properties = mapOf("name" to "Contest Detail Bottom Sheet"))
            )
        },
        contentBackgroundColor = MaterialTheme.colorScheme.background,
        footer = {
            if(data != null){
                Box(
                    modifier = Modifier
                        .navigationBarsPadding()
                        .fillMaxWidth()
                        .padding(24.dp, 8.dp)
                ){
                    ContestPlayButton(data,wallet,subTitle = if(data.is_free_entry == true) "FREE ENTRY" else data.entryFee.label, gameId = gameId)
                }
            }
        }
    ) {
        if(data == null){
            return@BottomSheetModal
        }
//        println("data ==== ${data}")
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp, Alignment.Top),
            modifier = Modifier.padding(24.dp, 12.dp)
        ) {
            ContentBox(
                titleComposable = { ContentTitle(title = "Summary") }
            ) {
                Column (
                    verticalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    val maximum_entries_per_player = data.maximum_entries_per_player ?: -1
                    Text(
                        text = if (isGuest) STR(R.string.in_guest_contests)
                                else if(data.maximum_slots != null )STR(R.string.these_contests_have)
                                else if(maximum_entries_per_player > 1)STR(R.string.unlimited_contests,maximum_entries_per_player)
                                else STR(R.string.unlimited_contests_one_entry),
                        style = MaterialTheme.typography.headlineMedium,
                    )

                    val rows: MutableList<@Composable ()->Unit> = mutableListOf(
                        {
                            Row(
                                modifier = Modifier
                                    .fillMaxSize(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "Game",
                                    style = MaterialTheme.typography.headlineMedium,
                                )
                                Text(
                                    text = data.game_display_name ?: (data.game_name ?: "Game222"),
                                    style = MaterialTheme.typography.headlineMedium,
                                )
                            }
                        },
                        {
                            Row(
                                modifier = Modifier
                                    .fillMaxSize(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = STR(R.string.players),
                                    style = MaterialTheme.typography.headlineMedium,
                                )
                                Text(
                                    text = data.maximum_slots?.toString() ?: "∞",
                                    style = MaterialTheme.typography.headlineMedium,
                                )
                            }
                        },
                        {

                            Row(
                                modifier = Modifier
                                    .fillMaxSize(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = STR(R.string.entry_fee),
                                    style = MaterialTheme.typography.headlineMedium,
                                )

                                Row(horizontalArrangement = Arrangement.Center, verticalAlignment = Alignment.CenterVertically,){
                                        val entry_waived = data.is_free_entry == true

                                        val value = if (data.entryFee.value > 0) data.entryFee.value.toDollarWithCommaString() else "FREE"
                                        if(entry_waived){
                                            Text(
                                                text = "FREE",
                                                textAlign = TextAlign.Center,
                                                style = MaterialTheme.typography.headlineMedium,
                                                overflow = TextOverflow.Visible,
                                                softWrap = false,
                                                color = Color(0xff128000)
                                            )
                                            Spacer(modifier = Modifier.width(2.dp))
                                        }
                                        Box(contentAlignment = Alignment.Center){
                                            Text(
                                                text = value,
                                                textAlign = TextAlign.Center,
                                                style = MaterialTheme.typography.headlineMedium,
                                                overflow = TextOverflow.Visible,
                                                softWrap = false,
                                                color = if(entry_waived) Color(0x33131433) else Color.Unspecified
                                            )
                                            if(entry_waived){
                                                val textMeasurer = rememberTextMeasurer()
                                                val textLayoutResult = textMeasurer.measure(
                                                    text = AnnotatedString(value),
                                                    style = MaterialTheme.typography.headlineMedium
                                                )

                                                val textWidth = with(LocalDensity.current) { textLayoutResult.size.width.toDp() }
                                                val textHeight = with(LocalDensity.current) { textLayoutResult.size.height.toDp() }

                                                Canvas(modifier = Modifier
                                                    .width(textWidth)
                                                    .height(textHeight)
                                                ) {
                                                    val y = size.height / 2  // middle of the text
                                                    val lineHeight = size.height * 0.04f
                                                    drawRect(
                                                        color = Color.Black,
                                                        topLeft = Offset(0f, y - lineHeight / 2),
                                                        size = Size(textLayoutResult.size.width.toFloat(), lineHeight)
                                                    )
                                                }
                                            }
                                        }
                                    }
                            }
                        }
                    )
//                    data.free_entry_ticket_expires_at = "2025-06-27T15:39:44Z"

                    if(data.free_entry_ticket_expires_at != null){
                        rows.add {
                            Row(
                                modifier = Modifier
                                    .fillMaxSize(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = STR(R.string.expires),
                                    style = MaterialTheme.typography.headlineMedium,
                                )
                                Text(
                                    text = (data.free_entry_ticket_expires_at?.toDate()
                                        ?.formatEST("MM/dd/yyyy hh:mm a") +  " ET"),
                                    style = MaterialTheme.typography.headlineMedium,
                                )
                            }
                        }
                    }

                    ContentListBody(
                        listElements = rows
                    )
                }
            }
            ContentBox(
                titleComposable = { ContentTitle(title = STR(R.string.prizes)) }
            ) {
                var totalPrize = 0f
                for (prize in data.prizes){
                    totalPrize += prize.prize_amount.toFloat() * (prize.end - prize.start + 1)
                }

                Column {
                    val rows: MutableList<@Composable ()->Unit> = mutableListOf(
                        {
                            Row(
                                modifier = Modifier
                                    .fillMaxSize(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = STR(R.string.total_prize_pool),
                                    style = MaterialTheme.typography.headlineLarge,
                                )
                                Text(
                                    text = totalPrize.toDollarWithCommaString(),
                                    style = MaterialTheme.typography.headlineLarge,
                                )
                            }
                        }
                    )
                    for (prize in data.prizes) {
                        rows.add {
                            Row(
                                modifier = Modifier
                                    .fillMaxSize(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Box(modifier = Modifier
                                    .height(32.dp)
                                    .width(if (prize.end >= 10) 64.dp else 32.dp)) {
                                    val medal: Int? = when(prize.start){
                                        1 -> R.drawable.ic_medal_gold
                                        2 -> R.drawable.ic_medal_silver
                                        3 -> R.drawable.ic_medal_bronze
                                        else -> null
                                    }

                                    if (medal != null){
                                        Image(
                                            modifier = Modifier.fillMaxSize(),
                                            painter = painterResource(medal),
                                            contentDescription = STR(R.string.prize_medal),
                                        )
                                    }

                                    val start = prize.start
                                    val end = prize.end
                                    val range = end - start
                                    Text(
                                        text = if (range == 0) start.toString() else "$start - $end",
                                        style = MaterialTheme.typography.headlineMedium,
                                        modifier = Modifier.align(if (prize.end >= 10) Alignment.CenterStart else Alignment.Center)
                                    )
                                }

                                Text(
                                    text = prize.prize_amount.toDollarWithCommaString(),
                                    style = MaterialTheme.typography.headlineMedium,
                                )
                            }
                        }
                    }

                    ContentListBody(listElements = rows)
                }
            }
        }
    }
}

@Composable
fun GamePreview(show: MutableState<Boolean>,selectedGameData: APIGamesResponseGame?,isFtue:Boolean, isGuest:Boolean){
    BottomSheetModal(
        title = if (selectedGameData != null) "${selectedGameData!!.display_name} Preview" else "Game Name Preview",
        titleStyle = MaterialTheme.typography.titleMedium.copy(fontSize = 18.sp),
        showBackButton = false,
        showDivider = false,
        expanded = show.value,
        closeAction = { show.value = false;
            AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Modal_Closed.value,
                properties = mapOf("name" to "Game Preview Bottom Sheet"))
            )
        },
        footer = {
            //Shadow
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(4.dp)
                    .offset(0.dp, (-4).dp)
                    .background(ShadowFade)
            )
            Row (
                modifier = Modifier
                    .navigationBarsPadding()
                    .padding(16.dp),
                horizontalArrangement = Arrangement
                    .spacedBy(16.dp, Alignment.CenterHorizontally)
            ){
                if (isFtue){
                    val tournamentsResponse by PlayViewModel.instance.contestData.collectAsState()
                    var buttonActive by remember { mutableStateOf(false) }
                    var allTournamentsForSelectedGame: List<APITournament> = listOf()

                    when (tournamentsResponse) {
                        is ResourceState.Error -> { buttonActive = false }
                        is ResourceState.Loading -> { buttonActive = false }
                        is ResourceState.Success -> {
                            val responseData = (tournamentsResponse as ResourceState.Success).data
                            allTournamentsForSelectedGame = responseData.results
                            buttonActive = true
                        }
                    }

                    CommonButton(
                        title = "Play a practice game",
                        fillColor = MaterialTheme.colorScheme.tertiary,
                        textColor = MaterialTheme.colorScheme.onTertiary,
                        enabled = buttonActive,
                        modifier = Modifier
                            .weight(1f)
                    ) {
                        // Start Ftue Practice Game
                        var practiceTournamentId = PlayViewModel.instance.practiceTournamentId
                        if(practiceTournamentId == null) {
                            if(allTournamentsForSelectedGame.size > 0){
                                practiceTournamentId = allTournamentsForSelectedGame[0].id
                            }
//                            for (tournament in allTournamentsForSelectedGame){
//                                if (tournament.brand?.lowercase()?.contains("guest") == true){
//                                    practiceTournamentId = tournament.id
//                                    break
//                                }
//                            }
                        }

                        val tournament = allTournamentsForSelectedGame.firstOrNull() {it.id == practiceTournamentId}
                        if(practiceTournamentId != null) {
                            PlayViewModel.instance.joinTournament(practiceTournamentId, selectedGameData?.id?:"", tournament = tournament)
                            show.value = false
                        } else {
                            AuthenticationManager.instance.showLobbyFtue = false
                            PlayViewModel.instance.logP1EscapeHatchEvent(selectedGameData?.id)
                            show.value = false
                        }
                    }
                }
                else {
                    CommonButton(
                        title = "More Details",
                        fillColor = MaterialTheme.colorScheme.surface,
                        textColor = MaterialTheme.colorScheme.primary,
                        borderColor = MaterialTheme.colorScheme.primary,
                        modifier = Modifier
                            .weight(1f)
                    ) {
                        if(selectedGameData?.more_info_url != null) {
                            FDManager.instance.showWebviewFD(
                                page = null,
                                sourceURL = selectedGameData?.more_info_url
                            )
                        }
                    }
                    CommonButton(
                        title = "Pick a Contest",
                        fillColor = MaterialTheme.colorScheme.tertiary,
                        textColor = MaterialTheme.colorScheme.onTertiary,
                        modifier = Modifier
                            .weight(1f)
                    ) {
                        show.value = false
                    }
                }
            }
        }
    ){
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp, Alignment.Top),
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.surface)
                .padding(horizontal = 16.dp)
        ) {
            Surface{
                VideoComponent(
                    videoUrl = selectedGameData?.preview ?: "",
                    aspectRatio = 0.8275f
                )
            }

            Text(
                text = selectedGameData?.display_name ?: "Game name",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.secondary
            )

            Text(
                text = selectedGameData?.long_description ?: "Game description",
                color = MaterialTheme.colorScheme.inverseOnSurface,
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}
