pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

rootProject.name = "app-android-fd"
include(":app")
include(":resources")
include(":uitest")
include(":unityLibrary")
include(":utilities")
project(":unityLibrary").projectDir = File(".\\game-android-container-fd\\Framework\\unityLibrary")

include(":unityLibrary:AndroidGameUtils.androidlib")

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        flatDir {
            dirs("${project(":unityLibrary").projectDir}\\libs")
        }
        maven {
            url = uri("https://fanduel.jfrog.io/artifactory/fd-maven/")
            credentials {
                username = System.getenv("FD_MAVEN_READ_USERNAME")
                password = System.getenv("FD_MAVEN_READ_TOKEN")
            }
        }
        maven { url = uri("https://perimeterx.jfrog.io/artifactory/px-Android-SDK/") }
        maven { url = uri("https://jitpack.io")}
        maven { url = uri("https://s3.amazonaws.com/salesforcesos.com/android/maven/release") }
    }
}

// Pre-build step to prevent Authority conflicts between the games app that uses the AndroidGameUtils.androidlib
gradle.beforeProject {
    if(name == "AndroidGameUtils.androidlib") {
        val selectedFlavor = gradle.startParameter.taskNames.find {
            it.contains("Games", ignoreCase = true) || it.contains("Gamesdev", ignoreCase = true)
        }?.lowercase() ?: "default"

        //This should match with the application ID
        val gameValue = when {
            "games" in selectedFlavor -> "com.fanduel.skillgames.games.qa.androidgameutils.fileprovider"
            "gamesdev" in selectedFlavor -> "com.fanduel.skillgames.games.dev.androidgameutils.fileprovider"
            else -> "com.fanduel.skillgames.androidgameutils.fileprovider"
        }

        rootProject.extensions.extraProperties["CUSTOM_GAMES_AUTH"] = gameValue
        println("Set in settings.gradle.kts: CUSTOM_GAMES_AUTH = $gameValue")
    }
}
