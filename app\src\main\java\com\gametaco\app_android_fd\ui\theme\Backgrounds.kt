package com.gametaco.app_android_fd.ui.theme

import android.util.DisplayMetrics
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import com.gametaco.app_android_fd.MainApplication.Companion.context

val displayMetrics: DisplayMetrics = context.resources.displayMetrics
val screenWidthPx = displayMetrics.widthPixels.toFloat()
val screenHeightPx = displayMetrics.heightPixels.toFloat()

//Brushes for various backgrounds
val BlueWelcomeBackground = Brush.linearGradient(
    colors = listOf(Color(0xFF00FFC0), Color(0xFF0070EB)),
    start = Offset(-.25f * screenWidthPx, -.25f * screenHeightPx),
    end = Offset(.5f * screenWidthPx, .5f * screenHeightPx)
)
val BlueMinimalBackground = Brush.linearGradient(
    colors = listOf(Color(0xFF00FFC0), Color(0xFF0070EB)),
    start = Offset(-.05f * screenWidthPx, -.15f * screenHeightPx),
    end = Offset(.1f * screenWidthPx, .2f * screenHeightPx)
)

val BlueMinimalBackgroundSubtle = Brush.linearGradient(
    colors = listOf(Color(0xFF01A0D4), Color(0xFF0070EB)),
    start = Offset(0f * screenWidthPx, 0f * screenHeightPx),
    end = Offset(.1f * screenWidthPx, .2f * screenHeightPx)
)

val BlueSingleBackground = Color(0xFF0070EB)

val OverlayBackground = Brush.radialGradient(
    colors = listOf(Color(0xaa333333), Color(0xaa333333)),
    center = Offset(-.2f, -.9f),
    radius = 700.0f
)

val GuestBlockBackground = Brush.linearGradient(
    colors = listOf(
        Color(0xD9EAF4FF),
        Color(0xFFEAF4FF),
        Color(0xFFEAF4FF),
        Color(0xD9EAF4FF),
    ),
    start = Offset(0f, 0f),
    end = Offset(0f, Float.POSITIVE_INFINITY)
)

val ShadowFade = Brush.verticalGradient(
    colors = listOf(Color(0xFFEAF0F6), Color(0x40000000)),
)

val GreenMinimalBackground = Brush.linearGradient(
    colors = listOf(Color(0xFF128000), Color(0xFF085C26)),
    start = Offset(-.05f * screenWidthPx, 0f),
    end = Offset(.5f * screenWidthPx, 0f)
)