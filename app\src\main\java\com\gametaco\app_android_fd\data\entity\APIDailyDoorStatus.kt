package com.gametaco.app_android_fd.data.entity

import com.gametaco.app_android_fd.utils.toDate
import java.util.Date

data class APIDailyDoorStatusRequest(
    val current_date:String?,
    val is_digest:Boolean
)
data class APIDailyDoorStatus(
    val current_streak:Int,
    val next_reward_at:String,
    val streak_expires_at:String,
    val claimable_day_id:String?,
){
    val nextRewardDate:Date?
        get() = next_reward_at.toDate()
    val streakExpiresDate:Date?
        get() = streak_expires_at.toDate()
    val isRewardAvailable:Boolean
        get() = claimable_day_id != null
}

data class APIDailyDoorCollectedRewards(
    val results:List<APIDailyDoorCollectedReward>,
    val count:Int
)

data class APIDailyDoorCollectedReward(
    val id:String,
    val collected_at:String,
    val name:String,
    val reward:APIDailyRewardInfo,
)

enum class DailyRewardInfoType(value:String){
    CASH("CASH"),
    FREE_GAME_ENTRY("FREE_GAME_ENTRY")
}

data class APIDailyRewardInfo(
    val type:DailyRewardInfoType,
    val value:String? = null,
    val cash_value:APIDailyRewardInfoCashValue?,
    val tournament_value:APIDailyRewardTournament? = null
)

data class APIDailyRewardInfoCashValue(
    val reward_type:String,//APIGoalRewardType,
    val amount:String
)
data class APIDailyRewardTournament(
    val id:String,
    val game_id:String,
    val game_name:String,
    val game_display_name:String,
    val game_mode_name:String,
    val entry_fee:String,
    val prizes:String,
)