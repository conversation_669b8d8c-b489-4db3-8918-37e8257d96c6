package com.gametaco.app_android_fd.ui.screens

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider

@Composable
fun PreviewRoot(content: @Composable () -> Unit) {
//    val navController = rememberNavController()

    CompositionLocalProvider(
        // Previews don't actually work ATM due to global instances not being configured, so
        // ignore this for now.
//        LocalNavController provides navController
    ) {
        content()
    }
}