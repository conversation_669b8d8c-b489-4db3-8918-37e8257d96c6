package com.gametaco.app_android_fd.ui.screens

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import com.gametaco.app_android_fd.LocalNavManager
import com.gametaco.app_android_fd.data.navigation.NavigationData
import com.gametaco.app_android_fd.ui.components.NavBar
import com.gametaco.app_android_fd.ui.components.UnityActivityLauncher
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.viewmodel.GamesViewModel
import com.gametaco.utilities.ResourceState

const val TAG_GAMES_SCREEN = "GamesScreen"

@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter")
@Composable
fun GamesScreen(gamesViewModel: GamesViewModel){
    val TAG = TAG_GAMES_SCREEN
    val gamesResponse by gamesViewModel.gameCatalog.collectAsState()
    val navManager = LocalNavManager.current

    Scaffold (
        bottomBar = { NavBar(navData = NavigationData.instance, navManager) }
    ){ paddingValues ->
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ){
            UnityActivityLauncher()

            when(gamesResponse){
                is ResourceState.Loading -> {
                    Logger.d(TAG, "Inside Loading")
                    LoadingScreen()
                }

                is ResourceState.Success -> {
                    Logger.d(TAG, "Inside Success")

//                val moshi = Moshi.Builder()
//                    .addLast(KotlinJsonAdapterFactory())
//                    .build()
//
//                val jsonAdapter: JsonAdapter<GameCatalogResponse> = moshi.adapter(GameCatalogResponse::class.java)
//
//                Logger.d(TAG, "dumping response as json: ${jsonAdapter.toJson((gamesResponse as ResourceState.Success<GameCatalogResponse>).data)}")

//                    val response = (gamesResponse as ResourceState.Success).data
                }

                is ResourceState.Error-> {
                    Logger.d(TAG, "Inside Error")
                }
            }
        }
    }
}