package com.gametaco.app_android_fd.ui.components
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import resources.R

enum class ArrowMode {
    Top,
    Bottom,
}



@Composable
fun Tooltip(
    modifier: Modifier = Modifier,
    enabled:Boolean = true,
    arrowMode: ArrowMode = ArrowMode.Top,
    offsetX: Int = 0,
    offsetY: Int = 0,
    arrowHeight:Dp = 5.dp,
    arrowPositionNormalised: Float = 0.5f,
    content: @Composable () -> Unit
) {
    if(!enabled){
        return
    }
    val arrowHeightPx = with(LocalDensity.current) {
        arrowHeight.toPx()
    }

    Box(
        modifier = modifier
            .offset(offsetX.dp, offsetY.dp)
            .background(
                color = Color.White,
                shape = RoundedCornerShape(5.dp)
            )
            .drawBehind {
                val path = Path()
                val width = drawContext.size.width

                when (arrowMode){
                    ArrowMode.Top -> {
                        val position = Offset(x = width * arrowPositionNormalised, y = 0f)
                        path.apply {
                            moveTo(x = position.x, y = position.y)
                            lineTo(x = position.x - arrowHeightPx, y = position.y)
                            lineTo(x = position.x, y = position.y - arrowHeightPx)
                            lineTo(x = position.x + arrowHeightPx, y = position.y)
                            lineTo(x = position.x, y = position.y)
                        }
                    }
                    ArrowMode.Bottom -> {
                        val arrowY = drawContext.size.height
                        val position = Offset(x = width * arrowPositionNormalised, y = arrowY)
                        path.apply {
                            moveTo(x = position.x, y = position.y)
                            lineTo(x = position.x + arrowHeightPx, y = position.y)
                            lineTo(x = position.x, y = position.y + arrowHeightPx)
                            lineTo(x = position.x - arrowHeightPx, y = position.y)
                            lineTo(x = position.x, y = position.y)
                        }
                    }
                }

                drawPath(
                    path = path,
                    color = Color.White,
                )
                path.close()
            }
    ) {
        Box(modifier = Modifier.padding(8.dp)){
            content()
        }
    }
}

@Composable
fun TooltipWithBorder(
    borderWidth: Dp,
    gapWidth: Dp,
    arrowPositionXNormalized: Float,
    arrowMode: ArrowMode,
    padding: Dp,
    cornerRadius: Dp,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .wrapContentSize()
            .customBorderWithGap(borderWidth, Color.Gray, gapWidth, arrowPositionXNormalized, arrowMode, cornerRadius)
            .padding(padding)
    ) {
        content()
    }
}

fun Modifier.customBorderWithGap(
    borderWidth: Dp,
    borderColor: Color,
    gapWidth: Dp,
    gapPositionXNormalized: Float,
    arrowMode: ArrowMode,
    cornerRadius: Dp,
): Modifier {
    when (arrowMode) {
        ArrowMode.Bottom -> {
            return customBorderWithGapBottom(
                borderWidth = borderWidth,
                borderColor = borderColor,
                gapWidth = gapWidth,
                gapPositionXNormalized = gapPositionXNormalized,
                cornerRadius = cornerRadius
            )
        }
        ArrowMode.Top -> {
            return customBorderWithGapTop(
                borderWidth = borderWidth,
                borderColor = borderColor,
                gapWidth = gapWidth,
                gapPositionXNormalized = gapPositionXNormalized,
                cornerRadius = cornerRadius
            )
        }
    }
}

fun Modifier.customBorderWithGapTop(
    borderWidth: Dp,
    borderColor: Color,
    gapWidth: Dp,
    gapPositionXNormalized: Float,
    cornerRadius: Dp,
): Modifier = this.then(
    Modifier.drawWithContent {
        val cornerRadiusPx = cornerRadius.toPx()
        val strokeWidthPx = borderWidth.toPx()
        val gapWidthPx = gapWidth.toPx()
        val canvasWidth = size.width
        val canvasHeight = size.height

        // Calculate the position of the gap based on normalized value
        val gapPositionXPx = (canvasWidth - gapWidthPx) * gapPositionXNormalized

        // Draw rounded background
        drawRoundRect(
            color = Color.White,
            size = size,
            cornerRadius = CornerRadius(cornerRadiusPx, cornerRadiusPx),
            style = Fill
        )

        // Draw rounded border
        drawRoundRect(
            color = borderColor,
            size = size,
            cornerRadius = CornerRadius(cornerRadiusPx, cornerRadiusPx),
            style = Stroke(strokeWidthPx)
        )

        // Draw the white triangle at the top
        val trianglePath = Path().apply {
            moveTo(gapPositionXPx, strokeWidthPx / 2)
            lineTo(gapPositionXPx + gapWidthPx / 2, -gapWidthPx / 2)
            lineTo(gapPositionXPx + gapWidthPx, strokeWidthPx / 2)
            close()
        }

        drawPath(
            path = trianglePath,
            color = Color.White
        )

        // Draw the gray border for the triangle at the top
        drawLine(
            color = Color.Gray,
            start = Offset(gapPositionXPx, strokeWidthPx / 2),
            end = Offset(gapPositionXPx + gapWidthPx / 2, -gapWidthPx / 2),
            strokeWidth = strokeWidthPx
        )
        drawLine(
            color = Color.Gray,
            start = Offset(gapPositionXPx + gapWidthPx / 2, -gapWidthPx / 2),
            end = Offset(gapPositionXPx + gapWidthPx, strokeWidthPx / 2),
            strokeWidth = strokeWidthPx
        )

        drawContent()
    }
)

fun Modifier.customBorderWithGapBottom(
    borderWidth: Dp,
    borderColor: Color,
    gapWidth: Dp,
    gapPositionXNormalized: Float,
    cornerRadius: Dp,
): Modifier = this.then(
    Modifier.drawWithContent {
        val cornerRadiusPx = cornerRadius.toPx()
        val strokeWidthPx = borderWidth.toPx()
        val gapWidthPx = gapWidth.toPx()
        val canvasWidth = size.width
        val canvasHeight = size.height

        // Calculate the position of the gap based on normalized value
        val gapPositionXPx = (canvasWidth - gapWidthPx) * gapPositionXNormalized

        // Draw rounded background
        drawRoundRect(
            color = Color.White,
            size = size,
            cornerRadius = CornerRadius(cornerRadiusPx, cornerRadiusPx),
            style = Fill
        )

        // Draw rounded border
        drawRoundRect(
            color = borderColor,
            size = size,
            cornerRadius = CornerRadius(cornerRadiusPx, cornerRadiusPx),
            style = Stroke(strokeWidthPx)
        )

        // Draw the white triangle
        val trianglePath = Path().apply {
            moveTo(gapPositionXPx, canvasHeight - strokeWidthPx / 2)
            lineTo(gapPositionXPx + gapWidthPx / 2, canvasHeight + gapWidthPx / 2)
            lineTo(gapPositionXPx + gapWidthPx, canvasHeight - strokeWidthPx / 2)
            close()
        }

        drawPath(
            path = trianglePath,
            color = Color.White
        )

        // Draw the gray border for the triangle
        drawLine(
            color = Color.Gray,
            start = Offset(gapPositionXPx, canvasHeight - strokeWidthPx / 2),
            end = Offset(gapPositionXPx + gapWidthPx / 2, canvasHeight + gapWidthPx / 2),
            strokeWidth = strokeWidthPx
        )
        drawLine(
            color = Color.Gray,
            start = Offset(gapPositionXPx + gapWidthPx / 2, canvasHeight + gapWidthPx / 2),
            end = Offset(gapPositionXPx + gapWidthPx, canvasHeight - strokeWidthPx / 2),
            strokeWidth = strokeWidthPx
        )

        drawContent()
    }
)

@Preview
@Composable
fun TooltipPreview(){
    Box(
       modifier = Modifier
           .fillMaxSize()
           .background(color = Color.Gray)
    ){
        Column {
            Box{
                Image(painter = painterResource(id = R.drawable.ic_geo_location), contentDescription = null)

                Tooltip(enabled = true,
                    offsetX = 100, offsetY = 140, arrowPositionNormalised = 0.8f, arrowMode = ArrowMode.Top) {
                    Column {
                        Image(painter = painterResource(id = R.drawable.ic_fanduel_account), contentDescription = "issso")
                        Text("This is a tooltip\nwhich you can customise content",color = Color.Red)
                    }
                }

                Box(
                    modifier = Modifier
                        .fillMaxSize() // Make the parent Box fill the screen
                        .padding(end = 32.dp), // Add padding to the right side
                    contentAlignment = Alignment.TopEnd // Align content to the top end (right side)
                ) {
                    TooltipWithBorder(
                        borderWidth = 1.dp,
                        gapWidth = 20.dp,
                        arrowPositionXNormalized = 0.75f,
                        arrowMode = ArrowMode.Bottom,
                        padding = 16.dp,
                        cornerRadius = 8.dp
                    ) {
                        // Your content here
                        Column(
                            verticalArrangement = Arrangement.spacedBy(12.dp),
                            modifier = Modifier
                                .width(216.dp)
                                .defaultMinSize(minHeight = 60.dp)
                        ) {
                            Text(
                                text = "Nice Work!",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.secondary
                            )
                            Text(
                                text = "You played your first game. " +
                                        "Ready to play against another player?",
                                style = MaterialTheme.typography.headlineMedium,
                                color = MaterialTheme.colorScheme.onSurface,
                                softWrap = true
                            )
                        }
                    }
                }

                TooltipWithBorder(
                    borderWidth = 2.dp,
                    gapWidth = 20.dp,
                    arrowPositionXNormalized = 0.75f,
                    arrowMode = ArrowMode.Bottom,
                    padding = 16.dp,
                    cornerRadius = 8.dp
                ) {
                    // Your content here
                    Column {
                        Image(painter = painterResource(id = R.drawable.ic_fanduel_account), contentDescription = "issso")
                        Text("This is a tooltip\nwhich you can customise content",color = Color.Red)
                    }
                }

                Tooltip(
                    modifier = Modifier
                        .width(316.dp)
                        .defaultMinSize(minHeight = 80.dp)
                        .align(Alignment.BottomEnd),
                    offsetX = 24,
                    offsetY = 300,
                    arrowHeight = 18.dp,
                    enabled = true,
                    arrowMode = ArrowMode.Bottom,
                    arrowPositionNormalised = 0.75f
                ) {
                    Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
                        Text(
                            text = "Nice Work!",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.secondary
                        )
                        Text(
                            text = "You played your first game. " +
                                    "Ready to play against another player?",
                            style = MaterialTheme.typography.headlineMedium,
                            color = MaterialTheme.colorScheme.onSurface,
                            softWrap = true
                        )
                    }
                }

            }
        }
    }
}