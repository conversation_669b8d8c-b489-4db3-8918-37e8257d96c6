package com.gametaco.app_android_fd.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.gametaco.app_android_fd.ui.theme.BlueMinimalBackground
import resources.R

@Composable
fun TopBar (title: String, onBack: (()->Unit)? = null){
    Box(
        modifier = Modifier
            .background(MaterialTheme.colorScheme.surface)
            .statusBarsPadding()
            .fillMaxWidth()
            .height(48.dp),
        contentAlignment = Alignment.Center
    ){
        if (onBack != null){
            BackButton(Modifier
                .align(alignment = Alignment.CenterStart), onBack = onBack)
        }
        Text(
            text = title,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 48.dp),
        )
    }
}

@Composable
fun TopBarLogo(){
    Box(
        modifier = Modifier
            .background(BlueMinimalBackground)
            .statusBarsPadding()
            .fillMaxWidth()
            .height(72.dp)
            .padding(24.dp, 0.dp)
            .padding(top = 12.dp, bottom = 8.dp)
    ){
        Image(
            modifier = Modifier
                .size(96.dp, 28.dp)
                .align(Alignment.CenterStart),
            painter = painterResource(R.drawable.ic_fanduel_top),
            contentDescription = null,
            contentScale = ContentScale.Fit,
            alignment = Alignment.TopEnd
        )
    }
}