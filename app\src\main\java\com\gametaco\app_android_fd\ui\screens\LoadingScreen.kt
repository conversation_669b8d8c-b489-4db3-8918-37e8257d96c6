package com.gametaco.app_android_fd.ui.screens

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.gametaco.app_android_fd.ui.theme.AppandroidwwTheme

@Composable
fun LoadingScreen(backgroundColor: Color = MaterialTheme.colorScheme.background,show:Boolean = true) {
    if(!show){
        return
    }
    val delay = 0
    val duration = 2.0f
    val loadingCircles = 3
    val infiniteAlpha = rememberInfiniteTransition()
    val baseCircleAlpha by infiniteAlpha.animateFloat(
        initialValue = 0.0f,
        targetValue = 2 + (loadingCircles -1) * duration/loadingCircles,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = duration.toInt() * 1000, easing = LinearEasing, delayMillis = delay),
            repeatMode = RepeatMode.Restart
        ), label = ""
    )

    Box(
        modifier= Modifier
            .fillMaxSize()
            .background(backgroundColor),
        contentAlignment = Alignment.Center
    )
    {
        Box(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.secondary, RoundedCornerShape(8.dp))
                .size(100.dp, 70.dp)
        )
        {
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(8.dp)
            )
            {

                Row(
                    modifier = Modifier
                        .weight(1.0f),
                    horizontalArrangement = Arrangement.spacedBy(10.dp),
                    verticalAlignment = Alignment.CenterVertically

                )
                {
                    for(i in 0..< loadingCircles)
                    {
                        var circleAlpha = (baseCircleAlpha - i * duration/loadingCircles)
                        circleAlpha =
                            if(circleAlpha < 0) 0.0f
                            else if(circleAlpha in 0.0..1.0) circleAlpha
                            else if(circleAlpha in 1.0..2.0) 2 - circleAlpha //Translates 1.0->2.0 to 1.0->0.0
                            else 0.0f
                        Box(
                            modifier = Modifier
                                .size(8.dp)
                                .background(
                                    MaterialTheme.colorScheme.onPrimary.copy(alpha = circleAlpha),
                                    RoundedCornerShape(4.dp),
                                )
                        )
                    }
                }

                Text(
                    modifier = Modifier
                        .weight(1.0f),
                    text = "Loading...",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimary,
                )
            }
        }
    }
}



@Preview
@Composable
fun LoadingScreenPreview(){
    PreviewRoot {
        AppandroidwwTheme {
            LoadingScreen()
        }
    }
}