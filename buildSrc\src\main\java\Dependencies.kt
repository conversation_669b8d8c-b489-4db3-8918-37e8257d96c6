import org.gradle.kotlin.dsl.provideDelegate

//import java.io.File

object Dependencies {
    val coreKtx by lazy { "androidx.core:core-ktx:${Versions.coreKtx}" }
    val eventBus by lazy { "org.greenrobot:eventbus:3.2.0" }
    val appcompat by lazy { "androidx.appcompat:appcompat:${Versions.appcompat}" }
    val amplitude by lazy { "com.amplitude:android-sdk:${Versions.amplitude}"}
    val amplitude_experimentation by lazy { "com.amplitude:experiment-android-client:${Versions.amplitude_experimentation}"}

    val appsFlyer by lazy { "com.appsflyer:af-android-sdk:${Versions.appsFlyer}"}
    val appsFlyerLvl by lazy { "com.appsflyer:lvl:${Versions.appsFlyerLvl}"}
    val trustly by lazy { "net.trustly:trustly-android-sdk:${Versions.trustly}"}
    val material by lazy { "com.google.android.material:material:${Versions.material}" }
    val lifecycleRuntimeKtx by lazy { "androidx.lifecycle:lifecycle-runtime-ktx:${Versions.lifecycleRuntimeKtx}" }
    val activityCompose by lazy { "androidx.activity:activity-compose:${Versions.activityCompose}" }
    val composeBom by lazy { "androidx.compose:compose-bom:${Versions.compose}" }
    val composeUi by lazy { "androidx.compose.ui:ui" }
    val composeUiGraphics by lazy { "androidx.compose.ui:ui-graphics" }
    val composeUiToolingPreview by lazy { "androidx.compose.ui:ui-tooling-preview" }
    val composeMaterial3 by lazy { "androidx.compose.material3:material3" }
    val navigationCompose by lazy { "androidx.navigation:navigation-compose:${Versions.navigation}" }
    val navigationRuntime by lazy { "androidx.navigation:navigation-runtime:${Versions.navigation}" }
    val composeTestJUnit4 by lazy { "androidx.compose.ui:ui-test-junit4:${Versions.compose}" }
    val composeUiTooling by lazy { "androidx.compose.ui:ui-tooling::${Versions.compose}" }
    val composeUiTestManifest by lazy { "androidx.compose.ui:ui-test-manifest:${Versions.compose}" }
    val lifecycleViewModelKtx by lazy { "androidx.lifecycle:lifecycle-viewmodel-ktx:${Versions.lifecycleViewModelKtx}" }
    val koinCore by lazy { "io.insert-koin:koin-core:${Versions.koin}" }
    val koinAndroid by lazy { "io.insert-koin:koin-android:${Versions.koin}" }
    val kotlinTest by lazy { "org.jetbrains.kotlin:kotlin-test:${Versions.kotlin}" }
    val kotlinxSerializationJson by lazy { "org.jetbrains.kotlinx:kotlinx-serialization-json:${Versions.kotlinSerializationJson}" }
    val jUnit by lazy {"junit:junit:${Versions.jUnit}"}
    val timber by lazy {"com.jakewharton.timber:timber:${Versions.timber}"}

    val composeMaterial3Icons by lazy {"androidx.compose.material:material-icons-extended-android:${Versions.composeMaterial3Icons}"}

    val appium by lazy { "io.appium:java-client:${Versions.appium}" }
    val logback by lazy { "ch.qos.logback:logback-classic:${Versions.logback}" }
    val moshi by lazy {"com.squareup.moshi:moshi-kotlin:${Versions.moshi}" }
    val okio by lazy { "com.squareup.okio:okio:${Versions.okio}" }
    val okhttp by lazy {"com.squareup.okhttp3:okhttp:${Versions.okhttp}" }
    val okhttpLoggingInterceptor by lazy {"com.squareup.okhttp3:logging-interceptor:${Versions.okhttp}" }
    val retrofit by lazy {"com.squareup.retrofit2:retrofit:${Versions.retrofit}" }
    val retrofitAdapterRxjava2 by lazy {"com.squareup.retrofit2:adapter-rxjava2:${Versions.retrofit}" }
    val retrofitConverterGson by lazy {"com.squareup.retrofit2:converter-gson:${Versions.retrofit}" }
    val retrofitConverterMoshi by lazy {"com.squareup.retrofit2:converter-moshi:${Versions.retrofit}" }
    val selenium by lazy { "org.seleniumhq.selenium:selenium-java:${Versions.selenium}" }
    val slf4j by lazy { "org.slf4j:slf4j-api:${Versions.slf4j}" }

    val coroutinesAndroid by lazy {"org.jetbrains.kotlinx:kotlinx-coroutines-android:${Versions.coroutines}" }
    val coroutinesCore by lazy { "org.jetbrains.kotlinx:kotlinx-coroutines-core:${Versions.coroutines}" }
    val coroutinesTest by lazy { "org.jetbrains.kotlinx:kotlinx-coroutines-test:${Versions.coroutines}" }

    val exoPlayer by lazy {"androidx.media3:media3-exoplayer:${Versions.exoPlayer}"}

    val googlePlayServicesBase by lazy { "com.google.android.gms:play-services-base:${Versions.googlePlayServicesBase}"}
    val playServicesLocation by lazy {"com.google.android.gms:play-services-location:${Versions.playServicesLocation}"}
    val googlePlayServicesAdsIdentifier by lazy { "com.google.android.gms:play-services-ads-identifier:${Versions.googlePlayServicesAdsIdentifier}"}
    val playIntegrity by lazy {"com.google.android.play:integrity:${Versions.playIntegrity}"}

    val coilCompose by lazy {"io.coil-kt:coil-compose:${Versions.coilCompose}"}

    val brazeBase by lazy {"com.braze:android-sdk-base:${Versions.braze}"}
//    val brazeUI by lazy {"com.braze:android-sdk-ui:${Versions.braze}"}
    val brazeCompose by lazy {"com.braze:android-sdk-jetpack-compose:${Versions.braze}"}
    val sift by lazy {"com.siftscience:sift-android:${Versions.sift}"}
    val lottie by lazy {"com.airbnb.android:lottie-compose:${Versions.lottie}"}
    val perimeterX by lazy {"com.perimeterx.sdk:msdk:${Versions.perimeterX}"}

    val fd_accounthub by lazy {"com.fanduel.libs:account-hub:${Versions.fd_accounthub}"}
    val fd_accounthub_contract by lazy {"com.fanduel.libs:account-hub-contract:${Versions.fd_accounthub_contract}"}
    val fd_corewebview by lazy {"com.fanduel.coremodules:webview:${Versions.fd_corewebview}"}
    val fd_coreconfig by lazy {"com.fanduel.coremodules:config:${Versions.fd_coreconfig}"}
    val fd_ioc by lazy {"com.fanduel.coremodules:ioc:${Versions.fd_ioc}"}


    val fd_corepx by lazy {"com.fanduel.coremodules:px:${Versions.fd_corepx}"}
    val fd_coredeeplinks by lazy {"com.fanduel.coremodules:deeplinks:${Versions.fd_coredeeplinks}"}
    val fd_coreevents by lazy {"com.fanduel.coremodules:core-events:${Versions.fd_coreevents}"}
    val fd_promolinks by lazy {"com.fanduel.libs:promolinks:${Versions.fd_promolinks}"}
    val fd_amplitude_um by lazy {"com.fanduel.libs:amplitude-um:${Versions.fd_amplitude_um}"}
    val fd_wallet by lazy {"com.fanduel.core.libs:wallet:${Versions.fd_wallet}"}
    val fd_account by lazy {"com.fanduel.core.libs:account:${Versions.fd_account}"}
    val fd_account_contract by lazy {"com.fanduel.core.libs:account-contract:${Versions.fd_account_contract}"}
    val fd_account_verification_contract by lazy {"com.fanduel.core.libs:account-verification-contract:${Versions.fd_account_verification_contract}"}
    val fd_responsible_gaming by lazy {"com.fanduel.libs:responsible-gaming:${Versions.fd_responsible_gaming}"}


    val fd_account_mfa by lazy {"com.fanduel.core.libs:account-mfa:${Versions.fd_account_mfa}"}
    val fd_commonmodules by lazy {"com.fanduel.core.libs:common-modules:${Versions.fd_commonmodules}"}

    val fd_coremodalpresenter by lazy {"com.fanduel.core.libs:modal-presenter:${Versions.fd_coremodalpresenter}"}

    val fd_coreapiidentities by lazy {"com.fanduel.core.libs:api-identities:${Versions.fd_coreapiidentities}"}

    val fd_salesforce_library by lazy {"com.fanduel.libs:salesforce-library:${Versions.fd_salesforce_library}"}
    val fd_salesforce_contract by lazy {"com.fanduel.libs:salesforce-library-contract:${Versions.fd_salesforce_contract}"}

    val salesforce_service_chat_ui by lazy{"com.salesforce.service:chat-ui:${Versions.salesforce_service_chat_ui}"}
}

object Modules {
    const val utilities = ":utilities"
    const val resources = ":resources"
}
