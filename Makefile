.PHONY: check-jdk check-permissions test buildDebug lint buildDebug buildRelease clean install-platformx install-unity

export SENTRY_AUTH_TOKEN := 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

lint:
	./gradlew lint
	
	
check-android: check-jdk
	@echo "Checking if Android SDK is installed..."
	if brew list android-commandlinetools &>/dev/null; then \
		echo "Android SDK is installed"; \
	else \
		echo "Android SDK is not installed"; \
		brew install gradle; \
		brew install --cask android-commandlinetools; \
		sdkmanager --install "platform-tools" "platforms;android-34" "build-tools;34.0.0" "emulator" "ndk;23.1.7779620"; \
		yes | sdkmanager --licenses; \
	fi

check-jdk:
	@echo "Checking if JDK 17 is installed..."
	if brew list openjdk@17 &>/dev/null; then \
		echo "JDK 17 is installed"; \
	else \
		echo "JDK 17 is not installed"; \
		brew install openjdk@17; \
	fi

check-aws:
	@echo "Checking AWS CLI"
	if brew list awscli &>/dev/null; then \
		echo "AWS CLI is installed"; \
	else \
		echo "AWS CLI is not installed"; \
		brew install awscli; \
		chmod +x awsconfig.sh; \
		chmod +x signanduploadtos3.sh; \
		./awsconfig.sh; \
	fi
	
install-deps: check-android check-permissions
	@echo "Installing dependencies"
	
build-checks: check-jdk check-aws check-permissions
	@echo "Running build checks..."; \
	touch local.properties; \
	chmod +x uploadtos3.sh; \
	
check-permissions:
	chmod +x gradlew

build-debug: build-checks clean
	./gradlew buildSrc:assemble app:assembleWithUnityDevelopmentDefaultDebug

build-release: build-checks clean
	./gradlew buildSrc:assemble app:assembleWithUnityDevelopmentDefaultRelease

build-debug-nounity: build-checks clean
	./gradlew buildSrc:assemble app:assembleNoUnityDevelopmentDefaultDebug

build-release-nounity: build-checks clean
	./gradlew buildSrc:assemble app:assembleNoUnityDevelopmentDefaultRelease

build-production-release: build-checks clean
	./gradlew buildSrc:assemble app:assembleWithUnityProductionDefaultRelease

build-production-debug: build-checks clean
	./gradlew buildSrc:assemble app:assembleWithUnityProductionDefaultDebug

build-games-qa: build-checks clean
	./gradlew buildSrc:assemble app:assembleWithUnityDevelopmentGamesDebug

build-games-dev: build-checks clean
	./gradlew buildSrc:assemble app:assembleWithUnityDevelopmentGamesdevDebug

pr-check: check-jdk check-permissions lint
	@echo "Running PR checks..."

install-platformx: check-jdk check-permissions
	@echo "Installing PlatformX"

install-unity: check-jdk check-permissions
	@echo "Installing Unity"

clean: check-jdk check-permissions
	@echo "Cleaning..."; \
	./gradlew clean


test:
	@echo "Test"
