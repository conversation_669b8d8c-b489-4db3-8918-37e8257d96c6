import com.gametaco.app_android_fd.data.entity.API400ErrorCode

// TournamentEntryState.kt
// Copyright © 2024 Game Taco, Inc.

// TournamentEntryState

sealed class TournamentEntryState {

    object JoinRequested : TournamentEntryState()
    object JoinSuccess : TournamentEntryState()
    data class JoinFailure(val message: String, val code: API400ErrorCode, val resourceStateCode : Int) : TournamentEntryState()

    object LoadGameRequested : TournamentEntryState()
    data class LoadGameInProgress(val progress: Float) : TournamentEntryState()
    object LoadGameSuccess : TournamentEntryState()
    object LoadGameFailure : TournamentEntryState()

    object TournamentStartRequested : TournamentEntryState()
    object TournamentStartSuccess : TournamentEntryState()
    data class TournamentStartFailure(val o: String, val message: String) : TournamentEntryState()

    object TournamentEntryComplete : TournamentEntryState()

    // Utility properties to mimic <PERSON>'s computed properties
    val str: String
        get() = this.javaClass.simpleName

    val isFailureState: Boolean
        get() = when (this) {
            is JoinFailure, is LoadGameFailure, is TournamentStartFailure -> true
            else -> false
        }

    val isRequestState: Boolean
        get() = when (this) {
            is JoinRequested, is LoadGameRequested, is TournamentStartRequested -> true
            else -> false
        }

    val isSuccessState: Boolean
        get() = when (this) {
            is JoinSuccess, is LoadGameSuccess, is TournamentStartSuccess -> true
            else -> false
        }

    companion object {
        fun equals(lhs: TournamentEntryState, rhs: TournamentEntryState): Boolean {
            return when {
                lhs is LoadGameInProgress && rhs is LoadGameInProgress -> lhs.progress == rhs.progress
                lhs is TournamentStartFailure && rhs is TournamentStartFailure -> lhs.o == rhs.o && lhs.message == rhs.message
                else -> lhs.str == rhs.str
            }
        }
    }
}
