package com.gametaco.app_android_fd

//import com.gametaco.app_android_fd.manager.PerimeterxManager
import AppsFlyerManager
import android.app.Application
import android.content.Context
import com.gametaco.app_android_fd.di.appModule
import com.gametaco.app_android_fd.manager.ActivityManager
import com.gametaco.app_android_fd.manager.BrazeManager
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.manager.SiftManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.utils.log.DiskLoggingTree
import org.koin.android.ext.android.inject
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.Koin
import org.koin.core.context.startKoin
import timber.log.Timber

@Suppress("DEPRECATION", "OVERRIDE_DEPRECATION")
class MainApplication : Application() {

    companion object {
        const val TAG = "MainApplication"
        lateinit var instance: MainApplication
            private set

        val context: Context
            get() = instance.applicationContext

        val koin: Koin
            get() = instance.koin
    }

    lateinit var koin: Koin

    private val brazeManager: BrazeManager by inject()
    private val siftManager: SiftManager by inject()
    private val analyticsManager: AnalyticsManager by inject()
    private val appsFlyerManager: AppsFlyerManager by inject()
    private val experimentManager: ExperimentManager by inject()
    private val activityManager: ActivityManager by inject()

    override fun onCreate() {
        super.onCreate()
        instance = this

        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree(), DiskLoggingTree(this))
        }

        startKoin {
            androidLogger()
            androidContext(this@MainApplication)
            modules(appModule)
            <EMAIL> = this.koin
        }

        activityManager.initialize(this)
        brazeManager.initBraze()
        siftManager.initSift()
        analyticsManager.initAnalytics()
        experimentManager.initialize()
        appsFlyerManager.initialise()

//        PerimeterxManager.start(this)
    }
}
