package com.gametaco.app_android_fd.models

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable

@Immutable
data class DialogData(
    val title: String? = null,
    val message: String? = null,
    val confirmButtonText: String? = null,
    val onConfirm: (() -> Unit)? = null,
    val cancelButtonText: String? = null,
    val onCancel: (() -> Unit)? = null,
    val composeFunction: (@Composable () -> Unit)? = null,
)

data class ModalPopupData(
    val composeFunction: @Composable (modalData: ModalPopupData) -> Unit,
    val onConfirm: () -> Unit,
    val onCancel: (() -> Unit)? = null,
    val onDismiss: (() -> Unit)? = null
)