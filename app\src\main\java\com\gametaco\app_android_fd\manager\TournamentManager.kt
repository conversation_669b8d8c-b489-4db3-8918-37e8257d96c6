package com.gametaco.app_android_fd.manager
import AppsFlyerManager
import TournamentEntryState
import android.content.Context
import android.os.Handler
import android.os.Looper
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import com.gametaco.app_android_fd.BuildConfig
import com.gametaco.app_android_fd.data.AppConstants
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.data.api.APIGuestMeMessage
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.API400ErrorCode
import com.gametaco.app_android_fd.data.entity.APITournament
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntry
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinResponse
import com.gametaco.app_android_fd.data.entity.APIUserGameStats
import com.gametaco.app_android_fd.data.entity.EntryFee
import com.gametaco.app_android_fd.data.entity.GCGameData
import com.gametaco.app_android_fd.data.entity.GameCatalogResponse
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.manager.analytics.AppsFlyerEventFactory
import com.gametaco.app_android_fd.manager.analytics.TournamentAnalytics
import com.gametaco.app_android_fd.ui.popups.AlertDialogButtonsSideBySide
import com.gametaco.app_android_fd.ui.popups.AlertDialogRestrictedStatus
import com.gametaco.app_android_fd.ui.popups.AlertDialogTechnicalProblemsFD
import com.gametaco.app_android_fd.ui.popups.AlertDialogTextCancel
import com.gametaco.app_android_fd.ui.screens.GeoAlertModel
import com.gametaco.app_android_fd.ui.screens.GeoAlertType
import com.gametaco.app_android_fd.ui.screens.showGuestPopups
import com.gametaco.app_android_fd.utils.CoroutineScopes
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.viewmodel.GameplayViewModel
import com.gametaco.app_android_fd.viewmodel.LeaderboardViewModel
import com.gametaco.app_android_fd.viewmodel.OnGameReadyEvent
import com.gametaco.utilities.ResourceState
import com.gametaco.utilities.STR
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.transform
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import resources.R
import java.util.Base64
import java.util.Timer
import java.util.concurrent.ConcurrentHashMap
import kotlin.concurrent.fixedRateTimer
import kotlin.time.Duration.Companion.seconds

// MARK: - TournamentManagerDelegate Interface
interface TournamentManagerDelegate {
    fun onStartTournament(o: String)
    fun onLoadGameProgressUpdate(progress: Float)
    fun onGameReady()
    fun onFinalScoreUpdate(o: String)
    fun onReturnToContainer()
    fun onUnityContainerError(errorString: String?)
}

fun generate400ErrorResponse(errorCode:API400ErrorCode) :ResourceState<APITournamentsJoinResponse> {

    return ResourceState.Error(
        "{\"field_errors\":{},\"general_error\":{\"message\":\"\",\"code\":\"${errorCode}\"}}",
        400
    )

}

// MARK: - TournamentManager Class
class TournamentManager(
    val context: Context,
    private val alertDialogManager: AlertDialogManager,
    activityManager: ActivityManager,
    private val worldWinnerAPI: WorldWinnerAPI,
    private val geoComplyManager: GeoComplyManager,
    private val authenticationManager: AuthenticationManager,
    private val brazeManager: BrazeManager,
    private val navManager: NavManager,
    private val modalPopupManager: ModalPopupManager,
    private val coroutineScopes: CoroutineScopes,
) : TournamentManagerDelegate {

    companion object {
        val instance: TournamentManager
            get() = resolve()
        const val TAG = "TournamentManager"
    }

    private var stallTimer: Timer? = null
    private val handler = Handler(Looper.getMainLooper())
    private var tournamentEntryState : TournamentEntryState? = null


    private val _tournamentEntryStateState = mutableStateOf<TournamentEntryState?>(null)
    val tournamentEntryStateState: State<TournamentEntryState?> = _tournamentEntryStateState

    var tournamentJoinErrorDebugOverride : API400ErrorCode = API400ErrorCode.NONE

    private val tournamentLifeCycleProvider by lazy {
        TournamentLifeCycleProvider(
            worldWinnerAPI, authenticationManager, geoComplyManager, tournamentManager = this)
    }
    private val tournamentInstanceEntryIdFlow: MutableStateFlow<String?> = MutableStateFlow(null)
    val tournamentInstanceEntryId: String?
        get() = tournamentInstanceEntryIdFlow.value
    var unityProvider : UnityProviding? = null
    var guestScore : Int = 0

    private val _scoreValid:MutableStateFlow<Boolean?> = MutableStateFlow(null)
    suspend fun waitForScoreValid(): Boolean {
        // only continue when value is not null
        // suspend until the first valid value
        return _scoreValid.filterNotNull().first()
    }

    private val _tournamentInstanceEntryFee = MutableStateFlow<EntryFee>(EntryFee.Unknown)
    val tournamentInstanceEntryFee: StateFlow<EntryFee>
        get() = _tournamentInstanceEntryFee

    private val gameData = MutableStateFlow<Map<String, GCGameData>>(emptyMap())
    var userGameStats: APIUserGameStats? = null
    private val tournamentIdToGameId = mutableMapOf<String, String>()
    // Note: there's no guarantee this will be non-null.
    private val tournamentInstanceEntry: MutableStateFlow<APITournamentInstanceEntry?> = MutableStateFlow(null)

    private var currentTournamentAnalytics: TournamentAnalytics? = null

    init {
        if(BuildConfig.INCLUDE_UNITY) {
            unityProvider = Unity(context, activityManager, tournamentManager = this, brazeManager, coroutineScopes, worldWinnerAPI)
        } else {
            unityProvider = MockUnity()
        }
        unityProvider?.setTournamentManagerDelegate(this)
    }

    fun getTournamentEntryState() : TournamentEntryState? {
        return tournamentEntryState
    }

    fun getTournamentId() : String {
        return tournamentLifeCycleProvider.providerTournamentId?:""
    }

    fun getGameId() : String {
        return tournamentLifeCycleProvider.providerGameId?:""
    }

    val currentGameData: StateFlow<GCGameData?> =
        combine(tournamentLifeCycleProvider.providerGameIdFlow, gameData) {
            currentGameId, gameDataMap ->
            gameDataMap[currentGameId]
                .also {
                    Logger.i("GameData", "[combine] currentGameData: $it, currentGameId: $currentGameId, gameDataMap: $gameDataMap")
                }
        }
            .stateIn(
                scope = coroutineScopes.io,
                started = SharingStarted.Eagerly,
                initialValue = null,
            )

    val currentGameBackgroundImageUrl: Flow<String?> =
        currentGameData.map { gameData ->
            gameData?.loading_background_image_url
        }

    fun resetTournamentEntryState() {
        Logger.d(TAG, "resetTournamentEntryState")

        tournamentEntryState = null
        _tournamentEntryStateState.value = null
        Logger.d("🎟️ TournamentManager - reset tournament entry state")
    }


    fun pauseGame(isPaused: Boolean) {
        Logger.d(TAG, "pauseGame")

        if (tournamentEntryState != null) {
            unityProvider?.pauseGame(isPaused)
        }
    }

    fun reEnterTournament(tournamentInstanceEntryId: String, gameId: String, entryFee: EntryFee) {
        Logger.d(TAG, "reEnterTournament: ${tournamentInstanceEntryId}")

        if(getTournamentEntryState() != null)
            return
        LeaderboardViewModel.instance.closeLeaderboard()

        setEntryState(TournamentEntryState.JoinRequested)
        _tournamentInstanceEntryFee.value = entryFee
        GameplayViewModel.instance.showGameplayScreen { success ->

            //catch the case where user backs out before screen loads
            if(getTournamentEntryState() is TournamentEntryState.JoinRequested) {

                coroutineScopes.main.launch {
                    if(!authenticationManager.isGuest) {

                        val geocomplyToken = tournamentLifeCycleProvider.getGeoComplyToken()

                        if (geocomplyToken == null) {
                            Handler(Looper.getMainLooper()).post {
                                navManager.navigateClearBackStack(Routes.GAMES_SCREEN)
                            }

                            setEntryState(
                                TournamentEntryState.JoinFailure(
                                    "",
                                    API400ErrorCode.TOURNAMENT_JOIN_LOCATION_SERVICE_FAILED,
                                    400
                                )
                            )
                            return@launch
                        }
                    }

                    val result =
                        tournamentLifeCycleProvider.reEnterTournament(tournamentInstanceEntryId)

                    if (result.isSuccess()) {
                        setEntryState(TournamentEntryState.JoinSuccess)

                        val responseData = (result as ResourceState.Success).data
                        tournamentLifeCycleProvider.setTournamentId(responseData.id)
                        tournamentLifeCycleProvider.setGameId(gameId)
                        Logger.d(TAG, "reEnterTournament responseData.id: ${responseData.id}")

                        AppsFlyerManager.instance.logEvent(AppsFlyerEventFactory.instance.cashEntryFee)

                        loadGame(
                            gameName = responseData.game_name,
                            joinRecord = responseData.game_options.join,
                            userId = responseData.user_id,
                            tournamentInstanceEntryId = responseData.id,
                            tournamentInstanceId = responseData.tournament_instance_id,
                            globalGameOptions = responseData.global_game_options,
                            entryFee = entryFee,
                        )
                    } else if (result.isError()) {
                        Handler(Looper.getMainLooper()).post {
                            navManager.navigateClearBackStack(Routes.GAMES_SCREEN)
                        }

                        val errorState = (result as ResourceState.Error)
                        if (errorState.code == 400) {
                            val error400 = worldWinnerAPI.stringTo400Error(errorState.error)

                            val generalError = error400?.general_error
                            val message = generalError?.message ?: "Unknown Error"
                            val code = generalError?.code ?: API400ErrorCode.UNKNOWN_ERROR

                            setEntryState(
                                TournamentEntryState.JoinFailure(
                                    message,
                                    code,
                                    errorState.code
                                )
                            )

                        } else {
                            ErrorManager.instance.handleResourceStateError(result)
                            setEntryState(
                                TournamentEntryState.JoinFailure(
                                    "Unknown Error",
                                    API400ErrorCode.UNKNOWN_ERROR,
                                    errorState.code
                                )
                            )
                        }
                    }

                }
            }
        }
    }

    private fun loadGame(
        gameName: String,
        joinRecord: Map<String, String>,
        userId: String,
        tournamentInstanceId: String,
        tournamentInstanceEntryId: String,
        globalGameOptions: Map<String, Any>?,
        entryFee: EntryFee,
    ) {
        Logger.d(TAG, "loadGame")

        setEntryState(TournamentEntryState.LoadGameRequested)
        tournamentInstanceEntryIdFlow.value = tournamentInstanceEntryId
        _scoreValid.value = null

        val joinParam = mapOf(
            "gameName" to gameName,
            "joinRecord" to joinRecord,
            "userId" to userId,
            "tournamentInstanceId" to tournamentInstanceId,
            "tournamentInstanceEntryId" to tournamentInstanceEntryId,
            "entryFee" to (entryFee.originalFee),
            "applicationVersionId" to AppConstants.API_APP_VERSION,
            "deviceNativeId" to DeviceManager.instance.getDeviceId()!!,
            "authToken" to (AuthenticationManager.instance.authToken ?: emptyMap<String, Any>()),
            "fdAuthToken" to (FDManager.instance.sessionDataToken ?: emptyMap<String, Any>()),
            "apiHostname" to AppEnv.current.apiEndpointHostname,
        )

        var joinParamJSON = JSONObject(joinParam as Map<Any, Any>)

        if(globalGameOptions != null) {
            joinParamJSON.put("globalGameOptions", JSONObject(globalGameOptions))
        }

        Logger.d(TAG, "load game options: ${joinParamJSON.toString()}")

        val data = try {
            joinParamJSON.toString().toByteArray()
        } catch (e: Exception) {
            // TODO: handle errors
            setEntryState(TournamentEntryState.LoadGameFailure)
            return
        }
        val base64Encoded = Base64.getEncoder().encodeToString(data)

        // Run on the main thread
        Handler(Looper.getMainLooper()).post {
            unityProvider?.loadGame(
                encodedString = base64Encoded
            )
        }
    }

    private fun setEntryState(state: TournamentEntryState) {
        Logger.d(TAG, "setEntryState $state")

        coroutineScopes.main.launch {
            if (tournamentEntryState != state || state.isFailureState) {
                tournamentEntryState = state
                _tournamentEntryStateState.value = state
                didUpdateTournamentEntryState(state)
            }
        }
    }

    private fun didUpdateTournamentEntryState(newState: TournamentEntryState?) {
        Logger.d(TAG, "didUpdateTournamentEntryState")

        cancelStallTimer()
        newState?.let { state ->
            Logger.i("🎟️ TournamentManager - Entry State: $state")

            when {
                state is TournamentEntryState.TournamentEntryComplete -> {
                    resetTournamentEntryState()
                }
                state.isSuccessState -> {
                    return
                }
                state.isFailureState -> {
                    handleFailureState(state)
                }
                state.isRequestState -> {
                    startStallTimer(state)
                }
            }

        }
    }

    private fun cancelStallTimer() {
        stallTimer?.cancel()
    }

    private fun startStallTimer(state: TournamentEntryState?) {
        stallTimer = fixedRateTimer(name = "stall-timer", initialDelay = 0L, period = 10 * 1000) {
            coroutineScopes.io.launch {
                Logger.d("🎟️ [ERROR] TournamentManager - stall detected")
            }
        }
    }

    private fun handleFailureState(state: TournamentEntryState) {
        Logger.d(TAG, "handleFailureState")

        var errorSummary : String = "Unknown"

        if(state is TournamentEntryState.TournamentStartFailure) {

            errorSummary = "Failed to start game"
            val tournamentStartFailure = (state as TournamentEntryState.TournamentStartFailure)

            alertDialogManager.showDialog("Error!", "Failed to start game", "Try Again", {
                onStartTournament(tournamentStartFailure.o)
            }, "Exit Game", {
                resetTournamentEntryState()
                unityProvider?.resetUnity()
            } )
        } else if (state is TournamentEntryState.JoinFailure) {

            val joinFailureState = state as TournamentEntryState.JoinFailure

            if(joinFailureState.resourceStateCode == 400) {
                when (joinFailureState.code) {
                    API400ErrorCode.TOURNAMENT_JOIN_GAME_NOT_ALLOWED -> {
                        GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_SPECIFIC_GAME)
                    }

                    API400ErrorCode.TOURNAMENT_JOIN_IDENTITY_NOT_VERIFIED -> {
                        FDManager.instance.showWebviewFD(FDWebviewPage.Verification)
                    }

                    API400ErrorCode.TOURNAMENT_JOIN_LOCATION_SERVICE_FAILED -> {
                        GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_LOCATION)
                    }

                    API400ErrorCode.TOURNAMENT_JOIN_GEOLOCATION_STATE_MISMATCH,
                    API400ErrorCode.TOURNAMENT_JOIN_INELIGIBLE_STATE,
                    API400ErrorCode.TOURNAMENT_JOIN_PAID_CONTESTS_NOT_ALLOWED -> {
                        GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_NOT_ALLOWED)
                    }

                    API400ErrorCode.TOURNAMENT_JOIN_MAX_OPEN_INSTANCES_FOR_USER -> {
                        alertDialogManager.showDialog(
                            "Sorry!",
                            "You already played this tournament the maximum amount of times. Please wait until it closes to play again or choose a different contest.",
                            "Got It", {})
                    }

                    API400ErrorCode.TOURNAMENT_JOIN_LOCATION_CANNOT_BE_DETERMINED,
                    API400ErrorCode.TOURNAMENT_JOIN_JWT_SIGNATURE_NOT_VERIFIED,
                    API400ErrorCode.TOURNAMENT_JOIN_GEOLOCATION_PACKET_EXPIRED,
                    API400ErrorCode.TOURNAMENT_JOIN_GEOLOCATION_IP_ADDRESS_MISMATCH,
                    API400ErrorCode.TOURNAMENT_JOIN_GEOLOCATION_SESSION_ID_MISMATCH,
                    API400ErrorCode.TOURNAMENT_JOIN_GEOLOCATION_CHECKS_FAILED -> {
                        GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_GENERIC)
                    }

                    API400ErrorCode.TOURNAMENT_JOIN_INSUFFICIENT_FUNDS -> {
                        alertDialogManager.showDialogCustom(composeFunction = @Composable {
                            AlertDialogTextCancel(title = ActivityManager.instance.activity.getString(R.string.insufficient_funds_title),
                                message = ActivityManager.instance.activity.getString(R.string.insufficient_funds_msg),
                                aButtonText = ActivityManager.instance.activity.getString(R.string.make_a_deposit),
                                onAPressed = {
                                    FDManager.instance.showWebviewFD(FDWebviewPage.Deposit)
                                },
                                bButtonText = ActivityManager.instance.activity.getString(R.string.cancel),
                                onBPressed = {
                                })
                        })
                    }

                    API400ErrorCode.TOURNAMENT_JOIN_TOURNAMENT_EXPIRED -> {
                        alertDialogManager.showDialog(
                            "This contest has just closed!",
                            "This contest is no longer taking entries.",
                            "Got It",
                            {})
                    }

                    API400ErrorCode.UNKNOWN_FANDUEL_ERROR_OCCURRED,
                    API400ErrorCode.UNKNOWN_ERROR -> {
                        alertDialogManager.showDialogCustom(composeFunction = @Composable {
                            AlertDialogTechnicalProblemsFD(title = "Sorry!",
                                message = "We are experiencing technical problems and apologize for any inconvenience. Please try again later. If you have any questions, please contact Player Services.",
                                aButtonText = "Ok",
                                onAPressed = {
                                },
                                bButtonText = "Get Help",
                                onBPressed = {
                                    FDManager.instance.showWebviewFD(FDWebviewPage.Support)
                                })
                        })
                    }

                    else -> {
                        alertDialogManager.showDialog("Error", state.message, "Got it", {
                        })

                    }
                }

                errorSummary = joinFailureState.code.errorMessage
            }

            resetTournamentEntryState()
        }

        val properties = mutableMapOf<String, Any>()
        properties["Error Summary"] = errorSummary

        val tournamentAnalytics = getCurrentTournamentStats()
        if (tournamentAnalytics != null) {
            properties["Tournament Instance ID"] = tournamentAnalytics.contestId
            properties["Game Name"] = tournamentAnalytics.gameName
            properties["Stake"] = tournamentAnalytics.stake
            properties["Game Mode"] = tournamentAnalytics.gameMode
            properties["Tournament Instance Entry ID"] = tournamentAnalytics.tournamentInstanceEntryId
        }
        AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Game_Failed.value,
            properties = properties))
    }

    override fun onStartTournament(o: String) {
        Logger.d(TAG, "onStartTournament")

        val tournamentInstanceEntryId = this.tournamentInstanceEntryId ?: return

        val eventName = if (authenticationManager.isGuest) {
            AnalyticsEventName.Game_Guest_Started
        } else {
            AnalyticsEventName.Game_Started
        }
        AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent =  eventName.value,
            properties = mapOf("tournament id" to tournamentInstanceEntryId)))

        coroutineScopes.main.launch {
            Logger.d(TAG, "onStartTournament: tournamentInstanceEntryId: ${tournamentInstanceEntryId}, o: $o" )

            setEntryState(TournamentEntryState.TournamentStartRequested)
            val result = tournamentLifeCycleProvider.startTournament(tournamentInstanceEntryId, o)

            if(result.isSuccess()){
                setEntryState(TournamentEntryState.TournamentStartSuccess)
                unityProvider?.startGame((result as ResourceState.Success).data.o)
            } else if(result.isError()) {
                val resourceStateError = (result as ResourceState.Error)

                var errorMessage = resourceStateError.error

                if(resourceStateError.code == 400) {
                    val error400 = WorldWinnerAPI.instance.stringTo400Error(resourceStateError.error)

                    val generalError = error400?.general_error
                    errorMessage = generalError?.message ?: "Unknown Error"
                }

                Logger.d(TAG,"onStartTournament Error: ${errorMessage}")
                setEntryState(TournamentEntryState.TournamentStartFailure(o = o, message = errorMessage))
            }
        }
    }



    fun joinTournament(
        tournamentId: String,
        gameId: String?,
        gameName: String? = null,
        joinRecord: Map<String, String>? = null,
        tournamentInstanceEntryId: String? = null,
        tournamentAnalytics: TournamentAnalytics? = null,
        globalGameOptions: Map<String, Any>? = null,
        entryFee: EntryFee,
    ) {
        Logger.d(TAG, "joinTournament")

        if(getTournamentEntryState() != null)
            return

        //guest mode max tournaments check
        if(authenticationManager.isGuest) {
            if(authenticationManager.apiGuestMe?.message == APIGuestMeMessage.hard.rawValue) {
                coroutineScopes.main.launch {
                    showGuestPopups(modalPopupManager, navManager, authenticationManager.apiGuestMe, true)
                }
                return //can't play any more tournaments as guest
            }
        }

        //handle restricted status
        if(AuthenticationManager.instance.restrictedStatus) {
            AlertDialogManager.instance.showDialogCustom(composeFunction = @Composable {
                AlertDialogRestrictedStatus(onAPressed = {
                    FDManager.instance.showWebviewFD(FDWebviewPage.Deposit)
                },
                    onBPressed = { })
            })
            return
        }

        tournamentLifeCycleProvider.setTournamentId(tournamentId)
        tournamentLifeCycleProvider.setGameId(gameId)
        _tournamentInstanceEntryFee.value = entryFee
        //eventStateMachine?.gameLoading(tournamentId, GameState.JOIN_REQUESTED)

        val userId = authenticationManager.apiMe?.id
        if (gameName != null && joinRecord != null && userId != null && tournamentInstanceEntryId != null && globalGameOptions != null && entryFee != null) {
            GameplayViewModel.instance.showGameplayScreen { success ->
                loadGame(
                    gameName = gameName,
                    joinRecord = joinRecord,
                    userId = userId,
                    tournamentInstanceId = tournamentId,
                    tournamentInstanceEntryId = tournamentInstanceEntryId,
                    globalGameOptions = globalGameOptions,
                    entryFee = entryFee
                )
            }
        } else {

            setEntryState(TournamentEntryState.JoinRequested)

            GameplayViewModel.instance.showGameplayScreen { success ->

                if(getTournamentEntryState() is TournamentEntryState.JoinRequested) {
                    coroutineScopes.io.launch {

                        if(!authenticationManager.isGuest) {

                            val geocomplyToken = tournamentLifeCycleProvider.getGeoComplyToken()

                            if (geocomplyToken == null) {
                                Handler(Looper.getMainLooper()).post {
                                    navManager.navigateClearBackStack(Routes.GAMES_SCREEN)
                                }
                                setEntryState(
                                    TournamentEntryState.JoinFailure(
                                        "",
                                        API400ErrorCode.TOURNAMENT_JOIN_LOCATION_SERVICE_FAILED,
                                        400
                                    )
                                )
                                return@launch
                            }
                        }

                        var response: ResourceState<APITournamentsJoinResponse>
                        if (authenticationManager.isGuest) {
                            val guestResult =
                                tournamentLifeCycleProvider.joinTournamentGuest(tournamentId)
                            response = when (guestResult) {
                                is ResourceState.Success -> ResourceState.Success(
                                    APITournamentsJoinResponse(
                                        id = guestResult.data.id,
                                        user_id = guestResult.data.guest_user_id,
                                        tournament_instance_id = "",
                                        game_name = guestResult.data.game_name,
                                        game_options = guestResult.data.game_options,
                                        global_game_options = guestResult.data.global_game_options,
                                    )
                                )

                                is ResourceState.Error -> ResourceState.Error(
                                    guestResult.error,
                                    guestResult.code
                                )

                                is ResourceState.Loading -> ResourceState.Loading()
                            }
                        } else {
                            if(tournamentJoinErrorDebugOverride != API400ErrorCode.NONE) {
                                response = generate400ErrorResponse(tournamentJoinErrorDebugOverride)
                            } else {
                                response = tournamentLifeCycleProvider.joinTournament(tournamentId)
                            }
                        }

                        var tournamentStats: TournamentAnalytics? = tournamentAnalytics;
                        if (tournamentStats == null && currentTournamentAnalytics != null
                            && currentTournamentAnalytics?.contestId == tournamentId
                        ) {
                            tournamentStats = currentTournamentAnalytics
                        }

                        if (tournamentStats != null) {
                            currentTournamentAnalytics = tournamentStats
                        }


                        if (response.isSuccess()) {
                            setEntryState(TournamentEntryState.JoinSuccess)

                            var responseData = (response as ResourceState.Success).data

                            loadGame(
                                gameName = responseData.game_name,
                                joinRecord = responseData.game_options.join,
                                userId = responseData.user_id,
                                tournamentInstanceId = responseData.tournament_instance_id,
                                tournamentInstanceEntryId = responseData.id,
                                globalGameOptions = responseData.global_game_options,
                                entryFee = entryFee
                            )

                            if (tournamentStats != null) {
                                AnalyticsManager.instance.logEvent(
                                    AnalyticsEvent(
                                        analyticsEvent = AnalyticsEventName.Joined_Game.value,
                                        properties = mapOf(
                                            "Game Name" to tournamentStats.gameName.toString(),
                                            "Contest ID" to tournamentStats.contestId,
                                            "Tournament Instance Entry ID" to responseData.id,
                                            "Tournament Instance ID" to responseData.tournament_instance_id,
                                            "Stake" to tournamentStats.stake.toString(),
                                            "Game Mode" to tournamentStats.gameMode.toString()
                                        )
                                    )
                                )
                            }
                        } else if (response.isError()) {
                            Handler(Looper.getMainLooper()).post {
                                navManager.navigateClearBackStack(Routes.GAMES_SCREEN)
                            }

                            val errorState = (response as ResourceState.Error)
                            if (errorState.code == 400) {

                                val error400 = worldWinnerAPI.stringTo400Error(errorState.error)

                                val generalError = error400?.general_error
                                val message = generalError?.message ?: "Unknown Error"
                                val code = generalError?.code ?: API400ErrorCode.UNKNOWN_ERROR

                                setEntryState(
                                    TournamentEntryState.JoinFailure(
                                        message,
                                        code,
                                        errorState.code
                                    )
                                )

                            } else {
                                ErrorManager.instance.handleResourceStateError(response)
                                setEntryState(
                                    TournamentEntryState.JoinFailure(
                                        "Unknown Error",
                                        API400ErrorCode.UNKNOWN_ERROR,
                                        errorState.code
                                    )
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    fun setIsInGame(newIsInGame : Boolean) {
        _isInGame.value = newIsInGame
    }

    private val _isInGame = MutableStateFlow(false)
    val isInGame: StateFlow<Boolean> get() = _isInGame

    override fun onGameReady() {
        Logger.d(TAG, "onGameReady")

        // This may be true if the user has pressed back during the loading phase
        if (getTournamentEntryState() == null) {
            Logger.w(TAG, "👀 onGameReady: tournamentEntryState is null, exiting...")
            return
        }

        coroutineScopes.main.launch {

            //fake set loading progress in case loading progress is bogus
            while(GameplayViewModel.instance.getLoadingProgress() < 1.0f &&
                (getTournamentEntryState() is TournamentEntryState.LoadGameRequested ||
                        getTournamentEntryState() is TournamentEntryState.LoadGameInProgress)) {

                var loadingProgress = GameplayViewModel.instance.getLoadingProgress()

                loadingProgress = minOf(loadingProgress + 0.1f, 1.0f)

                GameplayViewModel.instance.setLoadingProgress(loadingProgress)

                delay(100)
            }

            // Only set LoadGameSuccess if in one of these states,
            // There are daily mode tournaments that will skip the start screen and start the game automatically
            // as soon as it is loaded and as such the tournament state will be something further along the tournament flow like StartRequested etc..
            if(getTournamentEntryState() is TournamentEntryState.LoadGameRequested ||
                getTournamentEntryState() is TournamentEntryState.LoadGameInProgress) {

                setEntryState(TournamentEntryState.LoadGameSuccess)
            }

            //make sure loading bar is full
            GameplayViewModel.instance.setLoadingProgress(1.0f)

            //wait a lil bit so that loading bar is seen as full
            delay(100)

            Logger.d(TAG, "sending onGameReadyEvent")
            EventBus.getDefault().post(OnGameReadyEvent(true))
        }
    }

    //returning true means don't do back button navigation elsewhere
    //returning false means allow back button navigation elsewhere
    fun handleBackButton() : Boolean {
        if (getTournamentEntryState() == null) {
            return false
        }

        //block back button during joining and loading
        //backing out while joining can mess up geocomply
        //backing out while loading can completey lock the app up, cause the game to crash etc...
        if(getTournamentEntryState() is TournamentEntryState.JoinRequested ||
            getTournamentEntryState() is TournamentEntryState.LoadGameRequested ||
            getTournamentEntryState() is TournamentEntryState.LoadGameInProgress ||
            getTournamentEntryState() is TournamentEntryState.LoadGameFailure
            ) {
            return true
        }

        if (alertDialogManager.isShowing()) {
            return true
        }

        alertDialogManager.showDialogCustom(composeFunction = @Composable {
            AlertDialogButtonsSideBySide(title = STR(R.string.are_you_sure_leave_app),
                message = STR(R.string.leave_app_in_a_game_save_score),
                aButtonText = STR(R.string.leave_app),
                onAPressed = {
                    unityProvider?.resetUnity()
                },
                bButtonText = STR(R.string.stay_on_app),
                onBPressed = {
                })
        })

        return true
    }

    override fun onLoadGameProgressUpdate(progress: Float) {
        Logger.d(TAG, "onLoadGameProgressUpdate $progress")

        setEntryState(TournamentEntryState.LoadGameInProgress(progress = progress))
        // Update progress
        GameplayViewModel.instance.setLoadingProgress(progress)
    }

    override fun onReturnToContainer() {
        Logger.d(TAG, "onReturnToContainer")

        coroutineScopes.main.launch {
            _isInGame.value = false
        }

        if (!authenticationManager.isGuest) {
            tournamentInstanceEntryId?.also { tournamentInstanceEntryId ->
                coroutineScopes.io.launch {
                    // Force the data to refresh to minimize appearance of loading UI
                    worldWinnerAPI.getTournamentEntry(tournamentInstanceEntryId)
                }
            }
        }

        cancelStallTimer()
        resetTournamentEntryState()
    }

    enum class UnityError{
        GAME_MISSING,
        ASSETS_FAILED_TO_LOAD
    }
    override fun onUnityContainerError(errorString: String?) {
        Logger.d(TAG, "🎟️ [ERROR] TournamentManager - Unity Container Error:" + errorString)
        resetTournamentEntryState()
        unityProvider?.resetUnity()

        if(errorString == UnityError.GAME_MISSING.name){
            alertDialogManager.showDialog("Failed to Launch Game","Game Missing - Please update to latest","Okay", {})
        }else{
            alertDialogManager.showDialog("Failed to Launch Game", "There was an error trying to launch the game.  Please make sure there is an internet connection, and try again.  If this error continues to occur, you may need to terminate and re-launch the app.", "Okay", {})
        }
    }


    override fun onFinalScoreUpdate(o: String) {
        val tournamentInstanceEntryId = this.tournamentInstanceEntryId ?: return

        coroutineScopes.io.launch {
            // Fast attempts
            val result = tournamentLifeCycleProvider.submitScore(
                tournamentInstanceEntryId,
                o,
                1,
                0.75
            )
            Logger.i("🎟️ TournamentManager - Final score submitted")

            if(result.isError()) {
                alertDialogManager.showDialogCustom(composeFunction = @Composable {
                    AlertDialogButtonsSideBySide(title = "Unable to submit score",
                    message = "Please check your internet connection and select Retry. If you quit, your score might not be recorded.",
                    aButtonText = "Quit",
                    onAPressed = {
                        _scoreValid.value = false
                        unityProvider?.resetUnity()
                    } ,
                    bButtonText = "Retry",
                    onBPressed = {
                        onFinalScoreUpdate(o)
                    })
                })
            } else if(result.isSuccess()) {
                userGameStats = (result as ResourceState.Success).data
                _scoreValid.value = true
                resetTournamentEntryState()
            }
        }
    }

    fun getGameData(tournamentId: String): Flow<GCGameData?> {
        val gameId = tournamentIdToGameId[tournamentId]
        Logger.i("GameData", "getGameData(): [start] tournamentId: $tournamentId, gameId: $gameId")
        return gameData.transform { dataMap ->
            val data = dataMap[gameId]
            Logger.d("GameData", "[inner] getGameData(): tournamentId: $tournamentId, gameId: $gameId, data: $data")
            if (data != null) {
                emit(data)
            } else {
                emit(null)
            }
        }
    }

    fun addTournamentsIdsForGameId(tournament: APITournament, game_id : String) {
        Logger.d("GameData", "addTournamentsIdsForGameId(): ${tournament.id} -> $game_id")
        tournamentIdToGameId[tournament.id] = game_id
    }

    fun updateGameData(catalogDocument: GameCatalogResponse) {
        val localGameData = ConcurrentHashMap<String, GCGameData>()
        catalogDocument.data.games.keys.forEach { key ->
            catalogDocument.data.games[key]?.let { game ->
                localGameData[game.game_id] = game
                Logger.d("GameData", "updateGameData(): game.game_id: ${game.game_id}")
            }
        }

        Logger.i("GameData", "updateGameData(): size: ${localGameData.size}")
        gameData.value = localGameData.toMap()
    }

    private fun gameData(gameId: String): GCGameData? = gameData.value[gameId]

    fun setTournamentInstanceEntry(value: APITournamentInstanceEntry?) {
        tournamentInstanceEntry.value = value
    }

    init {
        coroutineScopes.main.launch {
            currentGameData.collect {
                Logger.i("GameData", "currentGameData: id: ${it?.id}, loading_background_image_url: ${it?.loading_background_image_url}")
            }
        }

        coroutineScopes.io.launch {
            gameData.collect { data ->
                Logger.d("GameData", "[gameData.collect] data.values.size: ${data.values.size}")
                data.values.forEachIndexed { index, game ->
//                    Log.v("GameData", "[gameData.collect] [$index] id: ${game.id}, game_id: ${game.game_id}, loading_background_image_url: ${game.loading_background_image_url}")
                }
            }
        }

        coroutineScopes.io.launch {
            tournamentInstanceEntryIdFlow.collect { tournamentInstanceEntryId ->
                if (!authenticationManager.isGuest && tournamentInstanceEntryId != null) {
                    worldWinnerAPI.getTournamentEntry(tournamentInstanceEntryId)
                }
            }
        }
    }

    fun getCurrentTournamentStats(): TournamentAnalytics? {
        return currentTournamentAnalytics
    }

    fun getReplayFilePath(tournamentInstanceEntryId : String) : String? {
        return unityProvider?.getReplayFilePath(tournamentInstanceEntryId)
    }

    fun getReplayFilePathWithTimeout(
        tournamentInstanceEntryId: String?,
        timeoutSeconds: Long = 10,
        maxRetries: Int = Int.MAX_VALUE,
        delayMs: Long = 100
    ): Flow<String?> = flow {
        if(tournamentInstanceEntryId.isNullOrEmpty()){
            emit(null)
            return@flow
        }

        var emittedNull = false

        val result = withTimeoutOrNull(timeoutSeconds.seconds) {
            var attempts = 0
            while (attempts < maxRetries) {
                val filePath = unityProvider?.getReplayFilePath(tournamentInstanceEntryId)

                if (!filePath.isNullOrEmpty()) {
                    return@withTimeoutOrNull filePath
                }

                if (!emittedNull) {
                    emit(null)
                    emittedNull = true
                }

                attempts++
                delay(delayMs)
            }
            null
        }

        // Only emit if result is non-null and wasn't already emitted
        if (!result.isNullOrEmpty()) {
            emit(result)
        }
    }
}
