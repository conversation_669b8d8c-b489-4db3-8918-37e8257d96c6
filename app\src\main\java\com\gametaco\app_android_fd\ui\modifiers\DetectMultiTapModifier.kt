package com.gametaco.app_android_fd.ui.modifiers
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput

/**
 * Detect multi tap
 * @param maxDelayBetweenTaps in ms default 300ms
 * @param numberOfTapsRequired default 3 times
 * @param onMultiTapDetected callback
 */
fun Modifier.detectMultiTap(
    maxDelayBetweenTaps: Long = 300,//ms
    numberOfTapsRequired: Int = 3,//times
    onMultiTapDetected: () -> Unit//callback
): Modifier = this.then(pointerInput(Unit) {
    var lastTapTime = 0L
    var tapCount = 0

    detectTapGestures(
        onTap = {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastTapTime > maxDelayBetweenTaps) {
                tapCount = 0
            }
            tapCount++
            lastTapTime = currentTime

            if (tapCount == numberOfTapsRequired) {
                onMultiTapDetected()
                tapCount = 0
            }
        }
    )
})
