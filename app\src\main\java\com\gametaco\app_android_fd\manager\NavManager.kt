package com.gametaco.app_android_fd.manager

import androidx.navigation.NavHostController
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.log.Logger

class NavManager {

    companion object {
        val instance: NavManager
            get() = resolve()
        const val TAG = "NavManager"
    }

    private var navController: NavHostController? = null

    private var _wasOnContestScreen: Boolean = false

    fun getWasOnContestScreen(): Boolean {
        return _wasOnContestScreen
    }

    fun setWasOnContestScreen(wasOnContestScreen: Boolean) {
        if (_wasOnContestScreen == wasOnContestScreen)
            return;

        _wasOnContestScreen = wasOnContestScreen
        Logger.d(TAG, "wasOnContestScreen set to $wasOnContestScreen")
    }

    val currentDestinationRoute: String?
        get() = navController?.currentDestination?.route
    val currentBackStackId: String?
        get() = navController?.currentBackStackEntry?.id

    fun registerNavController(navController: NavHostController) {
        this.navController = navController
    }

    fun navigate(route: String) {
        if (route == Routes.CONTEST_SCREEN) {
            setWasOnContestScreen(true)
        }

        navController?.navigate(route)

        //when navigating to games screen try handle cached deep links
        if(route == Routes.GAMES_SCREEN)
        {
            DeepLinkManager.instance.tryHandleCachedDeepLinkIntent()
        }
    }

    fun navigateClearBackStack(route: String, newStartEntry : String? = null) {
        setWasOnContestScreen(false)
        navController?.navigateClearBackStack(route, newStartEntry)

        //when navigating to games screen try handle cached deep links
        if(route == Routes.GAMES_SCREEN)
        {
            DeepLinkManager.instance.tryHandleCachedDeepLinkIntent()
        }

    }

    fun navigateUp() {
        navController?.navigateUp()
    }

    //return true if navigation handled here
    //return false if not handled here
    fun navigateBackRequested() : Boolean {
        // if we're on the login screen, and the user has pressed back on a webview,
        // then navigateUp to get to the correct backstack which should have been set when navigating to the login screen
        if(currentDestinationRoute == Routes.LOGIN_SCREEN) {
            navigateUp()
            return true
        }

        return false
    }
}


private fun NavHostController.navigateClearBackStack(
    route: String, newStartEntry : String? = null
) {

    if(newStartEntry != null) {
        this.navigate(route) {
            popBackStack(newStartEntry, false)
        }
    } else {
        this.navigate(route) {
            while(currentBackStackEntry != null) {
                popBackStack()
            }
        }
    }



}

