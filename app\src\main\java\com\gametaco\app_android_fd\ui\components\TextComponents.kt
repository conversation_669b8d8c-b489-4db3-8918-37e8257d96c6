package com.gametaco.app_android_fd.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.isUnspecified

@Composable
fun TextCentered(
    text: String,
    icon: Int? = null,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
) {
    Box(modifier = modifier)
    {
        Row(
            modifier = Modifier.fillMaxSize()

        )
        {
            if(icon != null)
            {
                Image(
                    painter = painterResource(icon),
                    contentDescription = null,
                    modifier = Modifier
                        .offset(x = (-3).dp)
                        .padding(top = 4.dp)
                        .size(10.dp)
                        .align(Alignment.CenterVertically),
                )
            }
            AutosizeText(
                modifier = Modifier.padding(top = 4.5.dp),
                text = text,
                style = TextStyle(fontFamily = fontFamily, fontSize = fontSize, fontWeight = fontWeight, color = color),
                textAlign = TextAlign.Center,
                maxLines = 1,
            )
        }
    }
}

@Composable
fun AutosizeText(
    text: String,
    style: TextStyle,
    maxLines: Int,
    modifier: Modifier = Modifier,
    color: Color = style.color,
    fontWeight: FontWeight? = null,
    textAlign: TextAlign? = null
) {
    var resizedStyle by remember { mutableStateOf(style) }
    var readyToDraw by remember { mutableStateOf(false) }

    val defaultFontSize = style.fontSize

    Text(
        text = text,
        modifier = modifier.drawWithContent {
            if (readyToDraw) {
                drawContent()
            }
        },
        color = color,
        softWrap = false,
        style = resizedStyle,
        maxLines = maxLines,
        fontWeight = fontWeight,
        textAlign = textAlign,
        onTextLayout = { result ->
            if (result.didOverflowWidth) {
                if (style.fontSize.isUnspecified) {
                    resizedStyle = resizedStyle.copy(
                        fontSize = defaultFontSize
                    )
                }
                resizedStyle = resizedStyle.copy(
                    fontSize = resizedStyle.fontSize * 0.95
                )
            } else {
                readyToDraw = true
            }
        }
    )
}