package com.gametaco.app_android_fd.ui.utils

import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.Dp
import androidx.core.view.ViewCompat

@Composable
fun getStatusBarHeight(): Dp {
    val density = LocalDensity.current
    val view = LocalView.current
    val insets = ViewCompat.getRootWindowInsets(view)
    val statusBarHeight = insets?.systemWindowInsetTop ?: 0
    return with(density) { statusBarHeight.toDp() }
}

@Composable
fun getNavigationBarHeight(): Dp {
    val density = LocalDensity.current
    val view = LocalView.current
    val insets = ViewCompat.getRootWindowInsets(view)
    val navigationBarHeight = insets?.systemWindowInsetBottom ?: 0
    return with(density) { navigationBarHeight.toDp() }
}

fun Modifier.heightIfNotNull(height: Dp?): Modifier {
    return height?.let { this.height(it) } ?: this
}
fun Modifier.widthIfNotNull(width: Dp?): Modifier {
    return width?.let { this.width(it) } ?: this
}