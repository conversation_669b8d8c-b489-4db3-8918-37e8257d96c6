package com.gametaco.app_android_fd.data.entity

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.serializer

@Serializable
data class GameCatalogVersion (
    val id: String,
    val version_number: Int,
    val is_eligible_for_card_games:Boolean
) {
    val jsonString: String
        get() = Json.encodeToString(kotlinx.serialization.serializer(), this)

    companion object {
        fun fromJsonString(jsonString: String): GameCatalogVersion {
            return Json.decodeFromString(kotlinx.serialization.serializer(), jsonString)
        }
    }
}
