package com.worldwinner.fanduel.uitest.appium

import com.worldwinner.fanduel.uitest.UiTestManager
import org.slf4j.LoggerFactory

class AppiumManager(
    val applicationId: String = "com.fanduel.skillgames",
    val activityName: String = "com.gametaco.app_android_fd.MainActivity",
) {
    companion object {
        private val appiumDriverManager = AppiumDriverManager()
    }

    private val logger = LoggerFactory.getLogger(AppiumManager::class.java)

    suspend fun startAppium() {
        appiumDriverManager.startAppium()
    }

    suspend fun stopAppium() {
        appiumDriverManager.stopAppium()
    }

    suspend fun isAppiumRunning(): Bo<PERSON>an {
        return appiumDriverManager.isAppiumRunning()
    }

    suspend fun run(
        uiTestManager: UiTestManager,
        body: suspend () -> Unit
    ) {
        startAppium()
        try {
            val driver = appiumDriverManager.createDriver(
                applicationId = applicationId,
                activityName = activityName,
            )
            uiTestManager.setAndroidDriver(driver)
            body.invoke()
            stopAppium()
        } catch (e: Exception) {
            stopAppium()
            logger.error(e.localizedMessage)
            throw e
        }
    }
}