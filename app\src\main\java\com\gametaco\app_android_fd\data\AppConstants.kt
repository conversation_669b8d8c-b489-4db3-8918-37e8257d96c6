package com.gametaco.app_android_fd.data

object AppConstants {
    const val API_BASE_URL_DEV = "https://sandbox-fd-api.wwqa.net"
    const val API_BASE_URL_GAMES = "https://qa2-apix.wwqa.net"
    const val API_BASE_URL_PROD = "https://api.ww-fd.com/"
    const val API_BASE_URL_QA = "https://qa1-fd-api.wwqa.net"

    const val FANDUEL_BASE_URL_DEV_GT = "http://account.games.devstack-gtstaging-web.use1.dev.us.fdbox.net"
    const val FANDUEL_BASE_URL_DEV_WW = "http://account.games.devstack-wwstaging-web.use1.dev.us.fdbox.net"
    const val FANDUEL_BASE_URL_PROD = "https://account.skillgames.fanduel.com"

    const val STATE_LIST_URL = "https://support.fanduel.com/s/article/Where-can-I-play-Faceoff"

//    const val API_BASE_URL = API_BASE_URL_DEV
    const val API_BASE_URL = API_BASE_URL_QA //default to qa environment

    const val API_APP_ID = "fd-android"
    //val API_APP_VERSION by lazy { BuildConfig.VERSION_NAME }
    const val API_APP_VERSION  = "1.0.0"

    const val BRAZE_API_KEY_DEV = "640cfc6d-f5d2-4cac-82b4-e0893af4affb"
    const val BRAZE_API_ENDPOINT_DEV = "sdk.iad-01.braze.com"
    const val BRAZE_API_KEY_PROD = "55a0e97b-3b42-4466-be2d-61637ba2b505"
    const val BRAZE_API_ENDPOINT_PROD = "sdk.iad-01.braze.com"
    const val AMPLITUDE_API_KEY_DEV = "********************************"
    const val AMPLITUDE_APP_ID_DEV = "344617"
    const val AMPLITUDE_EXPERIMENTS_KEY_DEV = "client-qELZBFqygi9uNRYaHmBDJgIncjy4HUlG"
//    const val AMPLITUDE_EXPERIMENTS_KEY_DEV = "client-rgYGKJZ8fTK64K7Do4p2E3iqUTwwal4Y"
    const val AMPLITUDE_API_KEY_PROD = "********************************"
    const val AMPLITUDE_APP_ID_PROD = "364168"
    const val AMPLITUDE_EXPERIMENTS_KEY_PROD = "client-1SYPosE196MXFcB2I3JPueYCpxrFUzSJ"
    const val APPSFLYER_API_KEY = "NEvWiNTVDrPoU8NBij8dab"
    const val APPSFLYER_APP_ID = "com.fanduel.skillgames"
    const val FIREBASE_CLOUD_MESSAGING_SENDER_ID = "************"

    object Trustly {
        const val URL_BASE = "https://www.fanduel.com/"
        private const val URL = "wallet/deposit/trustly-callback"
        const val FULL_URL = "${URL_BASE}+${URL}"
        const val PAYMENT_PROVIDER_ID = "TrustlyPaymentProviderId"
        const val OAUTH_LOGIN_PATH = "/oauth/login"
        const val PROCEED_TO_CHOOSE_ACCOUNT_SCRIPT = "javascript:window.Trustly.proceedToChooseAccount();"
    }

    const val SIFT_ACCOUNT_ID_DEV = "60f88a09293fb116c8220753"
    const val SIFT_ACCOUNT_ID_PROD = "60f88a09293fb116c8220750"
    const val SIFT_BEACON_KEY_DEV = "ae7b6ee919"
    const val SIFT_BEACON_KEY_PROD = "7336918cca"

    const val MAX_GUEST_PLAYS_COUNT = 7
    const val WARN_GUEST_PLAYS_COUNT = 5

    const val VIEW_MAX_WIDTH = 650
}
