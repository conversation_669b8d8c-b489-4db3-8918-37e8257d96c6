package com.gametaco.app_android_fd.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.gametaco.app_android_fd.data.entity.API400ErrorCode


@Composable
fun DisabledButton(modifier: Modifier = Modifier, title: String,subTitle:String? = null, textColor: Color = Color.Black){
    CommonButton(
        modifier = modifier,
        title = title,
        subTitle = subTitle,
        enabled = false,
        fillColor = Color(0xFFEAF0F6),
        textColor = textColor,
        onClick = {}
    )
}

@Composable
fun CommonButton(
    modifier: Modifier = Modifier,
    title: String,
    height: Float = 40f,
    width: Float? = null,
    hasShadow: Boolean = false,
    enabled:Boolean = true,
    subTitle: String? = null,
    titleStyle: TextStyle = MaterialTheme.typography.bodySmall,
    subtitleStyle: TextStyle = MaterialTheme.typography.headlineSmall,
    fillColor: Color = MaterialTheme.colorScheme.primary,
    textColor: Color = MaterialTheme.colorScheme.onPrimary,
    borderColor: Color? = null,
    autoSizeTile:Boolean = false,
    onClick: ()->Unit,
){
    Button(
        enabled = enabled,
        onClick = onClick,
        contentPadding = PaddingValues(2.dp),
        shape = RoundedCornerShape(4.dp),
        colors = ButtonDefaults.buttonColors(containerColor = fillColor, disabledContainerColor = fillColor, contentColor = textColor, disabledContentColor = textColor),
        border = BorderStroke(1.dp, borderColor?: fillColor),
        modifier = Modifier
            .let {
                if (width != null) {
                    it.width(width.dp)
                } else {
                    it.fillMaxWidth()
                }
            }
            .then(
                if (hasShadow) {
                    Modifier.shadow(2.dp, RoundedCornerShape(4.dp))
                } else {
                    Modifier
                }
            )
            .background(fillColor, RoundedCornerShape(4.dp))
            .height(height.dp)
            .padding(0.dp)
            .then(modifier)
    ) {
        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
        )
        {
            if(autoSizeTile){
                AutosizeText(
                    textAlign = TextAlign.Center,
                    text = title,
                    color = textColor,
                    style = titleStyle,
                    maxLines = 1
                )
            }else{
                Text(
                    textAlign = TextAlign.Center,
                    text = title,
                    color = textColor,
                    style = titleStyle,
                )
            }

            Spacer(modifier = Modifier.height(2.dp))
            if(subTitle != null)
            {
                Text(
                    textAlign = TextAlign.Center,
                    text = subTitle,
                    color = textColor,
                    style = subtitleStyle,
                )
            }
        }
    }
}

@Composable
fun LinkedText(
    title: String,
    style:TextStyle = MaterialTheme.typography.bodyMedium.copy(color = MaterialTheme.colorScheme.primary),
    onClick: ()->Unit,
)
{
    Box(
        modifier = Modifier
            .background(Color.Transparent)
            .wrapContentSize()
            .clickable {
                onClick()
            }
    )
    {
        Text(
            modifier = Modifier
                .wrapContentWidth(Alignment.CenterHorizontally),
            text = title,
            style = style,
        )
    }
}

@Composable
fun ProgressBar(
    modifier: Modifier = Modifier,
    currentStep: Int,
    maxSteps: Int,
    backgroundColor: Color = MaterialTheme.colorScheme.outline,
    barColor: Color = MaterialTheme.colorScheme.primary,
    cornerRadius: Dp = 6.dp,
) {
    var barWidth by remember { mutableStateOf(0.dp)}
    val density = LocalDensity.current
    Box(
        modifier = Modifier
            .background(backgroundColor, RoundedCornerShape(cornerRadius))
            .then(modifier)
            .onGloballyPositioned { layoutCoordinates ->
                barWidth = with(density) {layoutCoordinates.size.width.toDp()}
            }
    ) {
        val calculatedProgressWidth = (currentStep.toFloat() / maxSteps.toFloat() * barWidth.value)
        //if non-zero bar make sure it's longer than corner radius
        val progressWidth =
            if ((currentStep == 0) || calculatedProgressWidth > cornerRadius.value)
                calculatedProgressWidth.dp
            else
                cornerRadius
        Box(
            modifier = Modifier
                .background(
                    barColor,
                    RoundedCornerShape(cornerRadius)
                )
                .fillMaxHeight()
                .width(progressWidth)
        )
    }
}


@Composable
fun DebugActionButton(
    title: String,
    action: () -> Unit
) {
    Box(
        modifier = Modifier
            .background(Color.Transparent)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp, 0.dp)
                .height(36.dp)
                .background(Color.Transparent),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
            )
            Button(
                onClick = { action() },
                modifier = Modifier.align(Alignment.CenterVertically)
            ) {
                Text("Click Me")
            }
        }
    }
}

@Composable
fun ToggleSwitch(
    title:String,
    initialValue:Boolean,
    valueSetFunction: (Boolean)-> Unit,
    valueGetFunction: ()-> Boolean
)
{
    Box(
        modifier = Modifier
            .background(Color.Transparent)
    )
    {
        Row (
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp, 0.dp)
                .height(36.dp)
                .background(Color.Transparent),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
            )
            var switchValue by remember { mutableStateOf(initialValue) }
            Switch(
                modifier = Modifier
                    .align(Alignment.CenterVertically),
                enabled = true,
                checked = (switchValue),
                onCheckedChange =
                {
                    valueSetFunction(it)
                    switchValue = valueGetFunction()
                })
        }
    }
}


@Composable
fun Error400Selector(
    title: String,
    initialValue: API400ErrorCode = API400ErrorCode.NONE,
    onValueChange: (API400ErrorCode) -> Unit
) {
    var selectedValue by remember { mutableStateOf(initialValue) }
    var expanded by remember { mutableStateOf(false) }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(24.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(36.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
            )
            Text(
                text = selectedValue.value,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier
                    .clickable { expanded = true }
                    .padding(8.dp)
                    .background(MaterialTheme.colorScheme.surfaceVariant)
            )
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                modifier = Modifier.fillMaxWidth()
            ) {
                API400ErrorCode.values().forEach { errorCode ->
                    DropdownMenuItem(
                        text = { Text(errorCode.value) },
                        onClick = {
                            selectedValue = errorCode
                            onValueChange(errorCode)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

