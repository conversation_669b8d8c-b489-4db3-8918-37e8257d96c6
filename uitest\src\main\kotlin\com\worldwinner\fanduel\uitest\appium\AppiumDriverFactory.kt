package com.worldwinner.fanduel.uitest.appium


import io.appium.java_client.android.AndroidDriver
import io.appium.java_client.android.options.UiAutomator2Options
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import org.slf4j.LoggerFactory
import java.net.HttpURLConnection
import java.net.URL
import java.time.Duration

class AppiumDriverManager {
    private val logger = LoggerFactory.getLogger(AppiumDriverManager::class.java)
    private var appiumProcess: Process? = null

    /**
     * Gets the ChromeDriver executable path from environment variable and validates its existence
     * @throws IllegalStateException if APPIUM_CHROME_DRIVER is not set or the path doesn't exist
     */
    private fun getChromeDriverPath(): String {
        val path = System.getenv("APPIUM_CHROME_DRIVER") 
            ?: throw IllegalStateException("APPIUM_CHROME_DRIVER environment variable must be set. Download it from https://googlechromelabs.github.io/chrome-for-testing/")
        
        if (!java.io.File(path).exists()) {
            throw IllegalStateException("ChromeDriver not found at path specified in APPIUM_CHROME_DRIVER: $path")
        }
        
        return path
    }

    private fun getApkPath(): String? {
        val apkPath = System.getProperty("apkPath")
        return (if (apkPath != null && apkPath.isNotBlank()) {
            apkPath
        } else {
            null
        }).also {
            println("Apk path: $it")
        }
    }

    /**
     * Checks if any Android devices (emulator or physical) are connected and available
     * @throws IllegalStateException if no devices are available
     */
    private fun checkAndroidDeviceAvailable() {
        val process = ProcessBuilder("adb", "devices")
            .redirectOutput(ProcessBuilder.Redirect.PIPE)
            .redirectError(ProcessBuilder.Redirect.PIPE)
            .start()
        
        val output = process.inputStream.bufferedReader().use { it.readText() }
        process.waitFor(5, java.util.concurrent.TimeUnit.SECONDS)
        
        // Split output into lines and remove the first line (which is always "List of devices attached")
        val devices = output.lines()
            .drop(1)
            .filter { it.isNotBlank() }
            .map { it.trim() }
            .filter { it.endsWith("device") } // Only count devices that are fully booted
        
        if (devices.isEmpty()) {
            throw IllegalStateException("No Android devices found. Please ensure an emulator or physical device is connected and authorized.")
        }
        
        logger.info("Found ${devices.size} available Android ${if (devices.size == 1) "device" else "devices"}")
    }

    /**
     * Starts Appium Server as a background process.
     */
    suspend fun startAppium() = withContext(Dispatchers.IO) {
        if (isAppiumRunning()) {
            logger.info("Appium is already running. Skipping start.")
            return@withContext
        }

        logger.info("Starting Appium server...")

        val processBuilder = ProcessBuilder("appium", "--port", "4723")
            .redirectOutput(ProcessBuilder.Redirect.INHERIT)
            .redirectError(ProcessBuilder.Redirect.INHERIT)

        appiumProcess = processBuilder.start()

        val success = withTimeoutOrNull(10_000) { // Wait up to 10s
            while (!isAppiumRunning()) {
                delay(1000)
            }
            true
        } ?: false

        if (!success) {
            throw RuntimeException("Appium server failed to start within timeout")
        }

        logger.info("Appium server is up and running.")
    }

    /**
     * Stops Appium Server.
     */
    suspend fun stopAppium() = withContext(Dispatchers.IO) {
        logger.info("Stopping Appium server...")
        appiumProcess?.destroy()
        appiumProcess?.waitFor(5, java.util.concurrent.TimeUnit.SECONDS)
        appiumProcess = null
    }

    /**
     * Checks if Appium is running by making a request to the status endpoint.
     */
    suspend fun isAppiumRunning(): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            val connection = URL("http://localhost:4723/status").openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = 2000
            connection.readTimeout = 2000
            connection.responseCode == 200
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Creates an Appium driver instance for Android.
     */
    suspend fun createDriver(
        applicationId: String = "com.android.settings",
        activityName: String = ".Settings"
    ): AndroidDriver = withContext(Dispatchers.IO) {
        logger.info("Initializing Appium driver...")

        // Check for Android device before proceeding
        checkAndroidDeviceAvailable()

        val options = UiAutomator2Options().apply {
            setPlatformName("Android")
            setAutomationName("UiAutomator2")
            setDeviceName("Android Emulator")
            getApkPath()?.also { setApp(it) }
            setAppPackage(applicationId)
            setAppActivity(activityName)
            setChromedriverExecutable(getChromeDriverPath())
            setLanguage("en")
            setLocale("US")
        }

        return@withContext AndroidDriver(
            URL("http://localhost:4723"),
            options
        ).apply {
            manage().timeouts().implicitlyWait(Duration.ofSeconds(10))
        }.also {
            logger.info("Appium driver initialized successfully")
        }
    }
}