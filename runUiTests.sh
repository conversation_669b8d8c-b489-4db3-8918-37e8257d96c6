#!/bin/bash

# Function to print usage info
usage() {
    echo "Usage: $0 [-h|--help] [-apk=path/to/apk]"
    echo ""
    echo "Options:"
    echo "  -h, --help      Show this help message and exit."
    echo "  -apk=<path>     Specify the APK file path to use for testing."
    exit 0
}

APK_PATH=""

# Process arguments
for arg in "$@"; do
    case $arg in
        -h|--help)
            usage
            ;;
        -apk=*)
            APK_PATH="${arg#*=}"
            shift
            ;;
        *)
            echo "Unknown argument: $arg"
            usage
            ;;
    esac
done

if [ -n "$APK_PATH" ]; then
    echo "Tests will use specified APK: $APK_PATH"
    ./gradlew uitest:test -PapkPath="$APK_PATH"
else
    echo "No APK specified - tests will use device's current Fanduel Faceoff APK."
    ./gradlew uitest:test
fi