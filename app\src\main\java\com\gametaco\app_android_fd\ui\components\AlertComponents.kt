package com.gametaco.app_android_fd.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.gametaco.utilities.STR
import resources.R

@Composable
fun GeoAlertPage(
    title: String,
    icon: Int,
    dataBlock: String,
    mainButtonFunction: ()-> Unit,
    mainButtonTitle: String,
    secondaryButtonFunction: ()-> Unit,
    secondaryButtonTitle: String,
)
{
    Text(
        text = title,
        color = Color.White,
        style = MaterialTheme.typography.titleMedium,
        modifier = Modifier
            .padding(16.dp)
    )
    Image(
        painter = painterResource(icon),
        alignment = Alignment.TopCenter,
        contentScale = ContentScale.FillHeight,
        contentDescription = STR(R.string.content_description_check),
        modifier = Modifier.fillMaxWidth()
            .fillMaxHeight(.8f)
    )
}