package com.gametaco.app_android_fd.manager

import android.app.Activity
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.service.notification.StatusBarNotification
import com.braze.Braze
import com.braze.Constants
import com.braze.IBrazeDeeplinkHandler
import com.braze.configuration.BrazeConfig
import com.braze.enums.Channel
import com.braze.enums.NotificationSubscriptionType
import com.braze.models.cards.Card
import com.braze.models.outgoing.BrazeProperties
import com.braze.models.push.BrazeNotificationPayload
import com.braze.push.BrazeNotificationFactory
import com.braze.ui.BrazeDeeplinkHandler
import com.braze.ui.actions.NewsfeedAction
import com.braze.ui.actions.UriAction
import com.braze.ui.inappmessage.BrazeInAppMessageManager
import com.gametaco.app_android_fd.MainActivity
import com.gametaco.app_android_fd.MainApplication
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.log.Logger
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import org.json.JSONObject

enum class BrazeContentCardType{
    carousel,
    raf,
    contest_lobby,
}

class BrazeManager(
    private val activityManager: ActivityManager,
) : IBrazeDeeplinkHandler {

    companion object {
        val instance: BrazeManager
            get() = resolve()
        val TAG = "BrazeManager"
    }

    private val activity: Activity
        get() = activityManager.activity

    private val _cards:MutableStateFlow<List<Card>> = MutableStateFlow(listOf())
    val cards = _cards.asStateFlow()

    fun initBraze() {

        val config : BrazeConfig = BrazeConfig.Builder()
            .setApiKey(AppEnv.current.braze_api_key)
            .setCustomEndpoint(AppEnv.current.braze_api_endpoint)
            .setHandlePushDeepLinksAutomatically(true)
            .setPushDeepLinkBackStackActivityClass(MainActivity::class.java)
            .setGreatNetworkDataFlushInterval(10)
            .setIsFirebaseCloudMessagingRegistrationEnabled(true)
            .setFirebaseCloudMessagingSenderIdKey(AppEnv.current.firebase_cloud_messaging_sender_id)
            .build()

        Logger.i("===========initBraze===========")

        Braze.configure(MainApplication.context,config = config)
        BrazeInAppMessageManager.getInstance().ensureSubscribedToInAppMessageEvents(MainApplication.context)
        BrazeDeeplinkHandler.setBrazeDeeplinkHandler(this)

        Braze.customBrazeNotificationFactory = CustomBrazeNotificationFactory()
    }


    fun openSession(){
        Braze.getInstance(MainApplication.context).openSession(activity)
    }
    fun closeSession(){
        Braze.getInstance(MainApplication.context).closeSession(activity)
    }
    fun enableInAppMessagePresenter(){
        Logger.w("===========enableInAppMessagePresenter===========")
        BrazeInAppMessageManager.getInstance().registerInAppMessageManager(activity)
    }
    fun disableInAppMessagePresenter(){
        Logger.w("===========disableInAppMessagePresenter===========")
        BrazeInAppMessageManager.getInstance().unregisterInAppMessageManager(activity)
    }
    fun changeUser(brazeExternalId:String){
        Logger.w("===========changeUser to $brazeExternalId ===========")
        Braze.getInstance(MainApplication.context).changeUser(brazeExternalId)

        subscribeToContentCardsUpdates(){
            Logger.w("braze content cards update: ${it.allCards}")
            //make all cards read
            it.allCards.forEach {
                it.isIndicatorHighlighted = true
            }
            _cards.value = it.allCards
        }
        updatePushSubscription(true)

        setTestDevice(getTestDevice())
    }
    fun updatePushSubscription(on:Boolean){
        Braze.getInstance(MainApplication.context).currentUser?.setPushNotificationSubscriptionType(if(on) NotificationSubscriptionType.OPTED_IN else NotificationSubscriptionType.UNSUBSCRIBED)
        Braze.getInstance(MainApplication.context).requestImmediateDataFlush()
    }

    fun subscribeToContentCardsUpdates(subscriber: com.braze.events.IEventSubscriber<com.braze.events.ContentCardsUpdatedEvent>){
        Braze.getInstance(MainApplication.context).subscribeToContentCardsUpdates(subscriber)
        requestContentCardsRefresh()
    }
    fun requestContentCardsRefresh(){
        Braze.getInstance(MainApplication.context).requestContentCardsRefresh()
    }

    fun logEvent(eventName:String){
        Braze.getInstance(MainApplication.context).logCustomEvent(eventName)
    }
    fun logEvent(eventName:String,data:JSONObject){
        Braze.getInstance(MainApplication.context).logCustomEvent(eventName, BrazeProperties(data))
    }

    fun resumeActivity(){
        BrazeInAppMessageManager.getInstance().registerInAppMessageManager(activity)
    }

    fun pauseActivity(){
        BrazeInAppMessageManager.getInstance().unregisterInAppMessageManager(activity)
    }

    fun setTestDevice(isTestDevice : Boolean) {
        PreferencesManager.instance.setBrazeTestDevice(isTestDevice)
        Braze.getInstance(MainApplication.context).getCurrentUser( { user ->
            user.setCustomUserAttribute("isTestDevice", isTestDevice)
        })
    }

    fun getTestDevice() : Boolean {
        return PreferencesManager.instance.getBrazeTestDevice()
    }
    fun setCashBalance(balance:String){
        Braze.getInstance(MainApplication.context).getCurrentUser( { user ->
            user.setCustomUserAttribute("Cash_Balance", balance)
        })
    }

    override fun createUriActionFromUri(
        uri: Uri,
        extras: Bundle?,
        openInWebView: Boolean,
        channel: Channel
    ): UriAction {
        Logger.d(TAG, "createUriActionFromUri uri: ${uri} extras:${extras} openInWebView:${openInWebView} channel:${channel}" )

        return UriAction(uri,extras,openInWebView,channel)
    }

    override fun createUriActionFromUrlString(
        url: String,
        extras: Bundle?,
        openInWebView: Boolean,
        channel: Channel
    ): UriAction? {
        Logger.d(TAG, "createUriActionFromUrlString uri: ${url} extras:${extras} openInWebView:${openInWebView} channel:${channel}" )

        return UriAction(Uri.parse(url),extras,openInWebView,channel)
    }

    override fun getIntentFlags(intentFlagPurpose: IBrazeDeeplinkHandler.IntentFlagPurpose): Int {
        Logger.d(TAG, "getIntentFlags")
        return activity.intent.flags
    }

    override fun gotoNewsFeed(context: Context, newsfeedAction: NewsfeedAction) {
        Logger.d(TAG, "gotoNewsFeed")
        newsfeedAction.execute(context)
    }

    override fun gotoUri(context: Context, uriAction: UriAction) {
        val uri = uriAction.uri

    // reference test links:
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?TournamentSelect,018fa7ff-494e-71cc-8019-91acfaf8c61b") //checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?GameOverviews,018fa7ff-494e-71cc-8019-91acfaf8c61b") //checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?GameHistory") //checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?TournamentResult,0190c7ba-ac32-79e5-9c2a-c3f545e343aa")//checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?FanDuelAccountSettings")//checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?FanDuelAccountSettings,Deposit")//checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?WebView,https://www.fanduel.com")//checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?WebView,https://www.fanduel.com/faceoff-android,Native")//checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?Rewards")//checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?Lobby")//checked

    //broken link tests
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?TournamentSelect") //checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?GameOverviews") //checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?") //checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?TournamentResult")//checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?FanDuelAccountSettings")//checked
//        val uri = Uri.parse("faceoff://JUMP_TO_SCREEN?WebView")//checked

        Logger.d(TAG, "gotoUri: uri: ${uri}")
        DeepLinkManager.instance.handleUri(uri)
    }

    fun clearNotifications() {

        try {
            val context : Context = ActivityManager.instance.activity
            val notificationManager : NotificationManager = (context.getSystemService(Context.NOTIFICATION_SERVICE)) as NotificationManager;

            val activeNotifications : Array<StatusBarNotification> = notificationManager.getActiveNotifications()

            //delete any notifications on the NOTIFICATION_BADGE_CHANNEL_ID
            activeNotifications.forEach { notification ->
                notificationManager.cancel(Constants.BRAZE_PUSH_NOTIFICATION_TAG, notification.id)
            }

        } catch (e: Exception) {
            Logger.e("NotificationChannel", "Error clearing notifications", e)
        }

    }

}

enum class BrazeEventName(val value:String){
    Fanduel_Login_Success("Fanduel_Login_Success"),
    Promo_Banner_Clicked("Promo_Banner_Clicked"),
    FTUE_COMPLETE("FTUE_COMPLETE"),
    Returned_To_Lobby("Returned_To_Lobby"),
    User_Visited_Tournament_Page_Of_Game_("User_Visited_Tournament_Page_Of_Game_"),
    Visited_Deposit_Screen_But_Did_Not_Deposit("Visited_Deposit_Screen_But_Did_Not_Deposit"),
    Contest_Results("Contest Results"),
    Game_Ended("Game Ended"),//todo
    CashBalanceUpdateEvent("CashBalanceUpdateEvent"),//todo
}

val FOREGROUND_NOTIFICATION_CHANNEL_ID : String = "notification_channel_foreground"

class CustomBrazeNotificationFactory : BrazeNotificationFactory() {

    override fun createNotification(payload: BrazeNotificationPayload): Notification? {

        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.O) {

            createNotificationChannels(payload.context)

            if (ActivityManager.instance.mainActivityIsForeground) {
                payload.notificationChannelId = FOREGROUND_NOTIFICATION_CHANNEL_ID
            }
        }

        val builder = populateNotificationBuilder(payload)
        return if (builder != null) {
            builder.setTimeoutAfter(1000 * 60 * 60 * 24 * 30) //30 day timeout
            builder.build()
        } else {
            Logger.e(
                "CustomBrazeNotificationFactory",
                "Notification could not be built. Returning null as created notification"
            )
            null
        }
    }

        fun createNotificationChannels(context: Context?) {

        try {
            // Create a NotificationChannel with the specified parameters.
            val notificationChannel = NotificationChannel(FOREGROUND_NOTIFICATION_CHANNEL_ID, "Notifications without badges", NotificationManager.IMPORTANCE_DEFAULT)
            notificationChannel.description = "Notifications without badges"
            notificationChannel.setShowBadge(false)
            // Register the channel with the system
            val notificationManager = context?.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            if(notificationManager != null) {
                notificationManager.createNotificationChannel(notificationChannel)
            }

        } catch (e: Exception) {
            Logger.e("NotificationChannel", "Error creating notification channel", e)
        }
    }

}