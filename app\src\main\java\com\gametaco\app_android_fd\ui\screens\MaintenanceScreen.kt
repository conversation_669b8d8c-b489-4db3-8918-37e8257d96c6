package com.gametaco.app_android_fd.ui.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.gametaco.app_android_fd.LocalUIManager
import com.gametaco.app_android_fd.data.entity.APIMaintenanceModeStatus
import com.gametaco.app_android_fd.manager.MaintenanceModeManager
import com.gametaco.app_android_fd.ui.components.AutosizeText
import com.gametaco.app_android_fd.ui.components.CommonButton
import com.gametaco.app_android_fd.ui.theme.AppFont
import com.gametaco.app_android_fd.ui.theme.theme_light_tertiary
import com.gametaco.utilities.STR
import resources.R


@Composable
fun ForceUpdateScreen(maintenanceStatus: APIMaintenanceModeStatus) {
    val uiManager = LocalUIManager.current

    val message = maintenanceStatus.force_update_message
    val title = "Update required"

    Image(
        painter = painterResource(id = R.drawable.img_maintenance_bg),
        contentDescription = null,
        contentScale = ContentScale.Crop,
        modifier = Modifier.fillMaxHeight()
    )
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Transparent),
        contentAlignment = Alignment.Center
    ) {

        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
        ) {

            Spacer(Modifier.height(390.dp))


            if (title != null) {
                AutosizeText(
                    text = title!!,
                    style = TextStyle(
                        fontFamily = AppFont.FanduelDisplay,
                        fontSize = 25.sp,
                        color = Color.White,
                        fontWeight = FontWeight.Black,
                        textAlign = TextAlign.Center,
                        fontStyle = FontStyle.Normal
                    ),
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                    modifier = Modifier
                        .padding(horizontal = 48.dp)
                )
            }

            Spacer(Modifier.height(30.dp))

            // message text
            if (message != null) {
                Text(
                    text = message!!,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Light,
                    color = Color.White,
                    fontSize = 18.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .padding(horizontal = 48.dp)
                )
            }

            if (maintenanceStatus.android_download_url != null) {
                Spacer(Modifier.height(80.dp))

                CommonButton(
                    title = STR(R.string.maintenance_update),
                    fillColor = theme_light_tertiary,
                    height = 48f,
                    width = 250f,
                   // titleStyle = MaterialTheme.typography.bodyMedium

                    titleStyle = TextStyle(
                        fontFamily = AppFont.ProximaNova,
                        fontSize = 20.sp,
                        color = Color.White,
                        fontWeight = FontWeight.Light,
                        textAlign = TextAlign.Center,
                        fontStyle = FontStyle.Normal
                    )

                ) {
                    uiManager.openUrlInExternalBrowser(maintenanceStatus.android_download_url!!)
                }
            }
        }
    }
}

@Composable
fun MaintenanceModeScreen(maintenanceStatus: APIMaintenanceModeStatus) {
    val uiManager = LocalUIManager.current

    val title : String? = maintenanceStatus.maintenance_mode_title
    val message = maintenanceStatus.maintenance_mode_message

    Image(
        painter = painterResource(id = R.drawable.img_maintenance_bg),
        contentDescription = null,
        contentScale = ContentScale.Crop,
        modifier = Modifier.fillMaxHeight()
    )
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Transparent),
        contentAlignment = Alignment.Center
    ) {

        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
        ) {

            Spacer(Modifier.height(390.dp))


            Box(
                modifier = Modifier
                    .size(width = 300.dp, height = 200.dp)
                    .background(color = Color(0xFF0568DD).copy(alpha = 0.5f), shape = RoundedCornerShape(16.dp))
                    .border(width = 2.dp, color = Color.White.copy(alpha = 0.2f), shape = RoundedCornerShape(16.dp))
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxSize()
                ) {
                    if (title != null) {
                        Text(
                            text = title!!,
                            style = TextStyle(
                                fontFamily = AppFont.FanduelDisplay,
                                fontSize = 20.sp,
                                color = Color.White,
                                fontWeight = FontWeight.Black,
                                textAlign = TextAlign.Center,
                                fontStyle = FontStyle.Italic
                            ),
                            color = Color.White,
                        )
                    }

                    Spacer(Modifier.height(30.dp))

                    // message text
                    if (message != null) {
                        Text(
                            text = message!!,
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Light,
                            color = Color.White,
                            fontSize = 18.sp,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .padding(horizontal = 16.dp)
                        )
                    }
                }
            }

        }
    }
}



@Composable
fun MaintenanceScreen() {

    val maintenanceStatus by MaintenanceModeManager.instance.maintenanceStatus.collectAsState()


    if(maintenanceStatus.maintenance_mode)
        MaintenanceModeScreen(maintenanceStatus)
    else if(MaintenanceModeManager.instance.isUnderMinimumVersion(maintenanceStatus))
        ForceUpdateScreen(maintenanceStatus)

}





@Preview
@Composable
fun MaintentanceScreenPreview(){
    PreviewRoot {
        MaintenanceScreen()
    }
}