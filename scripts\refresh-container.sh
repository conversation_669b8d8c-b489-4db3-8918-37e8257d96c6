#!/bin/bash
set -e

# This script resets the container submodule to its original state as defined in
# the parent repository, removing any untracked files and uncommitted changes.

SUBMODULE_PATH="game-android-container-fd"

echo "Resetting submodule: $SUBMODULE_PATH to original state"

# First, ensure the submodule is initialized
git submodule update --init --recursive "$SUBMODULE_PATH"

cd "$SUBMODULE_PATH"

# Clean up any untracked files and directories
echo "Cleaning untracked files..."
git clean -fdx

# Reset any uncommitted changes to tracked files
echo "Resetting uncommitted changes..."
git reset --hard HEAD

# Go back to parent directory
cd -

# Update the submodule to the exact commit specified in the parent repo
echo "Updating submodule to commit specified in parent repo..."
git submodule update --recursive "$SUBMODULE_PATH"

echo "Submodule reset complete - should now show clean status"