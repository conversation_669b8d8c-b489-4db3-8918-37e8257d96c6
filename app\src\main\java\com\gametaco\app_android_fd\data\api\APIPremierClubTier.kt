package com.gametaco.app_android_fd.data.api

enum class APIPremierClubTier(val value: String) {
    BRONZ<PERSON>("BRONZE"),
    SILVER("SILVER"),
    GOLD("GOLD"),
    PLATINUM("PLATINUM");

    val id: String
        get() = value

    val imageName: String
        get() = when (this) {
            com.gametaco.app_android_fd.data.api.APIPremierClubTier.BRONZE -> "PremierClubBronzeSmall"
            com.gametaco.app_android_fd.data.api.APIPremierClubTier.SILVER -> "PremierClubSilverSmall"
            com.gametaco.app_android_fd.data.api.APIPremierClubTier.GOLD -> "PremierClubGoldSmall"
            com.gametaco.app_android_fd.data.api.APIPremierClubTier.PLATINUM -> "PremierClubPlatinumSmall"
        }
}
