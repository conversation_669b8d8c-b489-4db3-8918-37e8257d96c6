package com.worldwinner.fanduel.uitest

import org.junit.jupiter.api.Test

class FanduelAccountUiTest : FanduelUiTestBase() {


    // Logout Flow Tests
    @Test
    fun runToAccount_logout_confirmationDialog_displays() = uiTest {
        uiTestManager.runToAccount_logout_confirmationDialog_displays()
    }

    //
    @Test
    fun runToAccount_logout_confirmationDialog_canBeCancelled() = uiTest {
        uiTestManager.runToAccount_logout_confirmationDialog_canBeCancelled()
    }

    @Test
    fun runToAccount_logout_verifyLoggedOut() = uiTest {
        uiTestManager.runToAccount_logout_verifyLoggedOut()
    }
}