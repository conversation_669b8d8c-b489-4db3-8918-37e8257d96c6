package com.gametaco.app_android_fd.viewmodel

import androidx.lifecycle.ViewModel
import androidx.navigation.NavController
import com.gametaco.app_android_fd.data.navigation.Routes
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

class WebViewViewModel : ViewModel() {

    // URL to be loaded in WebView
    private val _url = MutableStateFlow<String?>(null)
    val url = _url.asStateFlow()

    // WebView visibility state
    private val _isVisible = MutableStateFlow(false)
    val isVisible = _isVisible.asStateFlow()

    fun loadUrl(newUrl: String, navController : NavController) {
        navController.navigate(Routes.WEBVIEW_SCREEN)
        _url.value = newUrl
        _isVisible.value = true
    }

    fun closeWebView() {
        _isVisible.value = false
    }

    companion object {
        // Singleton instance
        val instance: WebViewViewModel by lazy { WebViewViewModel() }
        const val TAG = "AccountViewModel"
    }
}
