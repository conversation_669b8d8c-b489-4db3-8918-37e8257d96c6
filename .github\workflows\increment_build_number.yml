name: Increment Build Number

on:
  workflow_dispatch:

jobs:
  increment-build-number:
    runs-on: self-hosted
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Increment Build Version
        id: increment_version
        run: |
          current_version=$(grep 'BuildVersion' ./app/gradle.properties | cut -d'=' -f2)
          new_version=$((current_version + 1))
          sed -i '' "s/BuildVersion=$current_version/BuildVersion=$new_version/" ./app/gradle.properties
          echo "New BuildVersion: $new_version"
          echo "new_version=$new_version" >> $GITHUB_OUTPUT

      - name: Commit Updated Version
        env:
          NEW_VERSION: ${{ steps.increment_version.outputs.new_version }}
        run: |
          git config --global user.name 'github-actions'
          git config --global user.email '<EMAIL>'
          git add ./app/gradle.properties
          git commit -m "build: set version to $NEW_VERSION"
          git push origin HEAD