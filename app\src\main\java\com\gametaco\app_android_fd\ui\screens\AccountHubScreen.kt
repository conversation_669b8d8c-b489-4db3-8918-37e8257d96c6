package com.gametaco.app_android_fd.ui.screens

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.navigation.NavHostController
import com.fanduel.libs.accounthub.AccountHub
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.viewmodel.AccountViewModel


@Composable
fun AccountHubScreen(navHostController: NavHostController) {

    LaunchedEffect(Unit) {
        FDManager.instance.showAccountHub(navHostController.previousBackStackEntry?.destination?.route ?: Routes.GAMES_SCREEN)
    }

    LoadingScreen()
}