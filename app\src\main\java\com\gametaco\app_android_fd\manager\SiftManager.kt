package com.gametaco.app_android_fd.manager

import com.gametaco.app_android_fd.MainApplication
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.utils.log.Logger
import siftscience.android.Sift

class SiftManager {

    fun initSift(){
        Logger.i("===============Sift init SDK===============")
        Sift.open(
            MainApplication.context, Sift.Config.Builder()
                .withAccountId(AppEnv.current.sift_account_id)
                .withBeaconKey(AppEnv.current.sift_beacon_key)
                .build()
        )
        Sift.collect();
    }
    fun setUserId(id:String){
        Logger.i("swift set user id: $id")
        Sift.setUserId(id)
    }
    fun unsetUserId(){
        Sift.unsetUserId()
    }
    fun pause(){
        Sift.pause()
    }
    fun resume(){
        Sift.resume(MainApplication.context)
    }
    fun close(){
        Sift.close()
    }
}