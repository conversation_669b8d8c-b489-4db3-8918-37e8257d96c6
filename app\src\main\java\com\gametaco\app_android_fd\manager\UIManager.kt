package com.gametaco.app_android_fd.manager

import androidx.compose.ui.graphics.Color
import com.gametaco.app_android_fd.di.resolve
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

enum class SystemBarState {
    Immersive,
    TransparentLightStatusBarIcons,
    TransparentDarkStatusBarIcons,
}

interface UIManager {
    fun setMainTheme()
    fun bringToFront()
    fun openURL(url:String)
    fun openUrlInExternalBrowser(url: String)
    fun showLauncherView()
    fun hideLauncherView()

    fun setSystemBarState(systemBarState: SystemBarState)
    val useDarkStatusBarIcons: StateFlow<Boolean>
    val navigationBarColor: StateFlow<Color>

    companion object {
        val instance: UIManager
            get() = resolve<ActivityManager>().uiManager
    }
}