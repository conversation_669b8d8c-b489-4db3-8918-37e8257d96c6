package com.gametaco.app_android_fd.models

import androidx.compose.runtime.Immutable

@Immutable
data class RewardDataModel(
    val value: Float? = null,
    val title: String,
    val shortDesc: String? = null,
    val longDesc: String? = null,
    val icon: Int? = null,
    val iconDesc: String? = null,
    val currentStepProgress: Int? = null,
    val activeStepIndex: Int? = null,
    val steps: List<RewardStepData>? = null,
    val completedDateTime: String? = null,
    val startDate: String? = null,
    val endDate: String? = null
)

@Immutable
data class RewardStepData(
    val maxProgress: Int,
    val value: Float,
    val stepDesc: String? = null,
//    val completedDateTime: String?
)
