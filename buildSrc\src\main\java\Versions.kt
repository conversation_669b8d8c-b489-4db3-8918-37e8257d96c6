object Versions {
    const val coreKtx = "1.13.1"
    const val kotlin = "2.0.0"
    const val kotlinSerializationJson = "1.7.3"
    const val appcompat = "1.7.0"
    // Note: "3.35.1" may be suggested as the latest version. If you look at
    // https://mvnrepository.com/artifact/com.amplitude/android-sdk and
    // https://github.com/amplitude/Amplitude-Android/tags, you will see that the "3.35.1" label
    // was a typo (it should have been "2.35.1"), and was published on Dec 17, 2021.
    const val amplitude = "2.40.3"
    const val amplitude_experimentation = "1.13.1"
    const val appsFlyer = "6.14.2"
    const val appsFlyerLvl = "6.12.3"
    const val trustly = "3.2.0"
    const val material = "1.9.0"
    const val lifecycleRuntimeKtx = "2.8.1"
    const val activityCompose = "1.9.0"
    const val compose = "2024.06.00"
    /**
     * Note: when upgrading this, make sure to check the `withUnity` flavor.
     * Version 2.7.7 does not work `withUnity`.
     */
    const val navigation = "2.7.0"
    const val lifecycleViewModelKtx = "2.7.0"
    const val jUnit = "4.12"

    const val appium = "9.4.0"
    const val koin = "3.5.6"
    const val logback = "1.4.7"
    const val moshi = "1.15.2"
    const val okio = "3.8.0"
    const val okhttp = "4.12.0"
    const val retrofit = "2.11.0"
    const val selenium = "4.29.0"
    const val slf4j = "2.0.16"
    const val timber = "5.0.1"

    const val coroutines = "1.8.1"

    const val composeMaterial3Icons = "1.5.0"

    const val ndkVersion = "23.1.7779620"
    const val buildToolsVersion = "34.0.0"

    const val exoPlayer = "1.4.1"

    const val googlePlayServicesBase = "18.5.0"
    const val playServicesLocation = "21.0.1"
    const val googlePlayServicesAdsIdentifier = "18.1.0"
    const val playIntegrity = "1.3.0"

    const val coilCompose = "2.6.0"

    const val braze = "31.1.0"
    const val sift = "1.3.0"
    const val lottie = "6.4.1"
    const val perimeterX = "3.3.0"

    const val fd_accounthub = "2.0.3"
    const val fd_accounthub_contract = "1.0.0"
    const val fd_account_contract = "4.2.0"
    const val fd_account_verification_contract = "2.0.0"
    const val fd_corewebview = "3.0.4"
    const val fd_coreconfig = "3.0.0"
    const val fd_ioc = "1.0.0"
    const val fd_corepx = "7.0.0"
    const val fd_commonmodules = "3.1.0"
    const val fd_coredeeplinks = "1.0.0"
    const val fd_coreevents = "3.0.0"
    const val fd_promolinks = "4.0.0"
    const val fd_amplitude_um = "3.0.1"
    const val fd_wallet = "5.0.4"
    const val fd_account = "6.3.0"
    const val fd_account_mfa = "4.0.0"
    const val fd_responsible_gaming = "3.0.0"
    const val fd_coremodalpresenter = "3.1.0"
    const val fd_coreapiidentities = "2.0.1"


    const val fd_salesforce_library = "3.0.2"
    const val fd_salesforce_contract = "1.0.0"//"1.1.0-alpha.1"
    const val salesforce_service_chat_ui = "4.3.6"
}
