package com.gametaco.app_android_fd

import androidx.compose.runtime.Composable
import com.gametaco.app_android_fd.manager.SystemBarState


interface UnityWrapperInterface {

    fun UnityNativeAPI_loadGame(loadString: String)
    fun UnityNativeAPI_startGameReady(startString: String)
    fun UnityNativeAPI_reset()
    fun hideLauncherView()
    fun showLauncherView()
    fun pause()
    fun resume()
    fun setSystemBarState(systemBarState: SystemBarState)
    fun UnityNativeAPI_getReplayFilePath(tournamentInstanceEntryId: String) : String?
    fun UnityNativeAPI_appAction(jsonString: String)
    fun UnityNativeAPI_getDailyRewardFutureTime():String?
    @Composable
    fun AppEntryPoint()


}