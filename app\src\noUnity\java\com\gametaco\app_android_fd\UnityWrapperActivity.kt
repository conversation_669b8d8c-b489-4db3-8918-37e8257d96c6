package com.gametaco.app_android_fd

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.Composable
import com.gametaco.app_android_fd.manager.ActivityManager
import com.gametaco.app_android_fd.manager.SystemBarState
import org.koin.android.ext.android.inject

open class UnityWrapperActivity : AppCompatActivity(), UnityWrapperInterface {

    companion object {
        const val TAG = "UnityWrapperActivity"
    }

    val activityManager: ActivityManager by inject()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            AppEntryPoint()
        }
    }

    @Composable
    override fun AppEntryPoint() {
    }


    override fun UnityNativeAPI_loadGame(loadString: String) {
    }

    override fun UnityNativeAPI_startGameReady(startString: String) {
    }

    override fun UnityNativeAPI_reset() {
    }

    override fun hideLauncherView() {
    }

    override fun showLauncherView() {
    }

    override fun pause() {
    }

    override fun resume() {
    }

    override fun setSystemBarState(systemBarState: SystemBarState) {

    }

    override fun UnityNativeAPI_getReplayFilePath(tournamentInstanceEntryId: String) : String?
    {
        return null
    }
}