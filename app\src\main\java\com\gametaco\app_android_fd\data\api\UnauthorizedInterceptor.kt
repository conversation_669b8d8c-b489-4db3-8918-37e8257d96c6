package com.gametaco.app_android_fd.data.api

import com.gametaco.app_android_fd.manager.AlertDialogManager
import com.gametaco.app_android_fd.manager.OnSessionChangedEvent
import okhttp3.Interceptor
import okhttp3.Response
import org.greenrobot.eventbus.EventBus

class UnauthorizedInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)

        if (response.code == 401) {
            AlertDialogManager.instance.showDialog("An Error Has Occurred", "To continue playing, please re-log in to your account", "Got it", {
                EventBus.getDefault().post(OnSessionChangedEvent(false, "UnauthorizedInterceptor"))
            } )

        }

        return response
    }
}