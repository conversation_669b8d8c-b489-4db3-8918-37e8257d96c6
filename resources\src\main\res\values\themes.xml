<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar" >
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@drawable/splash_drawable</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowTranslucentStatus">true</item>
    </style>
    
    <style name="AWToolbarStyleText" parent="TextAppearance.AppCompat.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">#FFFFFF</item>
    </style>

    <style name="AWToolbarStyleNavBtn" parent="@style/Widget.AppCompat.Toolbar.Button.Navigation">
        <item name="android:padding">10dp</item>
        <item name="tint">#fff</item>
    </style>

    <style name="AWToolbarStyle">
        <item name="android:background">#0d7fe1</item>
        <item name="android:height">56dp</item>
        <item name="navigationIcon">@drawable/aw_webview_ic_cross</item>

        <item name="navigationContentDescription">Close</item>

        <item name="contentInsetStart">16dp</item>
        <item name="titleTextAppearance">@style/AWToolbarStyleText</item>
        <item name="toolbarNavigationButtonStyle">@style/AWToolbarStyleNavBtn</item>
    </style>
    <style name="aw_dialog_style" parent="ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:background">#0d7fe1</item>
        <item name="android:textColor">#FFFFFF</item>
    </style>
    <style name="CustomWebViewTheme" parent="@style/Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:dialogTheme">@style/aw_dialog_style</item>
    </style>
</resources>