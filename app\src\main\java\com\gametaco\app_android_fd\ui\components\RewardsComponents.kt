package com.gametaco.app_android_fd.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBackIosNew
import androidx.compose.material3.Button
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.gametaco.app_android_fd.LocalNavManager
import com.gametaco.app_android_fd.data.entity.APIActiveGoal
import com.gametaco.app_android_fd.data.entity.APIActiveGoalClaimState
import com.gametaco.app_android_fd.data.entity.APICompletableGoal
import com.gametaco.app_android_fd.data.entity.APIGoalDetail
import com.gametaco.app_android_fd.data.entity.APIGoalRewardType
import com.gametaco.app_android_fd.data.entity.APIGoalType
import com.gametaco.app_android_fd.data.entity.APIGoalsCollectableGoal
import com.gametaco.app_android_fd.data.entity.APIGoalsTask
import com.gametaco.app_android_fd.data.entity.APIPostGoalsCollectableResponse
import com.gametaco.app_android_fd.data.entity.DailyRewardInfoType
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.FDWebviewPage
import com.gametaco.app_android_fd.ui.modifiers.clickableWithoutRipple
import com.gametaco.app_android_fd.ui.theme.AppFont
import com.gametaco.app_android_fd.ui.theme.GreenMinimalBackground
import com.gametaco.app_android_fd.ui.theme.theme_light_background
import com.gametaco.app_android_fd.utils.getLeftTimeString
import com.gametaco.app_android_fd.utils.parseHtml
import com.gametaco.app_android_fd.utils.toDate
import com.gametaco.app_android_fd.utils.toDollarString
import com.gametaco.app_android_fd.viewmodel.GamesViewModel
import com.gametaco.app_android_fd.viewmodel.GoalsViewModel
import com.gametaco.app_android_fd.viewmodel.LeaderboardViewModel
import com.gametaco.app_android_fd.viewmodel.PlayViewModel
import com.gametaco.utilities.STR
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import resources.R
import java.util.Date

@Composable
fun RewardsInfoTitle(
    primaryTitle: String,
    secondaryTitle: String,
    subTitle: String,
    isCluster: Boolean
) {
    val backgroundModifier = if (isCluster) {
        Modifier.background(
            GreenMinimalBackground,
            RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp)
        )
    } else {
        Modifier.background(
            MaterialTheme.colorScheme.surface,
            RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp)
        )
    }

    Column (
        modifier = Modifier
            .fillMaxWidth()
            .then(backgroundModifier)
            .padding(16.dp, 14.dp)
    ) {
        if (isCluster) {
            Text(
                text = primaryTitle,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onTertiary
            )
            Spacer(modifier = Modifier.height(6.dp))
            Text(
                text = secondaryTitle.uppercase(),
                style = MaterialTheme.typography.headlineSmall.copy(letterSpacing = 1.sp),
                color = MaterialTheme.colorScheme.onTertiary
            )
            Spacer(modifier = Modifier.height(10.dp))
            Text(
                text = subTitle.parseHtml(),
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.onTertiary
            )
        } else {
            Text(
                text = primaryTitle,
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.tertiary
            )
            Spacer(modifier = Modifier.height(6.dp))
            Text(
                text = secondaryTitle,
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onSurface
            )
            Spacer(modifier = Modifier.height(10.dp))
            Text(
                text = subTitle.parseHtml(),
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

@Composable
fun RewardsInfoBody(bodyText: String){
    Text(
        text = bodyText,
        style = MaterialTheme.typography.headlineMedium,
        color = MaterialTheme.colorScheme.onSurface
    )
}

@Composable
fun ClaimedRewardElement(title: String, timeStamp: String, value: String,rewardType: DailyRewardInfoType = DailyRewardInfoType.CASH, eligible:Boolean = true, showDivider:Boolean = true) {
    Column()
    {
        Row (
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp, 12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column (
                modifier = Modifier.weight(1f, false),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                AutosizeText(
                    text = title,
                    style = MaterialTheme.typography.bodySmall,
                    maxLines = 1
                )
                Text(
                    text = timeStamp,
                    style = MaterialTheme.typography.bodySmall
                )
            }
            
            Spacer(modifier = Modifier.width(6.dp))

            if (eligible) {
                Box(
                    modifier = Modifier
                        .background(
                            if(rewardType == DailyRewardInfoType.CASH) MaterialTheme.colorScheme.tertiary else Color(0xFF005FC8),
                            RoundedCornerShape(8.dp)
                        )
                ) {
                    Box (modifier = Modifier.padding(horizontal = 6.dp,3.dp), contentAlignment = Alignment.Center) {
                        Text(
                            text = value.toDollarString() + if(rewardType == DailyRewardInfoType.CASH) "" else " Entry",
                            style = MaterialTheme.typography.headlineLarge.copy(fontSize = 10.sp),
                            textAlign = TextAlign.Center,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onTertiary,
                            softWrap = false
                        )
                    }
                }
            } else {
                Box(
                    modifier = Modifier
                        .background(Color(0xFFC9D1DB), RoundedCornerShape(8.dp))
                ) {
                    Box (modifier = Modifier.padding(6.dp,3.dp), contentAlignment = Alignment.Center) {
                        Text(
                            text = "Not eligible",
                            style = MaterialTheme.typography.headlineLarge,
                            textAlign = TextAlign.Center,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF131314)
                        )
                    }
                }
            }
        }

        if (showDivider){
            HorizontalDivider(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.outlineVariant),
                thickness = 1.dp,
                color = MaterialTheme.colorScheme.outline
            )
        }
    }
}
@Composable
fun ClusterRewardsTimeShow(task: APIGoalsTask?){
//    println("task.is_active ? ${task?.is_active}")
    if(task != null && (task.starts_at != null || task.ends_at != null)){
        Row(modifier = Modifier
            .offset(0.dp, (0).dp)){

            Image(
                modifier = Modifier
                    .size(15.dp)
                    .offset(0.dp, 0.dp),
                painter = painterResource(if(task.isLocked) R.drawable.ic_lock else R.drawable.ic_clock),
                contentDescription = null,
                alignment = Alignment.Center
            )
            val now = Date()
            val start = task.starts_at?.toDate()
            val end = task.ends_at?.toDate()
            var desc = ""
            if(start != null && now.before(start)){
                desc = "UNLOCKS IN ${start.getLeftTimeString()}"
            }else if(end != null && now.before(end)){
                desc = "EXPIRES IN ${end.getLeftTimeString()}"
            }else{
                desc = "EXPIRED"
            }
//            println("task.ends_at ${task.ends_at}   task.starts_at${task.starts_at}")
            Text(
                text = desc,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.labelSmall.copy(fontSize = 14.sp),
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.align(Alignment.CenterVertically)
            )
        }
    }
}
@Composable
fun ActiveRewardElement(
    goalDataModel: APIActiveGoal,
    modifier: Modifier = Modifier,
    showInfo:Boolean = true,
    fromLeaderboard:Boolean = false,
    clusterTaskId:String? = null,
    onClaimClicked: (callback: (res: APIPostGoalsCollectableResponse)->Unit)->Unit = {},
    onClaimComplete: (success: Boolean)->Unit = {},
    onInfoClicked: ()->Unit = {},
) {
    var icon = goalDataModel.icon

    var rewardType = when(goalDataModel.rewardType){
        APIGoalRewardType.faceoff -> R.string.bonus_cash
        APIGoalRewardType.sportsbook -> R.string.sports_book_bonus_bet
        APIGoalRewardType.freebetbonus -> R.string.sports_book_bonus_bet
        else -> R.string.bonus_cash
    }
    var valueTitle = STR(
        rewardType,
        goalDataModel.cashString
    )

    if(goalDataModel.has_ticket_reward == true){
        icon = "FREE_TICKET_REWARDS"
        valueTitle = "FREE Game Entry"
    }

    var rewardTitle = goalDataModel.titleString
    var subTitle =  goalDataModel.subTitleString
    var claimable = goalDataModel.isComplete
    var currentStepProgress = goalDataModel.currentProgress
    var currentStepMaxProgress = goalDataModel.requiredProgress
    var currentStep = goalDataModel.currentStep
    var totalStepCount = goalDataModel.requiredStep
    var taskData:APIGoalsTask? = null
    if(goalDataModel.goalType == APIGoalType.cluster){
        rewardTitle = goalDataModel.cluster_title ?:""
        subTitle = goalDataModel.task_title ?:""
        if(clusterTaskId != null){
            taskData = goalDataModel.sortedTasks.find { it.goal_user_instance_task_id == clusterTaskId }
            if(taskData != null){
                subTitle = taskData.title ?:""
                claimable = taskData.isComplete
                currentStepProgress = taskData.current_progress?.toDouble()?.toInt() ?:0
                currentStepMaxProgress= taskData.required_progress.toDouble().toInt()
                currentStep = 1
                totalStepCount = 1
            }
        }
    }


    val showSlideTransition : MutableTransitionState<Boolean>  = remember { MutableTransitionState(true) }
    val claimState by goalDataModel.claimState.collectAsState()

    val showClaimBtn = claimable && claimState != APIActiveGoalClaimState.Complete
    val showClaimAnimation = claimable && claimState == APIActiveGoalClaimState.In_Progress
    val backgroundColor = if (claimable) {
        if(claimState == APIActiveGoalClaimState.Complete){
            MaterialTheme.colorScheme.outlineVariant
        }else{
            MaterialTheme.colorScheme.tertiaryContainer
        }
    }else {
        MaterialTheme.colorScheme.surface
    }

    AnimatedVisibility(
        visibleState = showSlideTransition,
        exit = fadeOut(animationSpec = tween(durationMillis = 1000)) + slideOutVertically(
            targetOffsetY = { fullHeight -> fullHeight * 2 },
            animationSpec = tween(durationMillis = 1000),
        ),
        modifier = modifier,
    ) {
        ContentBox (
            bodyPaddingValues = PaddingValues(),
            cornerRoundness = if (fromLeaderboard) 0.dp else 4.dp,
            boarderSize = if(fromLeaderboard) 0.dp else 1.dp
        ) {
            Box(
                modifier = Modifier
                    .background(backgroundColor)
                    .padding(
                        start = if (!claimable || claimState == APIActiveGoalClaimState.Complete) 8.dp
                        else 16.dp,
                        top = 16.dp,
                        end = 16.dp,
                        bottom = 16.dp
                    )
            ) {
                if (showInfo && !claimable) {
                    Row(modifier = Modifier
                        .align(Alignment.TopEnd)
                        .offset(0.dp, (0).dp)){
//                        if(goalDataModel is APICompletableGoal && goalDataModel.goalType != APIGoalType.cluster){
//                            Image(
//                                modifier = Modifier
//                                    .size(15.dp)
//                                    .offset(0.dp, 0.dp),
//                                painter = painterResource(R.drawable.ic_clock),
//                                contentDescription = null,
////                                colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.secondaryContainer),
//                                alignment = Alignment.Center
//                            )
//                            Text(
//                                text = " ${goalDataModel.ends_at?.toDate()?.getLeftTimeString() ?: "0S"} ",
//                                textAlign = TextAlign.Center,
//                                style = MaterialTheme.typography.labelSmall.copy(fontSize = 14.sp),
//                                color = MaterialTheme.colorScheme.onSurface,
//                                modifier = Modifier.align(Alignment.CenterVertically)
//                            )
//                        }
                        Image(
                            modifier = Modifier
                                .size(15.dp)
                                .clip(RoundedCornerShape(10.dp))
                                .clickable(
                                    enabled = true,
                                    onClick = onInfoClicked
                                ),
                            painter = painterResource(R.drawable.ic_info),
                            contentDescription = null,
                            colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.secondaryContainer),
                            alignment = Alignment.Center
                        )
                    }
                }
                Row(
                    horizontalArrangement = Arrangement.spacedBy(
                        8.dp,
                        Alignment.CenterHorizontally
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(IntrinsicSize.Min)
                ) {
                    if (!claimable || claimState == APIActiveGoalClaimState.Complete) {
                        Column(
                            verticalArrangement = Arrangement.SpaceBetween,
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = Modifier
                                .fillMaxHeight()
                                .width(IntrinsicSize.Min)
                        ) {
                            Box(modifier = Modifier.size(68.dp)) {
                                var iconColor =
                                    if (claimState == APIActiveGoalClaimState.Complete) {
                                        MaterialTheme.colorScheme.tertiary
                                    } else {
                                        MaterialTheme.colorScheme.primary
                                    }
                                var iconSize = 30.dp
                                Image(
                                    modifier = Modifier.fillMaxSize(),
                                    painter = painterResource(R.drawable.ic_reward_base),
                                    contentDescription = "Reward Icon Base",
                                    colorFilter = ColorFilter.tint(iconColor)
                                )
                                if (icon == "FIRST_TIME_DEPOSIT_REWARDS") {
                                    iconColor = Color.White
                                    iconSize = 36.dp
                                }
                                Image(
                                    modifier = Modifier
                                        .size(iconSize)
                                        .align(Alignment.Center)
                                        .offset(0.dp, (-3).dp),
                                    contentScale = ContentScale.FillBounds,
                                    painter = painterResource(getIcon(icon)),
                                    contentDescription = null,
                                    colorFilter = ColorFilter.tint(iconColor)
                                )
                            }

                            if (totalStepCount > 1) {
                                val stepNumString =
                                    STR(R.string.step_caps, currentStep, totalStepCount)
                                Box(
                                    modifier = Modifier
                                        .offset(y = 3.dp)
                                ) {
                                    Box(
                                        modifier = Modifier
                                            .height(14.dp)
                                            .background(
                                                MaterialTheme.colorScheme.secondaryContainer,
                                                RoundedCornerShape(3.dp)
                                            )
                                            .align(Alignment.BottomCenter)
                                    ) {
                                        Text(
                                            text = stepNumString,
                                            textAlign = TextAlign.Center,
                                            color = MaterialTheme.colorScheme.onPrimary,
                                            style = MaterialTheme.typography.labelLarge,
                                            modifier = Modifier
                                                .align(Alignment.Center)
                                                .padding(horizontal = 4.dp)
                                                .padding(top = 1.dp)
                                        )
                                    }
                                }
                            }
                        }
                    }

                    Column(
                        verticalArrangement = Arrangement.spacedBy(6.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier
                                    .weight(weight = 1f)
                            ) {
                                Box(
                                    modifier = Modifier
                                        .background(
                                            if(goalDataModel.has_ticket_reward == true) Color(0xFF0070EB) else MaterialTheme.colorScheme.tertiary,
                                            RoundedCornerShape(8.dp)
                                        )
                                )
                                {
                                    Box (modifier = Modifier.padding(horizontal = 6.dp,3.dp), contentAlignment = Alignment.Center) {
                                        Text(
                                            text = valueTitle,
                                            style = MaterialTheme.typography.headlineLarge,
                                            textAlign = TextAlign.Center,
                                            fontWeight = FontWeight.Bold,
                                            color = MaterialTheme.colorScheme.onTertiary,
                                        )
                                    }
                                }
                                Spacer(modifier = Modifier.height(4.dp))
                                Text(
                                    text = rewardTitle ?:"",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                                Spacer(modifier = Modifier.height(6.dp))
                                Text(
                                    text = subTitle.parseHtml(),
                                    style = MaterialTheme.typography.headlineMedium,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                            }

                            if (showClaimBtn) {
                                Box(
                                    modifier = Modifier
                                        .width(66.dp)
                                        .height(36.dp)
                                        .padding(horizontal = 4.dp)
                                ) {
                                    CommonButton(
                                        title = "Claim",
                                        fillColor = if (!showClaimAnimation) {
                                            MaterialTheme.colorScheme.tertiary
                                        } else {
                                            MaterialTheme.colorScheme.outlineVariant
                                        },
                                        enabled = !showClaimAnimation
                                    ) {
                                        onClaimClicked { res ->
                                            val success = res.was_eligible_to_claim
                                            if (success) {
                                                goalDataModel.claimState.value =
                                                    APIActiveGoalClaimState.In_Progress
                                            } else {
                                                goalDataModel.eligibleToClaim = false
                                            }

                                            CoroutineScope(Dispatchers.IO).launch {
                                                if (success) Thread.sleep(2000)
                                                goalDataModel.claimState.value =
                                                    APIActiveGoalClaimState.Complete
                                                if (!fromLeaderboard) {
                                                    showSlideTransition.targetState = false
                                                }
                                                if (success) Thread.sleep(1000)
                                                onClaimComplete(success)

                                                if(res.reward?.tournament_ticket != null){
                                                    PlayViewModel.instance.jumpToPlayView(res.reward.tournament_ticket.game_id)
                                                }
                                            }
                                        }
                                    }

                                    if (showClaimAnimation) {
                                        CustomAnimationView(
                                            animation = R.raw.anim_confetti_burst,
                                            animationDidFinish = {

                                            },
                                            modifier = Modifier
                                                .wrapContentSize(unbounded = true)
                                                .size(150.dp)
                                        )
                                    }
                                }
                            }
                        }

                        Box(
                            modifier = Modifier
                                .padding(
                                    end = if (
                                        claimable &&
                                        claimState != APIActiveGoalClaimState.Complete
                                    ) 76.dp else 0.dp
                                )
                        ) {
                            if (!claimable || showClaimAnimation) {
//                                println("goalDataModel.gameId " + goalDataModel.gameId + " >>" + taskData?.game_id)
                                RewardsProgressBar(
                                    task = taskData,
                                    currentStepProgress = currentStepProgress,
                                    currentStepMaxProgress = currentStepMaxProgress,
                                    isClaimed = fromLeaderboard,
                                    progressBarColor = if (
                                        claimState != APIActiveGoalClaimState.Not_Claim
                                    )
                                        MaterialTheme.colorScheme.tertiary
                                    else Color(
                                        0xFF005FC8
                                    ),
                                    ctaString = goalDataModel.ctaString,
                                    link = goalDataModel.linkString,
                                    gameId = taskData?.game_id ?: goalDataModel.gameId
                                )
                            } else {
                                RewardsProgressBar(
                                    task = null,
                                    currentStepProgress = currentStepProgress,
                                    currentStepMaxProgress = currentStepMaxProgress,
                                    showSteps = totalStepCount > 1,
                                    currentStep = currentStep,
                                    totalStepCount = totalStepCount,
                                    ctaString = goalDataModel.ctaString,
                                    link = goalDataModel.linkString,
                                    gameId = goalDataModel.gameId
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}
@Preview
@Composable
fun ActiveRewardElementPreview(){
    Column(modifier = Modifier.padding(18.dp)) {
        ActiveRewardElement(
            goalDataModel = APIGoalsCollectableGoal(
                "1",
                "123",
                APIGoalDetail("title","subtitle","desc","type",null,"icon","reward_type", null, null),
                "task_title",
                "1.00",
                "1",
                "",
                1,
                false,
                "false",
                null,
                null,
                has_ticket_reward = false,
                has_tag_reward = false,
            ),
            modifier = Modifier,
            showInfo = true,
            fromLeaderboard = false,
            onClaimClicked = { callback ->
                callback(APIPostGoalsCollectableResponse(true, null))
            }
        )
    }

}

@Composable
fun RewardsProgressBar(
    task: APIGoalsTask?,
    currentStepProgress: Int,
    currentStepMaxProgress: Int,
    showSteps: Boolean = false,
    currentStep: Int = 1,
    totalStepCount: Int = 1,
    spacerHeight: Dp = 4.dp,
    isLocked: Boolean = false,
    progressBarColor: Color = Color(0xFF005FC8),
    isClaimed: Boolean = false,
    ctaString: String,
    link:String,
    gameId:String?,
    goalInfoModalExpanded: MutableState<Boolean>? = null
) {
    val navManager = LocalNavManager.current
    Column (verticalArrangement = Arrangement.spacedBy(spacerHeight, Alignment.Top)) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (currentStepProgress >= currentStepMaxProgress) {
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 4.dp),
                    text = STR(if (isClaimed) R.string.claimed_caps else R.string.completed_caps),
                    style = MaterialTheme.typography.headlineSmall,
                    textAlign = TextAlign.Left,
                    color = progressBarColor
                )
            }
            else if (isLocked) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(0.dp, 4.dp),
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.Bottom
                ) {
                    Image(
                        modifier = Modifier.size(12.dp),
                        painter = painterResource(R.drawable.ic_lock),
                        contentDescription = STR(R.string.lock_icon),
                    )
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = STR(R.string.locked_caps),
                        style = MaterialTheme.typography.headlineSmall,
                        textAlign = TextAlign.Left,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
            }
            else if(task?.ends_at?.toDate()?.before(Date()) == true){
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 4.dp),
                    text = STR(R.string.expired_caps),
                    style = MaterialTheme.typography.headlineSmall,
                    textAlign = TextAlign.Left,
//                    color = progressBarColor
                )
            }
            else {
                Box(modifier = Modifier.fillMaxWidth()){
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "$currentStepProgress / $currentStepMaxProgress",
                            style = MaterialTheme.typography.headlineSmall,
                        )
                        val onClick: ()->Unit = {
                            if(goalInfoModalExpanded != null){
                                goalInfoModalExpanded.value = false
                            }

                            LeaderboardViewModel.instance.closeLeaderboard()
                            when (link){
                                "HOME_LOBBY" -> {
                                    navManager.navigate(Routes.GAMES_SCREEN)
                                }
                                "GAME_LOBBY" -> {
                                    if (!gameId.isNullOrEmpty()) {
                                        PlayViewModel.instance.setGameData(
                                            gameData = GamesViewModel.instance.getGCGameDataForGameId(gameId),
                                            game = GamesViewModel.instance.getGameDataById(gameId)
                                        )
                                        navManager.navigate(Routes.CONTEST_SCREEN)
                                    }
                                    else {
                                        navManager.navigate(Routes.GAMES_SCREEN)
                                    }
                                }
                                "ADD_FUNDS" -> {
                                    FDManager.instance.showWebviewFD(FDWebviewPage.Deposit)
                                }
                            }
                        }
                        if(task?.isLocked != true){
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.offset(2.dp, 0.dp)
                            ){
                                LinkedText(
                                    title = ctaString,
                                    style = MaterialTheme.typography.headlineMedium.copy(
                                        color = MaterialTheme.colorScheme.primary,
                                        fontWeight = FontWeight.Bold,
                                    ),
                                    onClick = onClick
                                )
                                Box(
                                    modifier = Modifier
                                        .size(16.dp)
                                        .clickable(onClick = onClick),
                                ) {
                                    Icon(
                                        imageVector = Icons.Filled.ArrowBackIosNew,
                                        tint = MaterialTheme.colorScheme.primary,
                                        contentDescription = null,
                                        modifier = Modifier
                                            .size(10.dp)
                                            .rotate(degrees = 180f)
                                            .align(Alignment.Center)
                                            .offset(0.dp, 1.dp),
                                    )
                                }
                            }
                        }
                    }

                    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center){
                        ClusterRewardsTimeShow(task = task)
                    }
                }
            }
        }
        Row {
            if (showSteps && totalStepCount > 1) {
                val stepNumString = "STEP $currentStep / $totalStepCount"
                Box(
                    modifier = Modifier
                        .padding(end = 8.dp)
                        .offset(y = 3.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .height(14.dp)
                            .background(
                                MaterialTheme.colorScheme.secondaryContainer,
                                RoundedCornerShape(3.dp)
                            )
                            .align(Alignment.BottomCenter)
                    ) {
                        Text(
                            text = stepNumString,
                            textAlign = TextAlign.Center,
                            color = MaterialTheme.colorScheme.onPrimary,
                            style = MaterialTheme.typography.headlineSmall,
                            modifier = Modifier
                                .align(Alignment.Center)
                                .padding(horizontal = 4.dp)
                                .padding(top = 1.dp)
                        )
                    }
                }
            }

            ProgressBar(
                modifier = Modifier
                    .height(8.dp)
                    .fillMaxWidth(),
                currentStep = currentStepProgress,
                maxSteps = currentStepMaxProgress,
                barColor = progressBarColor
            )
        }
    }
}

@Composable
fun RewardsStepList(
    steps: List<APIGoalsTask>,
    activeStepIndex: Int,
    currentStepProgress: Int,
    isCluster: Boolean
){
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        for (index in steps.indices) {
            val step = steps[index]
            val maxProgress = step.required_progress.toFloat().toInt()

            if (index > 0){
                HorizontalDivider(thickness = 1.dp, color = MaterialTheme.colorScheme.outline)
            }

            if (isCluster){
                val thisStepProgress = step.current_progress?.toFloat()?.toInt() ?: 0
                val isComplete = thisStepProgress >= maxProgress

                ClusterRewardsStepListElement(
                    task = step,
                    value = step.reward ?: "",
                    title = step.title ?: "",
                    currentStep = if (isComplete) maxProgress else thisStepProgress,
                    maxSteps = maxProgress,
                    isCompleted = isComplete,
                    ctaString = step.cta,
                    linkString = step.link,
                    gameId = step.game_id
                )
            } else {
                val isActiveStep = activeStepIndex == index
                val isComplete = activeStepIndex > index

                RewardsStepListElement(
                    task = step,
                    stepNumber = index + 1,
                    title = step.reward ?: "",
                    subTitle = step.title ?: "",
                    currentStep = if (isActiveStep) currentStepProgress else if (isComplete) maxProgress else 0,
                    maxSteps = maxProgress,
                    isLocked = !isActiveStep && !isComplete,
                    ctaString = step.cta,
                    linkString = step.link,
                    gameId = step.game_id
                )
            }
        }
    }
}

@Composable
fun RewardsStepListElement(
    task: APIGoalsTask?,
    stepNumber: Int,
    title: String,
    subTitle: String,
    currentStep: Int,
    maxSteps: Int,
    isLocked: Boolean,
    ctaString: String,
    linkString: String,
    gameId: String?,
){
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                if (isLocked) MaterialTheme.colorScheme.background else MaterialTheme.colorScheme.surface
            )
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(2.dp),
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 16.dp, end = 16.dp, top = 10.dp, bottom = 10.dp)
        ) {
            Text(
                text = getBonusCashValueTitle(title),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.tertiary
            )
            Text(
                text = subTitle,
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Spacer(modifier = Modifier) // Adding an extra spacedBy increment before the Row
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp, Alignment.CenterHorizontally),
                verticalAlignment = Alignment.Bottom,
                modifier = Modifier.fillMaxWidth()
            ) {
                val stepNumString = STR(R.string.step_number_caps, stepNumber)
                Box(
                    modifier = Modifier
                        .width(IntrinsicSize.Min)
                        .offset(0.dp, (2).dp)
                ) {
                    Box(
                        modifier = Modifier
                            .height(12.dp)
                            .background(
                                MaterialTheme.colorScheme.secondaryContainer,
                                RoundedCornerShape(3.dp)
                            )
                            .align(Alignment.BottomCenter)
                            .fillMaxWidth()
                    ) {
                        Text(
                            text = stepNumString,
                            textAlign = TextAlign.Center,
                            color = MaterialTheme.colorScheme.onPrimary,
                            style = MaterialTheme.typography.labelLarge,
                            modifier = Modifier
                                .width(42.dp)
                                .align(Alignment.Center)
                        )
                    }
                }

                RewardsProgressBar(
                    task = task,
                    currentStepProgress = currentStep,
                    currentStepMaxProgress = maxSteps,
                    spacerHeight = 5.dp,
                    isLocked = isLocked,
                    ctaString = ctaString,
                    link = linkString,
                    gameId = gameId
                )
            }
        }
    }
}

@Composable
fun ClusterRewardsStepListElement(
    task: APIGoalsTask?,
    value: String,
    title: String,
    currentStep: Int,
    maxSteps: Int,
    isCompleted: Boolean,
    ctaString: String,
    linkString: String,
    gameId: String?,
){
    val valueTitle = value.toDollarString()
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                if (isCompleted || task?.isExpired == true || task?.isLocked == true) MaterialTheme.colorScheme.background else MaterialTheme.colorScheme.surface
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 16.dp, end = 12.dp, top = 10.dp, bottom = 10.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.weight(0.75f)
                )
                Box(
                    modifier = Modifier
                        .padding(start = 6.dp)
                        .background(
                            MaterialTheme.colorScheme.tertiary,
                            RoundedCornerShape(8.dp)
                        )
                ) {
                    Box(
                        modifier = Modifier.padding(horizontal = 6.dp, 3.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = valueTitle,
                            style = MaterialTheme.typography.headlineLarge.copy(fontSize = 10.sp),
                            textAlign = TextAlign.Center,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            color = MaterialTheme.colorScheme.onTertiary,
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(10.dp))
            Box(
                modifier = Modifier.padding(end = 4.dp)
            )
            {
                RewardsProgressBar(
                    task = task,
                    currentStepProgress = currentStep,
                    currentStepMaxProgress = maxSteps,
                    spacerHeight = 8.dp,
                    ctaString = ctaString,
                    link = linkString,
                    gameId = gameId
                )
            }
        }
    }
}

@Composable
fun RewardsBonusCashDisplay(
    value: String
) {
    ContentBox(
        bodyPaddingValues = PaddingValues(16.dp)
    ){
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = "Bonus",
                color = Color(0xFF05285A),
                style = MaterialTheme.typography.titleSmall,
            )
            Text(
                text = value,
                color = Color(0xFF128000),
                style = MaterialTheme.typography.titleLarge.copy(fontSize = 24.sp),
            )
        }
    }
}

@Composable
fun DailyRewardsDisplay(){
    val dailyDoorStatus by GoalsViewModel.instance.dailyDoorStatus.collectAsState()
    val dailyRewardAvailable by GoalsViewModel.instance.isDailyRewardAvailable.collectAsState()
    val current_streak = dailyDoorStatus?.current_streak ?:0
    val nextRewardDate = dailyDoorStatus?.nextRewardDate ?: Date(System.currentTimeMillis() + 60 * 60 * 1000)// Default to 1 hour from now
    val nextRewardExpiresDate = dailyDoorStatus?.streakExpiresDate ?: Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000)// Default to 24 hours from now
    val maxStreak = 21 //21 for MVP
//    val current_streak = dailyDoorStatus?.current_streak ?:3
//    val nextRewardDate = dailyDoorStatus?.nextRewardDate ?: Date(Date().time + 3600000)

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.radialGradient(
                    colors = listOf(
                        Color(0xFF01CDCF),
                        Color(0xFF0070EB)
                    ),
                    center = Offset(0f, 0f),
                    radius = 100.dp.value * 2.0f
                ),
                shape = RoundedCornerShape(4.dp)
            )
            .clip(RoundedCornerShape(4.dp))
            .border(
                width = 1.dp,
                color = Color(0xFFB0B7BF),
                shape = RoundedCornerShape(4.dp)
            )
    ) {

        Image(
            painter = painterResource(id = R.drawable.img_dailyreward_part_2),
//                alignment = Alignment.TopStart,
            contentDescription = null,
            contentScale = ContentScale.Fit,
            modifier = Modifier
                .size(338.dp * 0.3f, 120.dp * 0.3f)
                .align(Alignment.TopStart)
        )
        Image(
            painter = painterResource(id = R.drawable.img_dailyreward_part_1),
//                alignment = Alignment.BottomStart,
            contentDescription = null,
            contentScale = ContentScale.Fit,
            modifier = Modifier
                .size(251.dp * 0.3f, 234.dp * 0.3f)
                .align(Alignment.BottomStart)
        )
        Image(
            painter = painterResource(id = R.drawable.img_dailyreward_part_3),
//                alignment = Alignment.TopEnd,
            contentDescription = null,
            contentScale = ContentScale.Fit,
            modifier = Modifier
                .size(138.dp * 0.3f, 108.dp * 0.3f)
                .align(Alignment.TopEnd)
        )
        Image(
            painter = painterResource(id = R.drawable.img_dailyreward_part_4),
//                alignment = Alignment.BottomEnd,
            contentDescription = null,
            contentScale = ContentScale.Fit,
            modifier = Modifier
                .size(222.dp * 0.3f, 204.dp * 0.3f)
                .align(Alignment.BottomEnd)
        )

        if(dailyRewardAvailable){
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(12.dp)
            ) {
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(bottom = 10.dp)
                ) {
                    Text(
                        text = "DAILY",
                        color = Color.White,
                        fontFamily = AppFont.Knowkout,
                        fontSize = 52.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.shadow(10.dp)
                    )
                    Text(
                        text = "REWARDS",
                        color = Color(0xFFFFDC2E),
                        fontFamily = AppFont.Knowkout,
                        fontSize = 52.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.shadow(10.dp)
                    )
                }
                Box(modifier = Modifier
                    .padding(horizontal = 6.dp)
                    .padding(bottom = 8.dp)){

                    CommonButton(title="Claim Now", height = 40f, textColor = Color(0xFF011638), titleStyle = MaterialTheme.typography.labelLarge.copy(
                        fontSize = 14.sp
                    ), fillColor = Color(0xFFFFDC2E)) {
                        GoalsViewModel.instance.openDailyRewardInUnity()
                    }
                }
                // Only show if we have a streak and not expired
                if(current_streak >= 0 && nextRewardExpiresDate.after(Date())){
                    val timeRemaining = nextRewardExpiresDate.time - Date().time
                    val totalMinutes = timeRemaining / 1000 / 60
                    val hours = totalMinutes / 60
                    val minutes = totalMinutes % 60

                    Text(text = "${current_streak} Day Streak", style = MaterialTheme.typography.labelLarge.copy(
//                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    ), color = Color.White,modifier = Modifier
                        .padding(bottom = 4.dp) )

                    Text(text = "TIME LEFT TO CLAIM: ${hours}H ${minutes} M", style = MaterialTheme.typography.labelSmall.copy(
                        fontSize = 12.sp
                    ), color = Color.White)
                }
            }
        }else{
            Column(modifier = Modifier
                .padding(12.dp)
                .clickableWithoutRipple {
                    GoalsViewModel.instance.openDailyRewardInUnity()
                }) {
                Row(horizontalArrangement = Arrangement.SpaceBetween, verticalAlignment = Alignment.CenterVertically, modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = 12.dp)) {
                    Column {
                        Row(horizontalArrangement = Arrangement.Center, verticalAlignment = Alignment.CenterVertically, modifier = Modifier.padding(bottom = 4.dp)) {
                            Text(
                                text = "DAILY",
                                color = Color.White,
                                fontFamily = AppFont.Knowkout,
                                fontSize = 30.sp,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.shadow(10.dp)
                            )
                            Text(
                                text = "REWARDS",
                                color = Color(0xFFFFDC2E),
                                fontFamily = AppFont.Knowkout,
                                fontSize = 30.sp,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.shadow(10.dp)
                            )
                        }
                        if(current_streak < maxStreak){
                            val timeRemaining = nextRewardDate.time - Date().time
                            val totalMinutes = timeRemaining / 1000 / 60
                            val hours = totalMinutes / 60
                            val minutes = totalMinutes % 60

                            Text(text = "PLAY AGAIN: ${hours}H ${minutes} M", style = MaterialTheme.typography.labelSmall.copy(
                                fontSize = 12.sp
                            ), color = Color.White )
                        }
                    }

                    Row(){
                        if(current_streak >= 1 ){
                            Text(text = "${current_streak} Day Streak", style = MaterialTheme.typography.labelLarge.copy(
                                fontSize = 16.sp
                            ), color = Color.White)
                        }

                        Icon(
                            imageVector = Icons.Filled.ArrowBackIosNew,
                            tint = Color.White,
                            contentDescription = null,
                            modifier = Modifier
                                .size(18.dp)
                                .padding(start = 2.dp)
                                .rotate(degrees = 180f)
                                .offset(0.dp, 1.dp),
                        )
                    }
                }
                AutosizeText(
                    text = if(current_streak < maxStreak) "COME BACK TOMORROW FOR MORE REWARDS!" else "ALL REWARDS CLAIMED!",
                    style = TextStyle(color = Color.White, fontFamily = AppFont.Knowkout, fontSize = 24.sp),
                    maxLines = 1)
            }
        }
    }
}
@Composable
fun ClusterRewardsBox(
    listElements: List<@Composable ()->Unit>,
    title: String,
    subTitle: String,
    onInfoClicked: () -> Unit
) {
    Column(modifier = Modifier
        .fillMaxWidth()
        .background(GreenMinimalBackground, shape = RoundedCornerShape(4.dp))
        .border(1.dp, MaterialTheme.colorScheme.outline, shape = RoundedCornerShape(4.dp))
        .padding(4.dp)
    ) {
        Box(
            modifier = Modifier.padding(
                start = 12.dp,
                top = 10.dp,
                end = 8.dp,
                bottom = 12.dp
            )
        ){
            Image(
                modifier = Modifier
                    .size(15.dp)
                    .offset(0.dp, (-2).dp)
                    .clip(RoundedCornerShape(10.dp))
                    .align(Alignment.TopEnd)
                    .clickable(
                        enabled = true,
                        onClick = onInfoClicked
                    ),
                painter = painterResource(R.drawable.ic_info),
                contentDescription = null,
                colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.onPrimary),
                alignment = Alignment.Center
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                Text(
                    text = title,
                    color = MaterialTheme.colorScheme.onPrimary,
                    style = MaterialTheme.typography.titleSmall,
                )
                Text(
                    text = subTitle.uppercase(),
                    color = MaterialTheme.colorScheme.onPrimary,
                    style = MaterialTheme.typography.headlineSmall,
                )
            }
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(2.dp))
                .background(
                    MaterialTheme.colorScheme.surface,
                    RoundedCornerShape(2.dp)
                )
        ) {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                for (element in listElements){
                    element()
                }
            }
        }
    }
}
fun GetRewardTypeString(rewardType:APIGoalRewardType?):Int{
    return when(rewardType){
        APIGoalRewardType.faceoff -> R.string.bonus_cash
        APIGoalRewardType.sportsbook -> R.string.sports_book_bonus_bet
        APIGoalRewardType.freebetbonus -> R.string.sports_book_bonus_bet
        else -> R.string.bonus_cash
    }
}


@Composable
fun ClusterRewardElement(
    taskData: APIGoalsTask,
    goalDataModel: APIActiveGoal,
    modifier: Modifier = Modifier,
    fromLeaderboard:Boolean = false,
    showDivider: Boolean = true,
    onClaimClicked: (callback: (res:APIPostGoalsCollectableResponse)->Unit)->Unit = {},
    onClaimComplete: (success: Boolean)->Unit = {},
) {
    val valueTitle = taskData.reward?.toDollarString()
    val rewardTitle = taskData.title
    val claimable = taskData.isComplete && taskData.goal_user_instance_task_id != null
    val currentStepProgress = taskData.current_progress?.toDouble()?.toInt() ?: 0
    val currentStepMaxProgress = taskData.required_progress.toDouble().toInt()

    val showSlideTransition : MutableTransitionState<Boolean>  = remember { MutableTransitionState(true) }
    val claimStateLocal by goalDataModel.claimState.collectAsState()

    val showClaimBtn = claimable && claimStateLocal != APIActiveGoalClaimState.Complete
    val showClaimAnimation = claimable && claimStateLocal == APIActiveGoalClaimState.In_Progress
    val backgroundColor = if(taskData.isLocked || taskData.isExpired) {
        theme_light_background
    }else if (claimable) {
        if(claimStateLocal == APIActiveGoalClaimState.Complete){
            MaterialTheme.colorScheme.outlineVariant
        }else{
            MaterialTheme.colorScheme.tertiaryContainer
        }
    }else {
        MaterialTheme.colorScheme.surface
    }

    AnimatedVisibility(
        visibleState = showSlideTransition,
        exit = fadeOut(animationSpec = tween(durationMillis = 1000)) + slideOutVertically(
            targetOffsetY = { fullHeight -> fullHeight * 2 },
            animationSpec = tween(durationMillis = 1000),
        ),
        modifier = modifier,
    ) {
        Box(
            modifier = Modifier
                .background(backgroundColor)
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(
                    8.dp,
                    Alignment.CenterHorizontally
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp)
            ) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(6.dp),
                    modifier = Modifier
                        .weight(1f)
                ) {
                    Row(
                        modifier = Modifier.fillMaxSize(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if (rewardTitle != null){
                            Text(
                                text = rewardTitle,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurface,
                                modifier = Modifier
                                    .padding(bottom = 6.dp)
                                    .weight(0.75f),
                                overflow = TextOverflow.Visible
                            )
                        }

                        if (!showClaimBtn && valueTitle != null) {
                            Box(
                                modifier = Modifier
                                    .background(
                                        MaterialTheme.colorScheme.tertiary,
                                        RoundedCornerShape(8.dp)
                                    )
                            ) {
                                Box(
                                    modifier = Modifier.padding(horizontal = 6.dp, 3.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = valueTitle,
                                        style = MaterialTheme.typography.headlineLarge.copy(fontSize = 10.sp),
                                        textAlign = TextAlign.Center,
                                        fontWeight = FontWeight.Bold,
                                        color = MaterialTheme.colorScheme.onTertiary,
                                    )
                                }
                            }
                        }
                    }

                    Box {
                        if (!claimable || showClaimAnimation) {
                            RewardsProgressBar(
                                task = taskData,
                                currentStepProgress = currentStepProgress,
                                currentStepMaxProgress = currentStepMaxProgress,
                                progressBarColor = if (
                                    claimStateLocal != APIActiveGoalClaimState.Not_Claim
                                )
                                    MaterialTheme.colorScheme.tertiary
                                else Color(
                                    0xFF005FC8
                                ),
                                ctaString = taskData.cta,
                                link = taskData.link,
                                gameId = taskData.game_id
                            )
                        } else {
                            RewardsProgressBar(
                                task = taskData,
                                currentStepProgress = currentStepProgress,
                                currentStepMaxProgress = currentStepMaxProgress,
                                ctaString = taskData.cta,
                                link = taskData.link,
                                gameId = taskData.game_id
                            )
                        }
                    }
                }

                if (showClaimBtn) {
                    Column (
                        horizontalAlignment = Alignment.End,
                        verticalArrangement = Arrangement.spacedBy(6.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .width(66.dp)
                                .height(30.dp)
                        ) {
                            CommonButton(
                                title = "Claim",
                                fillColor = if (!showClaimAnimation) {
                                    MaterialTheme.colorScheme.tertiary
                                } else {
                                    MaterialTheme.colorScheme.outlineVariant
                                },
                                enabled = !showClaimAnimation
                            ) {
                                onClaimClicked { res ->
                                    val success = res.was_eligible_to_claim
                                    if (success) {
                                        goalDataModel.claimState.value =
                                            APIActiveGoalClaimState.In_Progress
                                    } else {
                                        goalDataModel.eligibleToClaim = false
                                    }

                                    CoroutineScope(Dispatchers.IO).launch {
                                        if (success) Thread.sleep(2000)
                                        goalDataModel.claimState.value =
                                            APIActiveGoalClaimState.Complete
                                        if (!fromLeaderboard) {
                                            showSlideTransition.targetState = false
                                        }
                                        if (success) Thread.sleep(1000)
                                        onClaimComplete(success)

                                        if(res.reward?.tournament_ticket != null){
                                            PlayViewModel.instance.jumpToPlayView(res.reward.tournament_ticket.game_id)
                                        }
                                    }
                                }
                            }

                            if (showClaimAnimation) {
                                CustomAnimationView(
                                    animation = R.raw.anim_confetti_burst,
                                    animationDidFinish = {

                                    },
                                    modifier = Modifier
                                        .wrapContentSize(unbounded = true)
                                        .size(150.dp)
                                )
                            }
                        }

                        if (valueTitle != null){
                            Box(
                                modifier = Modifier
                                    .background(
                                        MaterialTheme.colorScheme.tertiary,
                                        RoundedCornerShape(8.dp)
                                    )
                            )
                            {
                                Box(
                                    modifier = Modifier.padding(horizontal = 6.dp, 3.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = valueTitle,
                                        style = MaterialTheme.typography.headlineLarge.copy(fontSize = 10.sp),
                                        textAlign = TextAlign.Center,
                                        fontWeight = FontWeight.Bold,
                                        color = MaterialTheme.colorScheme.onTertiary,
                                    )
                                }
                            }
                        }
                    }
                }
            }

            if (showDivider) {
                HorizontalDivider(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.BottomCenter),
                    thickness = 1.dp,
                    color = MaterialTheme.colorScheme.outline
                )
            }
        }
    }
}

@Composable
fun RewardsFirstDeposit(
    onDepositClick: () -> Unit
){
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.secondary)
            .padding(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ){
        Row (
            modifier = Modifier
                .fillMaxWidth()
                .padding(end = 4.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Box (modifier = Modifier.size(64.dp)) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    painter = painterResource(R.drawable.ic_reward_base),
                    contentDescription = "Reward Icon Base",
                    colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.primary)
                )
                Image(
                    modifier = Modifier
                        .size(24.dp)
                        .align(Alignment.Center)
                        .offset(2.dp, (-2).dp),
                    painter = painterResource(R.drawable.ic_dollar_sign),
                    contentDescription = null,
                    colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.onPrimary)
                )
            }

            Column (
                modifier = Modifier
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ){
                Text(
                    text = STR(R.string._100_deposit_match_bonus_up_to_10),
                    color = MaterialTheme.colorScheme.inversePrimary,
                    style = MaterialTheme.typography.bodyMedium,
                )
                Text(
                    text = STR(R.string.first_deposit),
                    color = MaterialTheme.colorScheme.onPrimary,
                    style = MaterialTheme.typography.bodyMedium,
                )
                Text(
                    text = STR(R.string.make_your_first_deposit_and_get_a_100_match_up_to_10_instantly),
                    color = MaterialTheme.colorScheme.onPrimary,
                    style = MaterialTheme.typography.headlineMedium,
                )
            }
        }

        CommonButton(
            title = STR(R.string.deposit_now),
            fillColor = MaterialTheme.colorScheme.tertiary,
            onClick = onDepositClick
        )
    }
}

@Composable
fun FirstDepositTile(reward: APICompletableGoal, onDepositClick: () -> Unit, onClick: () -> Unit) {
    Column(
        modifier = Modifier
            .background(Color(0xFF05285A), shape = RoundedCornerShape(4.dp))
            .border(1.dp, MaterialTheme.colorScheme.outline, shape = RoundedCornerShape(4.dp))
            .padding(16.dp)
    ) {
        Row(
            modifier = Modifier
                .padding(bottom = 6.dp)
                .clickable { onClick.invoke() },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box (
                modifier = Modifier
                    .size(68.dp)
                    .offset(x = (-8).dp)
            ) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    painter = painterResource(R.drawable.ic_reward_base),
                    contentDescription = "Reward Icon Base",
                    colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.primary)
                )
                Image(
                    modifier = Modifier
                        .size(36.dp)
                        .align(Alignment.Center)
                        .offset(0.dp, (-3).dp),
                    painter = painterResource(R.drawable.ic_dollar_sign),
                    contentScale = ContentScale.FillWidth,
                    contentDescription = null,
                    colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.onPrimary)
                )
            }

            Column(
                modifier = Modifier.padding(start = 8.dp)
            ) {
                Text(
                    text = reward.goal.title,
                    color = Color(0xFFFFDC2E),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.padding(bottom = 3.dp)
                )

                Text(
                    text = reward.goal.subtitle,
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    maxLines = 5,
                    softWrap = true,
                    overflow = TextOverflow.Visible
                )

                // Description removed as per WW request (DK)
//                reward.goal.description?.let {
//                    Text(
//                        text = it,
//                        color = Color.White,
//                        fontSize = 12.sp,
//                        fontWeight = FontWeight.Normal,
//                        maxLines = 2,
//                        overflow = TextOverflow.Ellipsis,
//                        modifier = Modifier.padding(bottom = 4.dp)
//                    )
//                }
            }
        }

        CommonButton(
            title = STR(R.string.deposit_now),
            fillColor = Color(0xFF127F00),
            onClick = onDepositClick
        )
    }
}

@Composable
fun getActiveRewardElement(
    goalDataModel: APIActiveGoal,
    onClaimClicked: (callback: (res: APIPostGoalsCollectableResponse) -> Unit) -> Unit,
    onClaimComplete: (success: Boolean) -> Unit,
    onInfoClicked: (() -> Unit)?,
    modifier: Modifier = Modifier,
    fromLeaderboard:Boolean = false,
    clusterTaskId:String? = null
): @Composable ()->Unit {
    return {
        key(goalDataModel.hashCode()) {
            ActiveRewardElement(
                goalDataModel = goalDataModel,
                modifier = modifier,
                onClaimClicked = onClaimClicked,
                onClaimComplete = onClaimComplete,
                onInfoClicked = onInfoClicked?: {},
                showInfo = onInfoClicked != null,
                fromLeaderboard = fromLeaderboard,
                clusterTaskId = clusterTaskId,
            )
        }
    }
}

@Composable
fun ClaimedActiveReward(
    goalDataModel: APIActiveGoal,
    showDivider: Boolean = true
){
    val rewardType = when(goalDataModel.rewardType){
        APIGoalRewardType.faceoff -> R.string.bonus_cash
        APIGoalRewardType.sportsbook -> R.string.sports_book_bonus_bet
        APIGoalRewardType.freebetbonus -> R.string.sports_book_bonus_bet
        else -> R.string.bonus_cash
    }

    val icon = goalDataModel.icon
    val iconDescription: String? = null
    val valueTitle = STR(
        rewardType,
        goalDataModel.cashString
    )

    var rewardTitle = goalDataModel.titleString
    var subTitle =  goalDataModel.subTitleString
    val currentStepProgress = goalDataModel.currentProgress
    val currentStepMaxProgress = goalDataModel.requiredProgress
    val currentStep = goalDataModel.currentStep
    val totalStepCount = goalDataModel.requiredStep

    if(goalDataModel.goalType == APIGoalType.cluster){
        rewardTitle = goalDataModel.cluster_title ?:""
        subTitle = goalDataModel.task_title ?:""
    }

    Box(
        modifier = Modifier
            .background(MaterialTheme.colorScheme.background)
            .padding(vertical = 12.dp)
            .padding(start = 8.dp, end = 16.dp)
    ) {
        Row (
            horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally),
            modifier = Modifier
                .fillMaxWidth()
                .height(IntrinsicSize.Min)
                .background(MaterialTheme.colorScheme.background)
        ) {
            Column(
                verticalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxHeight()
                    .width(IntrinsicSize.Min)
                    .background(MaterialTheme.colorScheme.background)
            ) {
                Box(modifier = Modifier.size(68.dp)) {
                    var iconColor = MaterialTheme.colorScheme.tertiary
                    var iconSize = 30.dp
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        painter = painterResource(R.drawable.ic_reward_base),
                        contentDescription = "Reward Icon Base",
                        colorFilter = ColorFilter.tint(iconColor)
                    )
                    if(icon == "FIRST_TIME_DEPOSIT_REWARDS")
                    {
                        iconColor = Color.White
                        iconSize = 36.dp
                    }
                    Image(
                        modifier = Modifier
                            .size(iconSize)
                            .align(Alignment.Center)
                            .offset(0.dp, (-3).dp),
                        contentScale = ContentScale.FillBounds,
                        painter = painterResource(getIcon(icon)),
                        contentDescription = iconDescription,
                        colorFilter = ColorFilter.tint(iconColor)
                    )
                }

                if (totalStepCount > 1) {
                    val stepNumString =
                        STR(R.string.step_caps, currentStep, totalStepCount)
                    Box(
                        modifier = Modifier
                            .offset(0.dp, (2).dp)
                            .padding(horizontal = 4.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .height(12.dp)
                                .background(
                                    MaterialTheme.colorScheme.secondaryContainer,
                                    RoundedCornerShape(3.dp)
                                )
                                .align(Alignment.BottomCenter)
                                .fillMaxWidth()
                        ) {
                            Text(
                                text = stepNumString,
                                textAlign = TextAlign.Center,
                                color = MaterialTheme.colorScheme.onPrimary,
                                style = MaterialTheme.typography.displaySmall,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .align(Alignment.Center)
                            )
                        }
                    }
                }
            }

            Column (
                verticalArrangement = Arrangement.spacedBy(6.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                Row (
                    modifier = Modifier.fillMaxWidth()
                ){
                    Column (
                        modifier = Modifier
                            .weight(weight = 1f)
                    ) {
                        Box(
                            modifier = Modifier
                                .background(
                                    MaterialTheme.colorScheme.tertiary,
                                    RoundedCornerShape(8.dp)
                                )
                        )
                        {
                            Box (modifier = Modifier.padding(horizontal = 6.dp,3.dp), contentAlignment = Alignment.Center) {
                                Text(
                                    text = valueTitle,
                                    style = MaterialTheme.typography.headlineLarge,
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.onTertiary,
                                )
                            }
                        }
                        Spacer(modifier = Modifier.height(5.dp))
                        Text(
                            text = rewardTitle,
                            style = MaterialTheme.typography.headlineLarge,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Spacer(modifier = Modifier.height(5.dp))
                        Text(
                            text = subTitle,
                            style = MaterialTheme.typography.headlineMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }
                }

                RewardsProgressBar(
                    task = null,
                    currentStepProgress = currentStepProgress,
                    currentStepMaxProgress = currentStepMaxProgress,
                    progressBarColor = MaterialTheme.colorScheme.tertiary,
                    ctaString = goalDataModel.ctaString,
                    link = goalDataModel.linkString,
                    gameId = goalDataModel.gameId
                )
            }
        }

        if(showDivider){
            HorizontalDivider(
                modifier = Modifier
                    .fillMaxWidth()
                    .offset(y = 12.dp)
                    .padding(start = 8.dp)
                    .align(Alignment.BottomCenter)
                    .background(MaterialTheme.colorScheme.outlineVariant),
                thickness = 1.dp,
                color = MaterialTheme.colorScheme.outline
            )
        }
    }
}

@Composable
fun getBonusCashValueTitle(value:String): String {
    return STR(R.string.bonus_cash, value.toDollarString())
}

@Composable
private fun getIcon(
    iconString: String?
): Int {
    var icon = R.drawable.ic_fanduel
    when (iconString) {
        //todo: VERIFY THESE KEYS WHEN FULL LIST OF KEYS IS OBTAINED
        "GRAND_SLAM_SUPERSTAR" -> icon = R.drawable.ic_goals_baseball
        "NOTHING_BUT_NET_REWARDS" -> icon = R.drawable.ic_goals_basketball
        "CALENDAR_BASED_REWARDS" -> icon = R.drawable.ic_goals_calendar
        "CASH_GAME_REWARDS" -> icon = R.drawable.ic_goals_cash
        "BLOCK_TRAIL_REWARDS" -> icon = R.drawable.ic_goals_blocktrail
        "FREE_GAME_REWARDS" -> icon = R.drawable.ic_goals_free
        "KINGS_CROSSING_REWARDS" -> icon = R.drawable.ic_goals_kings
        "WHEEL_OF_FORTUNE_REWARDS" -> icon = R.drawable.ic_goals_wof
        "FIRST_TIME_DEPOSIT_REWARDS" -> icon = R.drawable.ic_dollar_sign
        // No mapping provided for following elements:
        "SCRABBLE_CUBES" -> icon = R.drawable.ic_goals_scrabble
        "PUZZLE_PYRAMID" -> icon = R.drawable.ic_goals_pyramid
        "TRIVIAL_PURSUIT" -> icon = R.drawable.ic_goals_trivial
        "FIELD_GOAL_KICKER" -> icon = R.drawable.ic_goals_fieldgoal
        "QUARTER_BACK_ATTACK" -> icon = R.drawable.ic_goals_quarterback
        "FREE_TICKET_REWARDS" -> icon = R.drawable.ic_goals_free_tickets
    }
    return icon
}

//@Composable
fun getClusterIcon(
    iconString: String?
): Int {
    var icon = R.drawable.ic_fanduel
    when (iconString) {
        "NEW_GAME_DIAMOND" -> icon = R.drawable.ic_new_game_diamond
        "NEW_GAME_MEGAPHONE" -> icon = R.drawable.ic_new_game_megaphone
        "NEW_GAME_THREE_STARS" -> icon = R.drawable.ic_new_game_three_stars
        "NEW_GAME_STAR" -> icon = R.drawable.ic_new_game_star
        "NEW_GAME_LEVITATING_STAR" -> icon = R.drawable.ic_new_game_levitating_star
        "SEASONAL_CHECKLIST" -> icon = R.drawable.ic_seasonal_checklist
        "SEASONAL_LIST" -> icon = R.drawable.ic_seasonal_list
        "SEASONAL_STAR_LIST" -> icon = R.drawable.ic_seasonal_star_list
        "SEASONAL_SNOWFLAKE" -> icon = R.drawable.ic_seasonal_snowflake
        "SEASONAL_LEAF" -> icon = R.drawable.ic_seasonal_leaf
        "SEASONAL_SUN" -> icon = R.drawable.ic_seasonal_sun
        "SEASONAL_FLOWER" -> icon = R.drawable.ic_seasonal_flower
        "NEW_PLAYER_ADD_PLAYER" -> icon = R.drawable.ic_new_player_add_player
        "NEW_PLAYER_CHECK_PLAYER" -> icon = R.drawable.ic_new_player_check_player
        "NEW_PLAYER_SINGLE_PLAYER" -> icon = R.drawable.ic_new_player_single_player
        "NEW_PLAYER_GIFT" -> icon = R.drawable.ic_new_player_gift
        "POPULAR_FIRE" -> icon = R.drawable.ic_popular_fire
        "POPULAR_LIGHTNING" -> icon = R.drawable.ic_popular_lightning
        "POPULAR_STAR" -> icon = R.drawable.ic_popular_star
        "POPULAR_POSITIVE_GRAPH" -> icon = R.drawable.ic_popular_positive_graph
        "POPULAR_VIDEO_GAME_CONTROLLER" -> icon = R.drawable.ic_popular_video_game_controller
        "POPULAR_SHOOTING_STAR" -> icon = R.drawable.ic_popular_shooting_star
    }
    return icon
}
