package com.gametaco.app_android_fd.manager.analytics

import AppsFlyerManager
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.WalletManager

class AppsFlyerEventFactory(
    private val tournamentManager: TournamentManager,
    private val authenticationManager: AuthenticationManager,
    private val analyticsManager: AppsFlyerManager,
    private val walletManager: WalletManager
) {

    companion object {
        const val TAG = "AppsFlyerEventFactory"
        val instance: AppsFlyerEventFactory
            get() = resolve()

        private const val AF_CUSTOMER_USER_ID = "af_customer_user_id"
        private const val AF_REVENUE = "af_revenue"
        private const val FD_USERNAME = "fd_username"
        private const val FD_CAMPAIGN_ID = "fd_campaignID"
        private const val FD_DEPOSIT_AMOUNT = "fd_deposit_amount"
        private const val FD_GAME_ID = "fd_game_id"
        private const val FD_TOURNAMENT_ID = "fd_tournament_id"
        private const val UNKNOWN = "Unknown"
    }

    private fun getGameId() : String { return tournamentManager.getGameId() }
    private fun getTournamentId() : String { return tournamentManager.getTournamentId() }
    private fun getCustomerUserId() : String { return authenticationManager.apiMe?.id ?: UNKNOWN }
    private fun getUsername() : String { return authenticationManager.apiMe?.username ?: UNKNOWN }
    private fun getCampaignId() : String { return (analyticsManager.conversionData?.get("campaign_id") ?: UNKNOWN).toString() }
    private fun getEntryFee() : String { return tournamentManager.tournamentInstanceEntryFee.value.string }
    private fun getDepositAmount() : String { return walletManager.lastDepositAmount }

    val login: AnalyticsEvent get() = AnalyticsEvent(
        analyticsEvent = "fo_product_login",
        properties = mapOf(
        )
    )

    val cashEntryFee: AnalyticsEvent get() = AnalyticsEvent(
        analyticsEvent = "fo_product_entry_successful",
        properties = mapOf(
            AF_REVENUE to getEntryFee()
        )
    )

    val completeRegistration: AnalyticsEvent get() = AnalyticsEvent(
        analyticsEvent = "fo_product_registration_successful",
        properties = mapOf(
            AF_CUSTOMER_USER_ID to getCustomerUserId(),
            FD_CAMPAIGN_ID to getCampaignId(),
        )
    )

    val deposit: AnalyticsEvent get() = AnalyticsEvent(
        analyticsEvent = "fo_product_deposit_successful",
        properties = mapOf(
            AF_CUSTOMER_USER_ID to getCustomerUserId(),
            FD_CAMPAIGN_ID to getCampaignId(),
            FD_DEPOSIT_AMOUNT to getDepositAmount()
        )
    )

    val ftueCompleted: AnalyticsEvent get() = AnalyticsEvent(
        analyticsEvent = "fo_product_tutorial_complete",
        properties = mapOf(
            AF_REVENUE to getEntryFee(),
            FD_GAME_ID to getGameId(),
            FD_TOURNAMENT_ID to getTournamentId()
        )
    )

    val firstGamePlayed: AnalyticsEvent get() = AnalyticsEvent(
        analyticsEvent = "fo_product_first_entry_successful",
        properties = mapOf(
            AF_REVENUE to getEntryFee(),
            FD_GAME_ID to getGameId(),
            FD_TOURNAMENT_ID to getTournamentId()
        )
    )

    val firstPaidGame: AnalyticsEvent get() = AnalyticsEvent(
        analyticsEvent = "fo_product_first_paid_entry_successful",
        properties = mapOf(
            AF_REVENUE to getEntryFee(),
            FD_GAME_ID to getGameId(),
            FD_TOURNAMENT_ID to getTournamentId()
        )
    )
}
