package com.gametaco.app_android_fd.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.APIPlayedGame
import com.gametaco.app_android_fd.data.entity.APITournamentEntryResult
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntryList
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceStatus
import com.gametaco.app_android_fd.data.entity.EntryFee
import com.gametaco.app_android_fd.data.entity.TournamentStartInformation
import com.gametaco.app_android_fd.data.navigation.NavigationData
import com.gametaco.app_android_fd.manager.AlertDialogManager
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.ErrorManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.analytics.TournamentAnalytics
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.utils.toDate
import com.gametaco.utilities.ResourceState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

data class OnReLoadScoresDataEvent(val refresh: Boolean)

class ScoresViewModel : ViewModel() {

    init {
        Logger.d(TAG, "Init ScoresViewModel")

        if(!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this)

        EventBus.getDefault().post(OnReLoadScoresDataEvent(true))
    }

    private val _entryList = MutableStateFlow(APITournamentInstanceEntryList(0,null,null, listOf()))
    val entryList = _entryList.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading = _isLoading.asStateFlow()

    private val _isLoadingMore = MutableStateFlow(false)
    val isLoadingMore = _isLoadingMore.asStateFlow()

    private var _cursor:String? = null
    private var _legacy_cusor:Int = 0
    private val legacyCursorInterval:Int = 10
    private var _legacy_loaded:Boolean = false

    val hasMoreEntries:Boolean
        get() = _cursor != null

    val hasMoreLegacyEntries:Boolean
        get() {
            return _legacy_loaded && _legacy_cusor < legacyEntries.size
        }
    var legacyEntries:List<APITournamentEntryResult> = listOf()
        private set
    var nonLegacyEntries:List<APITournamentEntryResult> = listOf()
        private set
    val combinedEntries:List<APITournamentEntryResult>
        get() {
            return nonLegacyEntries + legacyEntries.subList(0,_legacy_cusor)
        }

    var notStartedEntries:List<APITournamentEntryResult> = listOf()
        private set
    var inProgressEntries:List<APITournamentEntryResult> = listOf()
        private set
    var closedEntries:List<APITournamentEntryResult> = listOf()
        private set

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSessionChangedEventHandler(event: OnReLoadScoresDataEvent) {
        loadAll()
    }

    fun loadAll(){
        //don't do this in guest mode
        if(AuthenticationManager.instance.isGuest)
            return
        _cursor = null
        _isLoading.value = true
        _legacy_cusor = 0
        _legacy_loaded = false

        viewModelScope.launch {
            loadEntries()

            sortEntries()
            calculateNew(_entryList.value.results)
            _isLoading.value = false
        }
    }
    private suspend fun loadEntries(){
        val res = WorldWinnerAPI.instance.getTournamentEntries()
        if(res.isSuccess()){
            val entries = (res as ResourceState.Success).data
            _cursor = entries.nextCursor()
            nonLegacyEntries = entries.results
            _entryList.value = entries
        }else if(res.isError()){
            nonLegacyEntries = emptyList()
            Logger.e("Scores","Load Entries Error")
            if(!ErrorManager.instance.handleResourceStateError(res)) {
                AlertDialogManager.instance.showDialog(
                    "Error",
                    (res as ResourceState.Error).error,
                    "Confirm",
                    {} )
            }
        }
    }
    private suspend fun loadLegacyEntries(){
        Logger.d("loadLegacyEntries")
        val res = WorldWinnerAPI.instance.getPlayedGames()
        if(res.isSuccess()){
            ////////////////test codes below////////////////
//            val entries = buildMockData(45)
//            legacyEntries = entries.map {
//                convertLegacyResultToAPITournamentEntryResult(it)
//            }
            //////////////////test codes above////////////////

            val entries = (res as ResourceState.Success).data
            legacyEntries = entries.results.map {
                convertLegacyResultToAPITournamentEntryResult(it)
            }.sortedByDescending { it.displayDate() }

//            println("firstlegacyEntries: ${legacyEntries[0].displayDate()}")
            _legacy_loaded = true
            checkAndMoveLegacyCursor()

            if(legacyEntries.isNotEmpty()){
                val oldEntries = _entryList.value
                _entryList.value = APITournamentInstanceEntryList(
                    count = nonLegacyEntries.size + _legacy_cusor,
                    next = oldEntries.next,
                    previous = oldEntries.previous,
                    results = combinedEntries
                )
            }
        }else{
            legacyEntries = emptyList()
            Logger.e("Scores","Load Legacy Entries Error")
            if(!ErrorManager.instance.handleResourceStateError(res)) {
                AlertDialogManager.instance.showDialog(
                    "Error",
                    (res as ResourceState.Error).error,
                    "Confirm",
                    {} )
            }
        }
    }

    fun loadMore(){
        //don't do this in guest mode
        if(AuthenticationManager.instance.isGuest)
            return

        if(hasMoreEntries || hasMoreLegacyEntries ){
            viewModelScope.launch {
                _isLoadingMore.value = true

                if(hasMoreEntries){
                    loadMoreEntries()
                }

                if(hasMoreLegacyEntries){
                    checkAndMoveLegacyCursor()
                }

                val oldEntries = _entryList.value
                _entryList.value = APITournamentInstanceEntryList(
                    count = combinedEntries.size,
                    next = oldEntries.next,
                    previous = oldEntries.previous,
                    results = combinedEntries
                )
                sortEntries()

                _isLoadingMore.value = false
            }
        }
    }
    private suspend fun loadMoreEntries(){
        Logger.d("loadMoreEntries")

        val res = WorldWinnerAPI.instance.getTournamentEntries(_cursor)
        if(res.isSuccess()){
            _isLoadingMore.value = false
            val oldEntries = _entryList.value
            val newEntries = (res as ResourceState.Success).data
            _cursor = newEntries.nextCursor()
            nonLegacyEntries = oldEntries.results + newEntries.results

            _entryList.value.previous = newEntries.previous
            _entryList.value.next = newEntries.next
        }else if(res.isError()){
            Logger.e("Scores","Load More Entries Error")

            if(!ErrorManager.instance.handleResourceStateError(res)) {
                AlertDialogManager.instance.showDialog(
                    "Error",
                    (res as ResourceState.Error).error,
                    "Confirm",
                    {})
            }
        }
    }

    private fun checkAndMoveLegacyCursor(){
        if(legacyEntries.isNotEmpty()){
            if(nonLegacyEntries.isEmpty() || !hasMoreEntries){//if no more entries then just load legacy entries
                _legacy_cusor += legacyCursorInterval
                _legacy_cusor = Math.min(_legacy_cusor,legacyEntries.size)
            }else{
                //find last date of non-legacy entry
                val lastNonLegacyEntry = nonLegacyEntries.sortedBy { it.displayDate() }[0]
//                println("lastNonLegacyEntry : ${lastNonLegacyEntry.displayDate()}")

                //find all the legacy entries that are after the lastNonLegacyEntry
                val legacyEntriesInRange = legacyEntries.filter { it.displayDate() >= lastNonLegacyEntry.displayDate() }
                val sizeInRange = legacyEntriesInRange.size

//                println("sizeInRange ::::::: ${sizeInRange}")
                //don't move cursor if legacy entries are before the the lastNonLegacyEntry
                if(sizeInRange > 0){
                    _legacy_cusor += Math.min(sizeInRange,legacyCursorInterval)
                    _legacy_cusor = Math.min(_legacy_cusor,legacyEntries.size)
                }
            }
        }
    }

    fun joinTournament(tournamentInstanceEntryId:String,tournament_start_information: TournamentStartInformation, gameId:String?){

        if(gameId == null){
            Logger.e(TAG, "joinTournament gameId is null")
            return
        }

        val entry = getEntry(tournamentInstanceEntryId)
        val tournamentAnalytics = entry?.let {
            TournamentAnalytics(
                contestId = tournament_start_information.tournament_id,
                tournamentInstanceEntryId = tournamentInstanceEntryId,
                gameId = entry.game_id ?: "Unknown", gameName = it.game_display_name,
                gameMode = entry.game_mode_name ?: "Unknown", stake = entry.entry_fee)
        }

        Logger.d("join a non-started tournament $tournamentInstanceEntryId - $tournament_start_information")
        TournamentManager.instance.joinTournament(
            tournamentInstanceEntryId = tournamentInstanceEntryId,
            gameId = gameId,
            gameName = tournament_start_information.game_name,
            tournamentId = tournament_start_information.tournament_id,
            joinRecord = tournament_start_information.game_options.join,
            tournamentAnalytics = tournamentAnalytics,
            globalGameOptions = tournament_start_information.global_game_options,
            entryFee = EntryFee(entry?.entry_fee),
        )
    }

    private fun getEntry(tournamentId: String): APITournamentEntryResult? {
        val allEntries = notStartedEntries + inProgressEntries + closedEntries
        val entry = allEntries.firstOrNull{it.id == tournamentId}
        return entry
    }

    private fun sortEntries(){
        val entries:List<APITournamentEntryResult> = _entryList.value.results

        notStartedEntries = entries.filter { it.tournament_instance_status == APITournamentInstanceStatus.Open.rawValue && it.tournament_start_information != null }
            .sortedByDescending { it.displayDate()}
        inProgressEntries = entries.filter { it.tournament_instance_status == APITournamentInstanceStatus.Open.rawValue && it.tournament_start_information == null }
            .sortedByDescending { it.displayDate()}
        closedEntries = entries.subtract(notStartedEntries).subtract(inProgressEntries)
            .sortedByDescending { it.displayDate() }

        Logger.d("allEntries:\n ${entries.count()}")
        Logger.d("notStartedEntries:\n ${notStartedEntries.count()}")
        Logger.d("inProgressEntries:\n ${inProgressEntries.count()}")
        Logger.d("closedEntries:\n ${closedEntries.count()}")
    }
    private fun calculateNew(entries:List<APITournamentEntryResult>){
        val lastSeen = PreferencesManager.instance.getScoresScreenLastSeen()
//        println("lastSeen: ${lastSeen}")
        val count = entries.count {
            val closeTime = it.tournament_instance_closed_at?.toDate()
            closeTime?.after(lastSeen) == true
        }
        NavigationData.instance.setNotificationCountScores(count)
    }
    private fun buildMockData(count:Int = 39):List<APIPlayedGame>{
        val list = mutableListOf<APIPlayedGame>()
        for (i in 1..count){
            val div = i % 4
            list.add(APIPlayedGame(
                id = i.toString(),
                created_at = "2024-08-06T20:20:58Z",
                tournament_brand = "Head to Head",
                tournament_description = "Play with standard Klondike solitaire rules and scoring.",
                started_at = "2024-08-06T20:20:58Z",
                ended_at = "2024-08-07T01:19:22Z",
                entry_fee = "1.00",
                game_id = "018fa7ff-494e-71cc-8019-91acfaf8c61b",
                game_icon = "https://qa1-s3-assets-cdn.wwqa-fd.com/games/a1aba138-337a-4c93-a1ee-3fcbe96387d1.jpg",
                maximum_entries = 10,
                is_winner = false,
                is_tie = false,
                game_mode_icon = null,
                game_display_name = "Solitaire Sprint",
                game_mode_description = "Play with standard Klondike solitaire rules and scoring.",
                tournament_instance_id = i.toString(),
                tournament_instance_status = if(div == 0) APITournamentInstanceStatus.Open.rawValue else if(div == 1) APITournamentInstanceStatus.Closed.rawValue else APITournamentInstanceStatus.Refunded.rawValue,
                tournament_instance_closed_at = "2024-08-07T01:19:22Z",
                tournament_start_information = null,
                tournament_instance_opened_at = "2024-08-07T01:09:22Z",
                game_mode_name = "Standard",
                status = "CLOSED",
                winnings = null,
            ))
        }
        return list.toList()
    }

    private fun convertLegacyResultToAPITournamentEntryResult(it:APIPlayedGame):APITournamentEntryResult{
        return  APITournamentEntryResult(
            id = it.id,
            created_at = it.created_at,
            tournament_instance_id = it.tournament_instance_id,
            started_at = it.started_at,
            entry_fee = it.entry_fee,
            game_icon = it.game_icon,
            game_id = it.game_id,
            maximum_slots = it.maximum_entries,
            is_winner = it.is_winner,
            is_tie = it.is_tie ?: false,
            tournament_instance_status = it.tournament_instance_status,
            tournament_instance_closed_at = it.tournament_instance_closed_at,
            status = it.status,
            tournament_brand = it.tournament_brand,
            prizes = null,
            game_display_name = it.game_display_name ?:"",
            game_options = null,
            tournament_start_information = it.tournament_start_information,
            winnings = it.winnings,
            tournament_description = it.tournament_description,
            game_mode_name = it.game_mode_name,
            game_mode_description = it.game_mode_description,
            game_mode_icon = it.game_mode_icon,
            is_legacy = true,
            has_replay = false
        )
    }
    companion object {
        const val TAG = "ScoresViewModel"
        val instance: ScoresViewModel by lazy { ScoresViewModel() }
    }

}