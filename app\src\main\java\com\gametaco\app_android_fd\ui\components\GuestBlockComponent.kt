package com.gametaco.app_android_fd.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Lock
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.gametaco.app_android_fd.ui.theme.AppandroidwwTheme
import com.gametaco.app_android_fd.ui.theme.GuestBlockBackground
import com.gametaco.app_android_fd.ui.theme.theme_light_primary
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.utils.parseHtml
import com.gametaco.utilities.STR
import resources.R

@Composable
fun GuestBlockComponent(
    modifier: Modifier = Modifier,
    title:String,
    topText:String? = null,
    desc:String,
    label:String,
    removeFade: Boolean = false,
    onClick:(()->Unit)? = null
)
{
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .then(modifier)
            .fillMaxSize()
            .then(
                if (!removeFade) {
                    Modifier.background(brush = GuestBlockBackground)
                } else {
                    Modifier
                }
            )

    ){
        if(topText != null)
        {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp))
            {
                Text(
                    text = topText,
                    modifier = Modifier.align(Alignment.TopStart),
                    style = MaterialTheme.typography.headlineMedium,
                )
            }
        }
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp))
        {
            Row {
                Icon(imageVector = Icons.Outlined.Lock,
                    contentDescription = null,
                    modifier = Modifier.size(25.dp)
                )
                Text(text = title,
                    style = MaterialTheme.typography.titleSmall,
                    color = Color(0xFF05285A)
                )
            }
            Text(text = desc.parseHtml(),
                style = MaterialTheme.typography.headlineMedium,
                color = Color(0xFF05285A),
                textAlign = TextAlign.Center,
                lineHeight = 15.sp
            )

            Spacer(modifier = Modifier.height(16.dp))

            CommonButton(
                title = label,
                fillColor = theme_light_primary,
            ) {
                if(onClick != null){
                    onClick()
                }
            }
        }
    }
}

@Preview
@Composable
fun GuestBlockComponentPreview(){
    AppandroidwwTheme {
        GuestBlockComponent(title = STR(R.string.join_now_to_unlock_rewards),
            desc = STR(R.string.rewards_are_waiting_for_you_register),
            label = STR(R.string.join_now_and_earn_rewards),
            modifier = Modifier.height(250.dp),
            onClick = {
                Logger.d("clicked")
            }
        )
    }
}