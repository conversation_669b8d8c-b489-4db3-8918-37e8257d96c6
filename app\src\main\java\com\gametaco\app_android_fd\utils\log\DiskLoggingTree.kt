package com.gametaco.app_android_fd.utils.log

import android.content.Context
import timber.log.Timber
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class DiskLoggingTree(context: Context) : Timber.Tree() {

    private val logFile: File

    init {
        val logDir = File(context.getExternalFilesDir(null), "logs")
        if (!logDir.exists()) logDir.mkdirs()
        cleanOldLogFiles(context)

        val sessionTimestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US).format(Date())
        logFile = File(logDir, "app_logs_$sessionTimestamp.txt")
        try {
            if (!logFile.exists()) {
                logFile.createNewFile()
                Timber.i(Logger.Tag, "Log file created: ${logFile.absolutePath}")
            }
        } catch (e: IOException) {
            Timber.e(e, "Failed to create log file")
        }

        // Ensure the log file exists.
        if (!logFile.exists()) {
            try {
                logFile.createNewFile()
            } catch (e: IOException) {
                Logger.e("DiskLoggingTree", "Failed to create log file", e)
            }
        }
    }

    private fun cleanOldLogFiles(context: Context, maxSessions: Int = 4) {
        val logDir = File(context.getExternalFilesDir(null), "logs")
        if (!logDir.exists()) return
        val logFiles = logDir.listFiles { file ->
            file.name.startsWith("app_logs_") && file.name.endsWith(".txt")
        }
            ?.toList()
            ?: emptyList()

        Timber.d(Logger.Tag,"Found ${logFiles.size} log files in $logDir")

        if (logFiles.size > maxSessions) {
            logFiles.sortedBy { it.lastModified() }
                .take(logFiles.size - maxSessions)
                .forEach {
                    Timber.i(Logger.Tag, "Deleting old log file: ${it.absolutePath}")
                    it.delete()
                }
        }
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US).format(Date())
        val logEntry = StringBuilder().apply {
            append("$timestamp [$priority] ")
            tag?.let { append("$it: ") }
            append(message)
            append("\n")
            t?.let { append(android.util.Log.getStackTraceString(it)).append("\n") }
        }.toString()

        try {
            FileWriter(logFile, true).use { writer ->
                writer.append(logEntry)
            }
        } catch (e: IOException) {
            Logger.e("DiskLoggingTree", "Error writing log to file", e)
        }
    }
}