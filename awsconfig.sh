#!/bin/zsh

# Set AWS Credentials
AWS_ACCESS_KEY_ID="********************"
AWS_SECRET_ACCESS_KEY="MYxWZxRlwp6yYnAAQOR+R11bV+Hsfj1pFba5hI1l"
AWS_DEFAULT_REGION="us-west-2"
AWS_OUTPUT_FORMAT="json"

# Create the AWS directory if it doesn't already exist
mkdir -p ~/.aws

# Write the AWS Configuration
echo "[default]" > ~/.aws/config
echo "region = $AWS_DEFAULT_REGION" >> ~/.aws/config
echo "output = $AWS_OUTPUT_FORMAT" >> ~/.aws/config

# Write the AWS Credentials
echo "[default]" > ~/.aws/credentials
echo "aws_access_key_id = $AWS_ACCESS_KEY_ID" >> ~/.aws/credentials
echo "aws_secret_access_key = $AWS_SECRET_ACCESS_KEY" >> ~/.aws/credentials

echo "AWS CLI is configured."

