package com.gametaco.app_android_fd.manager

import AppsFlyerManager
import com.gametaco.app_android_fd.data.api.APIGuestMe
import com.gametaco.app_android_fd.data.api.APIMe
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.APIAuthToken
import com.gametaco.app_android_fd.data.entity.APIGuestSignupResponse
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.manager.analytics.AppsFlyerEventFactory
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.utilities.ResourceState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import org.greenrobot.eventbus.EventBus

class AuthenticationManager(
    private val preferencesManager: PreferencesManager,
    private val worldWinnerAPI: WorldWinnerAPI,
    private val brazeManager: BrazeManager,
    private val siftManager: SiftManager,
    private val fdManagerLazy: Lazy<FDManager>,
    private val geoComplyManagerLazy: Lazy<GeoComplyManager>,
    private val errorManager: ErrorManager,
    private val maintenanceModeManagerLazy: Lazy<MaintenanceModeManager>,
    private val analyticsManager: AnalyticsManager,
    private val appsFlyerManager: AppsFlyerManager
) {
    private val fdManager: FDManager by lazy { fdManagerLazy.value }
    private val geoComplyManager: GeoComplyManager by lazy { geoComplyManagerLazy.value }
    private val maintenanceModeManager: MaintenanceModeManager by lazy { maintenanceModeManagerLazy.value }

    companion object {
        const val TAG = "AuthenticationManager"
        val instance: AuthenticationManager
            get() = resolve()

    }

    var apiMe : APIMe? = null
    var apiGuestMe: APIGuestMe? = null

    private val _apiMeFlow= MutableStateFlow(APIMe("",0,"","","0", ""))
    val apiMeFlow: StateFlow<APIMe> = _apiMeFlow.asStateFlow()

    var authToken: String?
        get() = preferencesManager.getWWAuthToken()
        set(value) {
            preferencesManager.setWWAuthToken(value)
        }


    var isGuest: Boolean
        get() = preferencesManager.getIsGuest() ?: false
        set(value) {
            preferencesManager.setIsGuest(value)
        }

    var hasPriorLogin: Boolean
        get() = preferencesManager.getHasPriorLogIn()
        set(value) {
            preferencesManager.setHasPriorLogIn(value)
        }

    var showRewardsFtue: Boolean
        get() = preferencesManager.getShowRewardsFtue()
        set(value) {
            preferencesManager.setShowRewardsFtue(value)
            _showRewardsFtueFlow.value = value
        }
    val showRewardsFtueDebug:Boolean
        get() = _showRewardsFtueFlow.value
    private val _showRewardsFtueFlow = MutableStateFlow(showRewardsFtue)
    fun toggleRewardsFtue(){
        val isShowing = showRewardsFtue
        showRewardsFtue = !isShowing
    }

    private val _showLobbyFtueFlow = MutableStateFlow<Boolean>(PreferencesManager.instance.getShowLobbyFtue())
    val showLobbyFtueFlow: StateFlow<Boolean>
        get() = _showLobbyFtueFlow.asStateFlow()
    var showLobbyFtue: Boolean
        get() = preferencesManager.getShowLobbyFtue()
        set(value) {
            _showLobbyFtueFlow.value = value
            preferencesManager.setShowLobbyFtue(value)
        }

    var restrictedStatus: Boolean
        get() = preferencesManager.getRestrictedStatus()
        set(value) {
            preferencesManager.setRestrictedStatus(value)
        }


    fun toggleLobbyFtue() {
        val isShowing = showLobbyFtue
        showLobbyFtue = !isShowing
        _showLobbyFtueFlow.value = showLobbyFtue
    }

    fun resetLogin() {
        Logger.d(TAG, "resetLogin")
        fdManager.resetLogin()
        authToken = null
        isGuest = false
        restrictedStatus = false
        siftManager.unsetUserId()
        geoComplyManager.reset()
        preferencesManager.clearCacheAfterLogout()
        maintenanceModeManager.reset()
        PreferencesManager.instance.setIsHapticsEnabled(false)
    }

    suspend fun refreshLoginForRestrictedStatus() :Boolean {

        if(fdManager.hasSessionData){

            val apiAuthTokenState = login(fdManager.sessionDataToken!!, false)
            if(apiAuthTokenState.isSuccess()) {
                return true
            }
        }
        return false
    }

    suspend fun logout() {

    }

    suspend fun login(authToken: String, skip_staff_update: Boolean): ResourceState<APIAuthToken> {

        val apiAuthTokenState = worldWinnerAPI.login(authToken, skip_staff_update)

        if(apiAuthTokenState.isSuccess())
        {
            BrazeManager.instance.logEvent(BrazeEventName.Fanduel_Login_Success.value)

            val apiAuthToken = (apiAuthTokenState as ResourceState.Success).data

            this.authToken = apiAuthToken.key

            showLobbyFtue = apiAuthToken.show_ftue

            restrictedStatus = apiAuthToken.is_restricted

            val meResponse = getMe()

            if(meResponse.isSuccess())
            {
                isGuest = false
                hasPriorLogin = true
                EventBus.getDefault().post(OnSessionChangedEvent(true, "login/meResponse"))
                appsFlyerManager.logEvent(AppsFlyerEventFactory.instance.login)
            } else {
                Logger.d(TAG, "Error in APIMe response")

                errorManager.handleResourceStateError(meResponse)

                //back to login
                EventBus.getDefault().post(OnSessionChangedEvent(false, "login/meResponse"))
            }

        } else if(apiAuthTokenState.isError()) {

            errorManager.handleResourceStateError(apiAuthTokenState)

            //back to login
            EventBus.getDefault().post(OnSessionChangedEvent(false, "login/!apiAuthTokenState"))
        }

        return apiAuthTokenState;
    }

    suspend fun guestLogin() : ResourceState<APIGuestSignupResponse> {
        val guestSignupResponse = worldWinnerAPI.postGuestSignup()

        if(guestSignupResponse.isSuccess()) {
            val apiAuthToken = (guestSignupResponse as ResourceState.Success).data

            this.authToken = apiAuthToken.key

            isGuest = true

            //get guest me
            val guestMeResponse = getGuestMe()

            if(guestMeResponse is ResourceState.Success) {
                EventBus.getDefault().post(OnSessionChangedEvent(true, "guestLogin/guestSignupResponse.isSuccess()"))
            } else if(guestMeResponse.isError()) { //error
                isGuest = false
                EventBus.getDefault().post(OnSessionChangedEvent(false, "guestLogin/guestSignupResponse/guestMeResponse.isError()"))
            }

        } else if(guestSignupResponse.isError()) {

            errorManager.handleResourceStateError(guestSignupResponse)

            isGuest = false
            EventBus.getDefault().post(OnSessionChangedEvent(false, "guestLogin/guestSignupResponse.isError()"))
        }

        return guestSignupResponse
    }

    suspend fun getMe() : ResourceState<APIMe>{
        var meResponse = worldWinnerAPI.getMe()

        if (meResponse.isSuccess()) {
            apiMe = (meResponse as ResourceState.Success).data
            _apiMeFlow.value = (meResponse as ResourceState.Success).data
            brazeManager.changeUser(apiMe?.braze_external_id ?:"")
            siftManager.setUserId(apiMe?.fanduel_id.toString())
            analyticsManager.setUserProperties(apiMe!!)
            appsFlyerManager.setUserId(apiMe!!.fanduel_id)
            EventBus.getDefault().post(OnMeChangedEvent(apiMe))
            Logger.d(TAG, "APIMe success returned data: " + apiMe.toString())
        } else if (meResponse.isError()) {
            errorManager.handleResourceStateError(meResponse)

            Logger.d(TAG, "Error in APIMe response")
        }
        return meResponse
    }

    suspend fun getGuestMe() : ResourceState<APIGuestMe>{
        var meResponse = worldWinnerAPI.getGuestMe()

        if (meResponse.isSuccess()) {
            apiGuestMe = (meResponse as ResourceState.Success).data
            Logger.d(TAG, "APIGuestMe success returned data: " + apiGuestMe.toString())
        } else if (meResponse.isError()) {
            errorManager.handleResourceStateError(meResponse)

            Logger.d(TAG, "Error in APIGuestMe response")
        }
        return meResponse
    }

    suspend fun getAppSession() : ResourceState<Void>{
        var sessionResponse = worldWinnerAPI.getAppSession()

        if(sessionResponse.isSuccess()) {
//            var data = (sessionResponse as ResourceState.Success).data

            Logger.d(TAG, "login getAppSession success")// returned data: " + data.toString())
        } else if(sessionResponse.isError()){
            errorManager.handleResourceStateError(sessionResponse)

            Logger.d(TAG, "Error in getAppSession response")
        }
        return sessionResponse
    }
}

data class OnSessionChangedEvent(val loggedIn: Boolean, val via: String)
data class OnMeChangedEvent(val meData: APIMe?)

