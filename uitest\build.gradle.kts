import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    kotlin("jvm")
}

group = "com.example"
version = "1.0-SNAPSHOT"

dependencies {
    implementation(Dependencies.appium)
    implementation(Dependencies.coroutinesCore)
    implementation(Dependencies.logback)
    implementation(Dependencies.okhttp)
    implementation(Dependencies.selenium)
    implementation(Dependencies.slf4j)
    testImplementation(Dependencies.coroutinesTest)
    testImplementation(Dependencies.kotlinTest)
}

tasks.test {
    useJUnitPlatform()
}

tasks.register<JavaExec>("uiTest") {
    group = "application"
    description = "Runs the Appium test suite"

    mainClass.set("com.worldwinner.fanduel.uitest.MainKt")
    classpath = sourceSets.main.get().runtimeClasspath

    args = listOf()
}

kotlin {
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_17)
    }
}
java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}