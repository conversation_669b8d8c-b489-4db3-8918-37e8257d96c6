package com.gametaco.app_android_fd.viewmodel

import com.gametaco.app_android_fd.utils.log.Logger
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.UIManager
import com.gametaco.app_android_fd.models.GameplayDataModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class GameplayViewModel : ViewModel() {

    private val uiManager: UIManager by lazy { UIManager.instance }
    private val tournamentManager: TournamentManager by lazy { TournamentManager.instance }

    init {
        Logger.d(TAG, "Init GameplayViewModel")

        if(!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this);
    }

    companion object {
        const val TAG = "GameplayViewModel"
        val instance: GameplayViewModel by lazy { GameplayViewModel() }
    }

    private val navManager: NavManager
        get() = NavManager.instance

    private val backgroundURL: Flow<String?>
        get() = tournamentManager.currentGameBackgroundImageUrl

    private val loadingProgress : MutableStateFlow<Float> = MutableStateFlow(0.0f)

    val hideLoadingScreenContent : MutableStateFlow<Boolean> = MutableStateFlow(false)

    var loadingScreenReady : Boolean = false

    val gameplayDataModel: StateFlow<GameplayDataModel?>
        = combine(loadingProgress, backgroundURL, hideLoadingScreenContent) { loadingProgress, backgroundURL, hideLoadingScreenContent ->
            GameplayDataModel(
                backgroundURL = backgroundURL,
                loadingProgress = loadingProgress,
                onAllImagesLoaded = { loadingScreenReady() },
                hideLoadingScreenContent = hideLoadingScreenContent
            )
        }.stateIn(viewModelScope, started = SharingStarted.WhileSubscribed(), initialValue = null)

    private var loadingScreenReadyCallback: ((Boolean) -> Unit)? = null

    private var currentRoute:String? = null

    fun setLoadingProgress(progress: Float) {
        //animate progress
        if(loadingScreenReady) {
            if (progress > 0) {
                loadingProgress.value = minOf(
                    loadingProgress.value + minOf(0.1f, progress - loadingProgress.value),
                    1.0f
                )
            }
        }
    }

    fun getLoadingProgress() : Float {
        return loadingProgress.value
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onGameReadyEventHandler(event: OnGameReadyEvent) {
        Logger.d(TAG, "onGameReadyEventHandler")
        hideLoadingScreenContent.value = true
        uiManager.hideLauncherView()
    }

    fun showGameplayScreen(callback: (Boolean) -> Unit) {
        Logger.d(TAG, "showGameplayScreen")

        tournamentManager.setIsInGame(true)
        loadingProgress.value = 0.0f
        loadingScreenReadyCallback = callback
        loadingScreenReady = false
        hideLoadingScreenContent.value = false
        currentRoute = navManager.currentDestinationRoute
        navManager.navigate(Routes.GAMEPLAY_SCREEN)
    }

    fun showLeaderboard(canGoBack : Boolean) {
        Logger.d(TAG, "showLeaderboard")

        updateDataAfterGame()
        if(canGoBack){
            navManager.navigate(currentRoute ?: Routes.SCORES_SCREEN)
        } else {
            navManager.navigateClearBackStack(currentRoute ?:Routes.SCORES_SCREEN, Routes.GAMES_SCREEN)
        }
        LeaderboardViewModel.instance.showLeaderboard(TournamentManager.instance.tournamentInstanceEntryId?:"", true)
    }

    fun loadingScreenReady() {
        Logger.d(TAG, "loadingScreenReady")
        loadingScreenReady = true
        loadingScreenReadyCallback?.invoke(true)
        loadingScreenReadyCallback = null
    }

    private fun updateDataAfterGame() {
        EventBus.getDefault().post(OnReLoadScoresDataEvent(true))
        EventBus.getDefault().post(OnReLoadGoalsDataEvent(true))
        EventBus.getDefault().post(OnReLoadPlayDataEvent(true))
    }
}


data class OnGameReadyEvent(val ready: Boolean)
