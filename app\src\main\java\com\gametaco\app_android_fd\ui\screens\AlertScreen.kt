package com.gametaco.app_android_fd.ui.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import com.gametaco.app_android_fd.data.AppConstants.STATE_LIST_URL
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.FDWebviewPage
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.ui.components.Background
import com.gametaco.app_android_fd.ui.components.CommonButton
import com.gametaco.app_android_fd.ui.theme.BlueMinimalBackground
import com.gametaco.app_android_fd.ui.theme.Typography
import com.gametaco.app_android_fd.ui.theme.bodyLargeAnnotated
import com.gametaco.app_android_fd.ui.utils.getNavigationBarHeight
import com.gametaco.app_android_fd.utils.parseHtml
import com.gametaco.app_android_fd.viewmodel.PlayViewModel
import com.gametaco.utilities.STR
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import resources.R

@Composable
fun AlertHighTraffic()
{
    Background(BlueMinimalBackground)

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Transparent)
            .clickable(onClick = { GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_NONE) }),
        contentAlignment = Alignment.Center
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(
                space = 16.dp,
                alignment = Alignment.CenterVertically
            ),
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .padding(68.dp, 0.dp)
        )
        {
            Image(
                painter = painterResource(id = R.drawable.ic_fanduel_full),
                alignment = Alignment.BottomCenter,
                contentDescription = null,
                modifier = Modifier
                    .size(200.dp, 184.dp)
            )
            Text(
                text = STR(R.string.we_are_experiencing_higher_than_normal_traffic),
                color = Color.White,
                textAlign = TextAlign.Center,
                fontSize = 16.sp,
                lineHeight = 18.sp
            )

            Text(
                text = STR(R.string.please_reload_the_app_or_try_again_in_a_few_minutes),
                color = Color.White,
                textAlign = TextAlign.Center,
                fontSize = 16.sp,
                lineHeight = 18.sp
            )
        }
    }
}

@Composable
fun AlertGeoGeneric(){
    // Geo Generic
    Background(BlueMinimalBackground)

    GeoAlertPage(
        title = STR(R.string.geo_somethings_gone_wrong),
        icon = R.drawable.ic_geo_warning,
        mainButtonFunction = {
            GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_NONE)
            FDManager.instance.showWebviewFD(FDWebviewPage.Support)
        },
        mainButtonTitle = STR(R.string.contact_support),
        secondaryButtonFunction = {
            GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_NONE)
        },
        secondaryButtonTitle = STR(R.string.dismiss),
    ){
        Text(text = STR(R.string.geo_generic_description), fontSize = 14.sp, color = Color.White)
    }
}

@Composable
fun AlertGeoNotAllowed(){
    val coroutineScope = rememberCoroutineScope()

    // Geo Not Allowed
    Background(BlueMinimalBackground)

    GeoAlertPage(
        title = STR(R.string.enjoy_free_games_on_faceoff),
        icon = R.drawable.ic_geo_warning,
        mainButtonFunction = {
            coroutineScope.launch {
                val hasGame = PlayViewModel.instance.game.value != null
                if (hasGame) {
                    NavManager.instance.navigate(Routes.CONTEST_SCREEN)
                    // Give the contest screen time to load before dismissing the alert
                    delay(500)
                }
                GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_NONE)
            }
         },
        mainButtonTitle = STR(R.string.play_free_games),
        secondaryButtonFunction = {
            GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_NONE)
            FDManager.instance.showWebviewFD(FDWebviewPage.Support)
        },
        secondaryButtonTitle = STR(R.string.contact_support),
    ){
        // Top text
        Text(text = STR(R.string.every_state_is_different), fontSize = 14.sp, color = Color.White)

        // Link text
        val str = STR(R.string.error_please_contact_support_states)
        Text(text = str.parseHtml(), fontSize = 14.sp, color = Color.White, modifier = Modifier.clickable {
            //Logger.d("SupportLink", "Clicked support 'state list' text link")
            FDManager.instance.showWebviewFD(page = null, sourceURL = STATE_LIST_URL)
        })

        // Bottom text
        Text(text = STR(R.string.error_please_contact_support), fontSize = 14.sp, color = Color.White)
    }
}

@Composable
private fun ColumnScope.TextGeoAlert(
    text: CharSequence,
    style: TextStyle = Typography.bodyLarge,
) {
    val textItems = text.split("\n")
        .filter { it != "\n" && it.isNotEmpty() }

    textItems.forEachIndexed { index, string ->
        Text(
            text = string,
            style = style,
            color = MaterialTheme.colorScheme.onPrimary,
        )
        if (index != textItems.size - 1) {
            Spacer(modifier = Modifier.height(8.dp))
        }
    }
}

@Composable
fun AlertGeoLocationService(){
    //Geo Location Service
    Background(BlueMinimalBackground)
    GeoAlertPage(
        title = STR(R.string.your_location_could_not_be_verified),
        icon = R.drawable.ic_geo_location,
        mainButtonFunction = { FDManager.instance.showWebviewFD(FDWebviewPage.Support) },
        mainButtonTitle = STR(R.string.contact_support),
        secondaryButtonFunction = { GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_NONE) },
        secondaryButtonTitle = STR(R.string.dismiss),
        paddingValues = PaddingValues(start = 24.dp, end = 24.dp, top = 48.dp, bottom = 16.dp),
    ) {
        val titleSpacing = 16.dp

        Column {
            TextGeoAlert(
                STR(R.string.turn_on_your_location_services),
                style = Typography.titleMedium,
            )
            Spacer(modifier = Modifier.height(titleSpacing))
            TextGeoAlert(text = STR(R.string.go_to_settings_privacy_location_services))

            Spacer(modifier = Modifier.height(24.dp))

            TextGeoAlert(
                STR(R.string.give_fanduel_faceoff_location_access),
                style = Typography.titleMedium,
            )
            Spacer(modifier = Modifier.height(titleSpacing))
            TextGeoAlert(text = STR(R.string.make_sure_precise_location_is_on))

            Spacer(modifier = Modifier.height(24.dp))

            TextGeoAlert(
                STR(R.string.give_fanduel_faceoff_location_access),
                style = Typography.titleMedium,
            )
            Spacer(modifier = Modifier.height(titleSpacing))

            val annotatedString: AnnotatedString = buildAnnotatedString {
                val str = STR(R.string.find_more_troubleshooting_steps_here)
                val here = STR(R.string.here)
                val startIndex = str.indexOf(here)
                val endIndex = startIndex + here.length

                withStyle(
                    style = Typography.bodyLarge.toSpanStyle()
                        .copy(color = MaterialTheme.colorScheme.onPrimary)
                ) {
                    append(str)
                }
                addStyle(style = bodyLargeAnnotated.toSpanStyle(), startIndex, endIndex)
            }

            ClickableText(text = annotatedString) {
                FDManager.instance.showWebviewFD(page = null, sourceURL = "https://support.fanduel.com/s/article/Location-Troubleshooting-Tips",title = "Location Troubleshooting Tips")
            }
        }
    }
}
@Composable
fun AlertGeoSpecificGame(){
    // Geo Specific Game
    Background(BlueMinimalBackground)
    GeoAlertPage(
        title = STR(R.string.please_try_a_different_game),
        icon = R.drawable.ic_geo_warning,
        mainButtonFunction = {
            GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_NONE)
            FDManager.instance.showWebviewFD(page = null, sourceURL = "https://support.fanduel.com/s/article/Where-can-I-play-Faceoff", title = "Where can I play Faceoff")
        },
        mainButtonTitle = STR(R.string.get_more_information),
        secondaryButtonFunction = {
            GeoAlertModel.instance.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_NONE)
        },
        secondaryButtonTitle = STR(R.string.play_a_different_game),
    ) {
        val annotatedString: AnnotatedString = buildAnnotatedString {
            val str = STR(R.string.error_please_try_a_different_game)
            val here = STR(R.string.state_specific_game_restrictions)
            val startIndex = str.indexOf(here)
            val endIndex = startIndex + here.length
            withStyle(
                style = Typography.bodyLarge.toSpanStyle()
                    .copy(color = MaterialTheme.colorScheme.onPrimary)
            )
            {
                append(str)
            }
            addStyle(style = bodyLargeAnnotated.toSpanStyle(), startIndex, endIndex)
        }
        ClickableText(text = annotatedString, onClick =
        {
            FDManager.instance.showWebviewFD(page = null, sourceURL = "https://support.fanduel.com/s/article/Where-can-I-play-Faceoff", title = "Where can I play Faceoff")
        })
    }
}

@Composable
fun GeoAlertPage(
    title: String,
    icon: Int,
    mainButtonFunction: ()-> Unit,
    mainButtonTitle: String,
    secondaryButtonFunction: ()-> Unit,
    secondaryButtonTitle: String,
    paddingValues: PaddingValues = PaddingValues(24.dp, 48.dp),
    content: @Composable ()-> Unit,
)
{
    Column (Modifier.pointerInput(Unit) { /* Prevent click through / drag through to page behind */ }) {
        Column (
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(
                space = 16.dp,
                alignment = Alignment.Top
            ),
            modifier = Modifier
                .padding(paddingValues)
                .weight(1f, false)
                .fillMaxSize()
        ){
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium.copy(fontSize = 22.sp),
                color = Color.White,
                textAlign = TextAlign.Center,
            )
            Image(
                painter = painterResource(icon),
                alignment = Alignment.TopCenter,
                contentScale = ContentScale.FillWidth,
                contentDescription = STR(R.string.content_description_check),
                modifier = Modifier.size(200.dp, 136.dp)
            )
            content()
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(108.dp)
                .background(color = Color(0xFF005FC8))
        )
        {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(start = 48.dp, top = 24.dp, end = 48.dp, bottom = 8.dp),

                verticalArrangement = Arrangement.Absolute.spacedBy(8.dp, Alignment.CenterVertically),
                horizontalAlignment = Alignment.CenterHorizontally,
            )
            {
                CommonButton(title = mainButtonTitle, fillColor = Color(0xFF128000), hasShadow = true) {
                    mainButtonFunction()
                }
                Box(
                    modifier = Modifier.fillMaxSize()
                )
                {
                    Text(
                        text = secondaryButtonTitle,
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.White,
                        modifier = Modifier
                            .clickable { secondaryButtonFunction() }
                            .align(Alignment.Center)
                            .padding(0.dp)
                    )
                }
            }
        }
        Box(
            modifier = Modifier
                .height(getNavigationBarHeight())
                .fillMaxWidth()
                .background(color = Color(0xFF005FC8)),
        ) {}
    }
}
enum class GeoAlertType(val value: Int) {
    GEO_ALERT_NONE(0),
    GEO_ALERT_LOCATION(1),
    GEO_ALERT_NOT_ALLOWED(2),
    GEO_ALERT_SPECIFIC_GAME(3),
    GEO_ALERT_GENERIC(4),
    ALERT_HIGH_TRAFFIC(5)
}

@Composable
fun AlertGeo() {
    val screenType by GeoAlertModel.instance.showGeoBlockedAlert.collectAsState()
    when(screenType) {
        GeoAlertType.GEO_ALERT_NONE -> { return }
        GeoAlertType.GEO_ALERT_LOCATION -> { AlertGeoLocationService() }
        GeoAlertType.GEO_ALERT_NOT_ALLOWED -> { AlertGeoNotAllowed() }
        GeoAlertType.GEO_ALERT_SPECIFIC_GAME -> { AlertGeoSpecificGame() }
        GeoAlertType.GEO_ALERT_GENERIC -> { AlertGeoGeneric() }
        GeoAlertType.ALERT_HIGH_TRAFFIC -> { AlertHighTraffic() }
    }
}



class GeoAlertModel : ViewModel() {
    companion object {
        const val TAG = "GeoAlertModel"
        val instance: GeoAlertModel
            get() = resolve()
    }

    private val _showGeoBlockedAlert = MutableStateFlow(GeoAlertType.GEO_ALERT_NONE)
    val showGeoBlockedAlert = _showGeoBlockedAlert.asStateFlow()
    fun showHideGeoBlockedAlert(value:GeoAlertType){
        _showGeoBlockedAlert.value = value
    }
    
    fun getGeoBlockedAlertScreen() : GeoAlertType {
        return _showGeoBlockedAlert.value
    }
}

@Composable
@Preview
fun AlertViewPreview(){
//    AlertHighTraffic()
    AlertGeoNotAllowed()
//    AlertGeoLocationService()
//    AlertGeoSpecificGame()
}