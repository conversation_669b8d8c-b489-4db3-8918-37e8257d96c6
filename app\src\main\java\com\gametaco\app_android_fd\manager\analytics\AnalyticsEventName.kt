package com.gametaco.app_android_fd.manager.analytics

sealed class AnalyticsEventName(val value: String) {

    data object Game_Failed : AnalyticsEventName("Game Failed")
    data object Open_Webview : AnalyticsEventName("Webview Open")
    data object Open_Fanduel_Webview : AnalyticsEventName("Fanduel Webview Open")
    data object Joined_Game : AnalyticsEventName("Game Launched")
    data object Game_Result_Leaderboard : AnalyticsEventName("Results Viewed")
    data object First_Launch : AnalyticsEventName("First Launch")
    data object Welcome_Viewed : AnalyticsEventName("Welcome Page Viewed")
    data object Completed_Registration : AnalyticsEventName("Guest Registration Successful")
    data object Game_Started : AnalyticsEventName("Game Started")
    data object Game_Guest_Started : AnalyticsEventName("Guest Play Started")
    data object Game_Ended : AnalyticsEventName("Game Played")
    data object Game_Guest_Ended : AnalyticsEventName("Guest Game Played")
    data object Rewards_Tab_Viewed : AnalyticsEventName("Rewards Tab Viewed")
    data object Scores_Tab_Viewed : AnalyticsEventName("Score Tab Viewed")
    data object Play_Tab_Viewed : AnalyticsEventName("Play Tab Viewed")
    data object Account_Tab_Viewed : AnalyticsEventName("Account Tab Viewed")
    data object Modal_Closed : AnalyticsEventName("Modal Closed")
    data object Modal_Open : AnalyticsEventName("Modal Open")
    data object Promotion_Opened : AnalyticsEventName("Promotion Opened")
    data object Game_Selected : AnalyticsEventName("Game Selected")
    data object Reward_Claimed : AnalyticsEventName("Reward Claimed")
    data object Replay_Upload_Success : AnalyticsEventName("Replay Upload Did Succeed")
    data object Replay_Upload_Failure : AnalyticsEventName("Replay Upload Did Fail")
    data object Results_Closed : AnalyticsEventName("Results Closed")
    data object Replay_Modal_Viewed : AnalyticsEventName("Replay Modal Viewed")
    data object Replay_Video_Load_Error : AnalyticsEventName("Replay Video Load Error")
//    data object Replay_Video_Tracking : AnalyticsEventName("Replay Video Tracking")
}
