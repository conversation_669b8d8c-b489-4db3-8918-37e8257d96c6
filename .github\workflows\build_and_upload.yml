name: Build and Upload to S3

on:
  # schedule:
  #  - cron: '0 23 * * *'  # 9am AEST (11pm UTC previous day)
  #  - cron: '30 5 * * *'   # 3pm AEST (5am UTC)
  workflow_dispatch:
    inputs:
      buildType:
        description: 'Type of build to perform (Unity/NoUnity/Production)'
        required: true
        default: 'Unity'

jobs:
  prepare-build-environment:
    runs-on: self-hosted
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          lfs: true

      - name: GetUnity
        run: |
          git submodule update --init --recursive

      - name: Prepare Build Environment
        run: |
          make build-checks
        shell: bash

  check-for-changes:
    runs-on: self-hosted
    needs: prepare-build-environment
    outputs:
      changes: ${{ steps.check_changes.outputs.changes }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: "us-west-2"

      - name: Check if AWS CLI is installed
        id: check_aws_cli
        run: |
          if ! command -v aws &> /dev/null; then
            echo "AWS CLI is not installed"
            echo "install_aws_cli=true" >> $GITHUB_ENV
          else
            echo "AWS CLI is already installed"
            echo "install_aws_cli=false" >> $GITHUB_ENV
          fi

      - name: Install AWS CLI
        if: ${{ env.install_aws_cli == 'true' }}
        run: |
          sudo apt-get update
          sudo apt-get install -y awscli

      - name: Download last build SHA from S3
        run: |
          aws s3 cp s3://faceoff-android-dev-release/_last_build_sha.txt _last_build_sha.txt

      - name: Retrieve last build SHA
        id: get_last_sha
        run: |
          if [ -f _last_build_sha.txt ]; then
            last_commit=$(cat _last_build_sha.txt)
          else
            last_commit=$(git rev-list --max-parents=0 HEAD)  # Initial commit
          fi
          echo "last_commit=$last_commit" >> $GITHUB_OUTPUT

      - name: Check for changes
        id: check_changes
        run: |
          last_commit=${{ steps.get_last_sha.outputs.last_commit }}
          current_commit=$(git rev-parse HEAD)
          if [ "$last_commit" = "$current_commit" ]; then
            echo "No new changes found."
            echo "changes=false" >> $GITHUB_OUTPUT
          else
            echo "New changes found."
            echo "changes=true" >> $GITHUB_OUTPUT
          fi
          echo "Check for changes result: ${{ steps.check_changes.outputs.changes }}"

  set-build-type:
    runs-on: self-hosted
    outputs:
      buildType: ${{ steps.set-output.outputs.buildType }}
    steps:
      - name: Set build type
        id: set-output
        run: |
            echo "::set-output name=buildType::${{ github.event.inputs.buildType || 'Unity' }}"

  build:
    if: needs.check-for-changes.outputs.changes == 'true' && needs.set-build-type.outputs.buildType == 'Unity'
    runs-on: self-hosted
    needs: [prepare-build-environment, check-for-changes, set-build-type]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: BuildRelease
        run: |
          make build-release

      - name: Upload to s3 Release
        run: |
          ./signanduploadtos3.sh "./app/build/outputs/apk/withUnityDevelopmentDefault/release/app-withUnity-development-default-release-unsigned.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "Release"

      - name: BuildDebug
        run: |
          make build-debug

      - name: Upload to s3 Debug
        run: |
          ./signanduploadtos3.sh "./app/build/outputs/apk/withUnityDevelopmentDefault/debug/app-withUnity-development-default-debug.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "Debug"

      - name: Trigger Increment Build Number Workflow
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: Increment Build Number
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Trigger Save Commit SHA Workflow
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: Save Commit SHA
          token: ${{ secrets.GITHUB_TOKEN }}

  buildNoUnity:
    if: needs.check-for-changes.outputs.changes == 'true' && needs.set-build-type.outputs.buildType == 'NoUnity'
    runs-on: self-hosted
    needs: [prepare-build-environment, check-for-changes, set-build-type]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: BuildReleaseNoUnity
        run: |
          make build-release-nounity

      - name: Upload to s3 Release NoUnity
        run: |
          ./signanduploadtos3.sh "./app/build/outputs/apk/NoUnityDevelopmentDefault/release/app-NoUnity-development-default-release-unsigned.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "ReleaseNoUnity"

      - name: BuildDebugNoUnity
        run: |
          make build-debug-nounity

      - name: Upload to s3 Debug NoUnity
        run: |
          ./signanduploadtos3.sh "./app/build/outputs/apk/NoUnityDevelopmentDefault/debug/app-NoUnity-development-default-debug.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "DebugNoUnity"

      - name: Trigger Increment Build Number Workflow
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: Increment Build Number
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Trigger Save Commit SHA Workflow
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: Save Commit SHA
          token: ${{ secrets.GITHUB_TOKEN }}
          
          
  buildProduction:
    if: needs.check-for-changes.outputs.changes == 'true' && needs.set-build-type.outputs.buildType == 'Production'
    runs-on: self-hosted
    needs: [prepare-build-environment, check-for-changes, set-build-type]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: BuildProductionRelease
        run: |
          make build-production-release

      - name: Upload to s3 ProductionRelease
        run: |
          ./signanduploadtos3.sh "./app/build/outputs/apk/withUnityProductionDefault/release/app-withUnity-production-default-release-unsigned.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "ProductionRelease"

      - name: BuildProductionDebug
        run: |
          make build-production-debug

      - name: Upload to s3 ProductionDebug
        run: |
          ./signanduploadtos3.sh "./app/build/outputs/apk/withUnityProductionDefault/debug/app-withUnity-production-default-debug.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "ProductionDebug"

      - name: Trigger Increment Build Number Workflow
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: Increment Build Number
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Trigger Save Commit SHA Workflow
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: Save Commit SHA
          token: ${{ secrets.GITHUB_TOKEN }}          
          