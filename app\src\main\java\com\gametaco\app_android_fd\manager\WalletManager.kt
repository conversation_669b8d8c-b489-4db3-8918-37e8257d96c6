package com.gametaco.app_android_fd.manager

import AppsFlyerManager
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.APIWalletResponse
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.manager.analytics.AppsFlyerEventFactory
import com.gametaco.app_android_fd.utils.CoroutineScopes
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.viewmodel.OnReLoadGoalsDataEvent
import com.gametaco.utilities.ResourceState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class WalletManager(
    private val worldWinnerAPI: WorldWinnerAPI,
    private val authenticationManager: AuthenticationManager,
    private val coroutineScopes: CoroutineScopes,
    private val errorManager: ErrorManager,
    private val brazeManager: BrazeManager
) {
    companion object {
        const val TAG = "WalletManager"
        val instance: WalletManager
            get() = resolve()
    }

    private val _wallet = MutableStateFlow(APIWalletResponse("0", "0", "0", "0"))
    private var initialRefresh = true
    val wallet: StateFlow<APIWalletResponse> = _wallet.asStateFlow()
    var lastDepositAmount: String = ""
    private val _isWalletValid = MutableStateFlow(false)
    val isWalletValid = _isWalletValid.asStateFlow()

    fun refreshWallet(withBrazeEvent:Boolean = false) {
        if(!authenticationManager.isGuest) {
            coroutineScopes.io.launch {
                val res = worldWinnerAPI.getWallet()
                if (res.isSuccess()) {
                    _isWalletValid.value = true
                    val data = (res as ResourceState.Success).data

                    if (data.balance == _wallet.value.balance) {
                        if (withBrazeEvent){//back from deposit page but did not deposit
                            brazeManager.logEvent(BrazeEventName.Visited_Deposit_Screen_But_Did_Not_Deposit.value)
                        }
                    } else {
                        brazeManager.setCashBalance(data.balance)
                    }

                    var sharedDepositDelta = data.shared_deposit_balance.toDouble() - _wallet.value.shared_deposit_balance.toDouble()
                    if (sharedDepositDelta > 0) {
                        lastDepositAmount = sharedDepositDelta.toString()
                        EventBus.getDefault().post(OnReLoadGoalsDataEvent(true))

                        if (!initialRefresh) {
                            // Putting the AppsFlyer deposit event here for now until a specific deposit API call is added by WW
                            Logger.d(TAG, "Deposit made ($$lastDepositAmount)")
                            AppsFlyerManager.instance.logEvent(AppsFlyerEventFactory.instance.deposit)
                        }
                    }

                    _wallet.value = data
                    initialRefresh = false
                } else if(res.isError()) {
                    _isWalletValid.value = false
                    errorManager.handleResourceStateError(res)
                    Logger.e("Wallet API", "fetch wallet error")
                }
            }
        }else{
            _isWalletValid.value = true
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSessionChangedEventHandler(event: OnSessionChangedEvent) {
        Logger.d(TAG, "onSessionChangedEventHandler: $event")
        if (!event.loggedIn) {
            // Reset initialRefresh and lastDepositAmount if the user logs out
            initialRefresh = true
            lastDepositAmount = ""
            Logger.d(TAG, "initialRefresh and lastDepositAmount reset")
        }
    }

    init {
        if(!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this)
    }

}