package com.gametaco.app_android_fd.ui.components

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.gametaco.app_android_fd.data.events.EventBusExample
import com.gametaco.app_android_fd.utils.log.Logger
import org.greenrobot.eventbus.EventBus

var isUnityLoaded = false

const val TAG_UNITY_ACTIVITY_LAUNCHER = "UnityActivityLauncher"

@Composable
fun UnityActivityLauncher() {
    val TAG = TAG_UNITY_ACTIVITY_LAUNCHER
    val context = LocalContext.current

    Row(
        modifier = Modifier.padding(16.dp)
    ) {
        Button(
            onClick = {
                EventBus.getDefault().post(EventBusExample("LaunchingUnity"))
                Logger.d(TAG, "Clicked 'Launch Unity' button")
//                val mainActivity = context as MainActivity
//                mainActivity.loadUnity()
            },
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
        ) {
            Text("Launch Unity")
        }

        Button(
            onClick = {
                Logger.d(TAG, "Clicked 'Unload Unity' button")
 //               val mainActivity = context as MainActivity
//                mainActivity.unloadUnity(true)
            },
            enabled = isUnityLoaded,
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
        ) {
            Text("Unload Unity")
        }
    }
}