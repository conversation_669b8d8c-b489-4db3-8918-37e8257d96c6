package com.gametaco.app_android_fd.ui.components

import android.net.Uri
import androidx.annotation.OptIn
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.VideoSize
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.PlayerView
import com.gametaco.app_android_fd.utils.log.Logger

@OptIn(UnstableApi::class)
@Composable
fun VideoComponent(videoUrl: String, aspectRatio: Float = 1f, showController:Boolean = false, successCallback : ((<PERSON><PERSON><PERSON>,String?) -> Unit)? = null,videoSizeCallback:((VideoSize) -> Unit)?= null) {
    val context = LocalContext.current

    val player = remember {
        ExoPlayer.Builder(context)
            .build()
            .apply {
                try {
                    setMediaItem(MediaItem.fromUri(Uri.parse(videoUrl)))
                    playWhenReady = true
                    repeatMode = Player.REPEAT_MODE_ONE
                    prepare()
                    addListener(object : Player.Listener {
                        override fun onPlaybackStateChanged(state: Int) {
                            when (state) {
                                Player.STATE_ENDED -> {
                                    // Optionally, handle the playback completion here.
                                    Logger.d("VideoComponent", "Playback ended")
                                }
                            }
                        }

                        override fun onIsPlayingChanged(isPlaying: Boolean) {
                            if (isPlaying) {
                                // Video is playing successfully
                                successCallback?.invoke(true,null)
                            }
                        }

                        override fun onPlayerError(error: PlaybackException) {
                            // Handle playback errors and invoke callback with failure.
                            Logger.e("VideoComponent", "Playback error", error)
                            when (error.errorCode) {
                                PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED -> {
                                    // Handle network connection error
                                    Logger.d("VideoComponent","Network connection error")
                                }

                                PlaybackException.ERROR_CODE_IO_FILE_NOT_FOUND -> {
                                    // Handle file not found error
                                    Logger.d("VideoComponent","File not found")
                                }

                                PlaybackException.ERROR_CODE_DECODER_INIT_FAILED -> {
                                    // Handle decoder initialization error
                                    Logger.d("VideoComponent","Decoder initialization error")
                                }

                                else -> {
                                    // Handle other types of errors
                                    Logger.d("VideoComponent","Other error: ${error.message}")
                                }
                            }
                            successCallback?.invoke(false,error.message)
                        }

                        override fun onVideoSizeChanged(videoSize: VideoSize) {
                            Logger.d("VideoComponent", "Video Size: ${videoSize.width} x ${videoSize.height}")
                            videoSizeCallback?.invoke(videoSize)
                        }
                    })
                } catch (e: Exception) {
                    Logger.d("VideoComponent", "Caught exception: ${e.message}")
                    successCallback?.invoke(false,e.message)
                }
        }
    }

    DisposableEffect(player) {
        onDispose {
            player.release()
        }
    }

    AndroidView(
        factory = { ctx ->
            PlayerView(ctx).apply {
                useController = showController
                resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FIT
                this.player = player
            }
        },
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(aspectRatio)
    )
}
