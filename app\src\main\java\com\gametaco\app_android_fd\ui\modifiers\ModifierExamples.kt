package com.gametaco.app_android_fd.ui.modifiers

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.overlay
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.ViewModel
import com.gametaco.app_android_fd.utils.log.Logger
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class ExampleViewModel : ViewModel() {
    private val _data = MutableStateFlow("")
    val data: StateFlow<String> = _data.asStateFlow()

    fun updateData(newData: String) {
        _data.value = newData
    }
}
@Preview
@Composable
fun ModifierExamples() {
    var tapsDetected by remember { mutableStateOf(0) }
    var viewModel by remember {
        mutableStateOf(ExampleViewModel())
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.LightGray)
            .detectMultiTap(numberOfTapsRequired = 3) {
                tapsDetected++
                viewModel.updateData("tap $tapsDetected")
            }
            .onChange(viewModel, viewModel.data){
                Logger.d("@@@changed to:$it")
            }
    ) {
        Text(
            "Number of triple taps detected: $tapsDetected",
            modifier = Modifier
                .align(Alignment.Center)
                .overlay(Color.Green.copy(0.3f))
                .onAppear {
                    Logger.d("on appear")
                }
                .onDisappear {
                    Logger.d("on disappear...")
                }
                .onChange(viewModel, viewModel.data){
                    Logger.d("###changed to:$it")
                }
        )
    }
}