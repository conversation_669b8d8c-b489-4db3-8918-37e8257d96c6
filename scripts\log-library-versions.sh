#!/bin/bash

set -euo pipefail

echo "Running script..."

if [[ $# -ne 1 ]]; then
  echo "Usage: $0 group:artifact"
  exit 1
fi

INPUT="$1"
echo "Input: $INPUT"

IFS=":" read -r GROUP ARTIFACT <<< "$INPUT"
echo "Parsed GROUP: $GROUP"
echo "Parsed ARTIFACT: $ARTIFACT"

GROUP_PATH=$(echo "$GROUP" | tr '.' '/')
echo "Converted GROUP_PATH: $GROUP_PATH"

URL="https://fanduel.jfrog.io/artifactory/fd-maven/${GROUP_PATH}/${ARTIFACT}/maven-metadata.xml"
echo "Fetching: $URL"

if [[ -z "${FD_MAVEN_READ_USERNAME:-}" ]]; then
  echo "FD_MAVEN_READ_USERNAME is not set"
fi

if [[ -z "${FD_MAVEN_READ_TOKEN:-}" ]]; then
  echo "FD_MAVEN_READ_TOKEN is not set"
fi

RESPONSE=$(curl -sfu "$FD_MAVEN_READ_USERNAME:$FD_MAVEN_READ_TOKEN" "$URL") || {
  echo "Failed to fetch metadata. Curl exit code: $?"
  exit 1
}

#echo "Raw XML response:"
#echo "$RESPONSE"

VERSIONS=$(echo "$RESPONSE" | xmllint --xpath '//metadata/versioning/versions/version/text()' - 2>/dev/null | tr ' ' '\n' || true)

if [[ -z "$VERSIONS" ]]; then
  echo "No versions found"
else
  echo "Found versions for $1:"
  echo "$VERSIONS" | sort -V
fi
