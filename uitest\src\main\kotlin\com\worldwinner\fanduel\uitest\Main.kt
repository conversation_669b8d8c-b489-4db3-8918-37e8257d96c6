package com.worldwinner.fanduel.uitest

import com.worldwinner.fanduel.uitest.appium.AppiumManager
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory

/**
 * See [README.md](../README.md). As a rule, you're better off running via [FanDuelUiTest] instead.
 */
fun main() = runBlocking {
    val logger = LoggerFactory.getLogger("Main.kt")

    val appiumManager = AppiumManager()
    val uiTestManager = UiTestManagerImpl(appiumManager)

    var result: UiTestResult? = null
    appiumManager.run(uiTestManager) {
        result = uiTestManager.runToSignIn_success()
    }

    logger.error("Test result: $result")
}