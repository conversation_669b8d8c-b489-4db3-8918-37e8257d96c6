package com.worldwinner.fanduel.uitest
import org.junit.jupiter.api.Test


class FanduelUiTest : FanduelUiTestBase() {

    // Navigation Tests
    @Test
    fun runToLobbyTab() = uiTest {
        uiTestManager.runToLobbyTab()
    }

    @Test
    fun runToRewardsTab() = uiTest {
        uiTestManager.runToRewardsTab()
    }

    @Test
    fun checkSectionsOnRewardsPage() = uiTest {
        uiTestManager.checkSectionsOnRewardsPage()
    }

    @Test
    fun runToScoresTab() = uiTest {
        uiTestManager.runToScoresTab()
    }

    //Account Page
    @Test
    fun runToAccountTab() = uiTest {
        uiTestManager.runToAccountTab()
    }

    @Test
    fun checkAccountPageSections() = uiTest {
        uiTestManager.checkAccountPageSections()
    }

    //Game List Verification Tests
    @Test
    fun runToLobby_verifyRecentlyPlayed() = uiTest {
        uiTestManager.runToLobby_verifyRecentlyPlayed()
    }

    @Test
    fun runToLobby_verifyPopularGames() = uiTest {
        uiTestManager.runToLobby_verifyPopularGames()
    }

    @Test
    fun runToLobby_verifyAllGames() = uiTest {
        uiTestManager.runToLobby_verifyAllGames()
    }

    @Test
    fun runToLobby_verifyGameCount() = uiTest {
        uiTestManager.runToLobby_verifyGameCount()
    }

    // Close Dialog Test
    @Test
    fun runToCloseDialog_whenVisible() = uiTest {
        uiTestManager.runToCloseDialog_whenVisible()
    }


    @Test
    fun runToTurboPopup() = uiTest {
        uiTestManager.runToTurboPopup()
    }
}