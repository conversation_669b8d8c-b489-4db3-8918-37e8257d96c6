package com.gametaco.app_android_fd.viewmodel

import android.webkit.CookieManager
import android.webkit.WebView
import androidx.compose.runtime.Composable
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.gametaco.app_android_fd.MainApplication
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.ActivityManager
import com.gametaco.app_android_fd.manager.AlertDialogManager
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.ErrorManager
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.FDWebviewPage
import com.gametaco.app_android_fd.manager.GeoComplyManager
import com.gametaco.app_android_fd.manager.LoadingManager
import com.gametaco.app_android_fd.manager.LocationManager
import com.gametaco.app_android_fd.manager.MaintenanceModeManager
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.OnGeocomplyTokenExchangeEvent
import com.gametaco.app_android_fd.manager.OnSessionChangedEvent
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.UIManager
import com.gametaco.app_android_fd.models.LoginDataModel
import com.gametaco.app_android_fd.ui.popups.AlertDialogRestrictedStatus
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.viewmodel.LoginViewModel.Companion.instance
import com.gametaco.app_android_fd.viewmodel.LoginViewModel.Companion.instanceExists
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class LoginViewModel : ViewModel() {

    private val uiManager: UIManager
        get() = UIManager.instance

    private val navManager: NavManager
        get() = NavManager.instance

    private val _counter = MutableStateFlow(0f)

    val loginDataModel: StateFlow<LoginDataModel> =
        _counter.map { progress -> LoginDataModel(progress) }
            .stateIn(viewModelScope, SharingStarted.Eagerly, LoginDataModel(0f))

    private fun startProgressCounter() {
        viewModelScope.launch {
            val durationMillis = 20 * 1000          // 20 seconds
            val updateIntervalMillis = 1000 / 10    // 10 times a second
            val increments = durationMillis / updateIntervalMillis
            val incrementValue = 1f / increments

            for (i in 0 until increments) {
                _counter.value += incrementValue
                delay(updateIntervalMillis.toLong())
            }
            _counter.value = 1f
        }
    }

    fun setLoadingProgress(progress : Float) {
        _counter.value = progress
    }

    init {
        Logger.d(TAG, "Init LoginViewModel")

        startProgressCounter()

        if(!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this);

        clearSplashScreen()

        viewModelScope.launch(Dispatchers.Main) {
            MaintenanceModeManager.instance.pollMaintenanceModeStatus()

            //wait for maintenance mode manager to return any response
            while (!MaintenanceModeManager.instance.hasResponse) {
                delay(30L)
            }

            //do nothing if maintenance is required
            if(MaintenanceModeManager.instance.maintenanceRequired())
                return@launch

            if(MaintenanceModeManager.instance.responseIsError) {
                if(MaintenanceModeManager.instance.maintenanceResponse != null) {
                    ErrorManager.instance.handleResourceStateError(MaintenanceModeManager.instance.maintenanceResponse!!)
                    //https://kromestudios.atlassian.net/browse/KF-1510
                    //is there is no network, still need to show lobby
                    if(FDManager.instance.hasSessionData){
                        NavManager.instance.navigateClearBackStack(Routes.GAMES_SCREEN)
                    }else{
                        resetLogin()
                        NavManager.instance.navigateClearBackStack(Routes.WELCOME_SCREEN)
                    }
                    return@launch
                }
            }

            performLogin()
        }
    }

    private fun resetLogin() {
        AuthenticationManager.instance.resetLogin()
    }

    private fun performLogin() {
        Logger.d(TAG, "performLogin()")
        LocationManager.instance.checkPermissions(true) { granted ->
            viewModelScope.launch(Dispatchers.Main) {

                //ensure FDManager is initialized
                while(!FDManager.instance.initialized) {
                    delay(100)
                }

                val loginResult = tryAutomaticLogin()
                Logger.d(TAG, "tryAutomaticLogin(): result: $loginResult")
                if (!loginResult) {
                    //https://kromestudios.atlassian.net/browse/KF-1510
                    //is there is no network, still need to show lobby
                    if(!FDManager.instance.hasSessionData){
                        NavManager.instance.navigateClearBackStack(Routes.WELCOME_SCREEN)
                    }
                } else {
                    GeoComplyManager.instance.requestGeolocation()
                }

                delay(2000)
            }
        }
    }

    // Called when the app is resumed. See #FX-3002.
    fun tryAndRefreshLogin() {
        if (FDManager.instance.hasSessionData) {
            Logger.d(TAG, "tryAndRefreshLogin() - call performLogin()")
            performLogin()
        }
    }

    fun clearSplashScreen() {
        uiManager.setMainTheme()
        uiManager.showLauncherView()
    }


    companion object {
        const val TAG = "LoginViewModel"

        /**
         * Non-destructive way of checking if the instance exists. Code should be careful calling
         * [instance] before the app is ready, or else you risk introducing bugs. By checking
         * [instanceExists] first, you can ensure [instance] is only used when it is safe to do so.
         */
        private var _instanceExists = false
        val instanceExists: Boolean
            get() = _instanceExists

        val instance: LoginViewModel by lazy {
            LoginViewModel().also {
                _instanceExists = true
            }
        }
    }

    fun loginPressed() {
        resetWebview() //this is needed to ensure that the webview doesn't have any cookies from previous logins
        FDManager.instance.login()
    }


    private suspend fun tryAutomaticLogin() : Boolean {
        Logger.d("LoginViewModel", "tryAutomaticLogin()")
        //check guest first
        if(AuthenticationManager.instance.isGuest && AuthenticationManager.instance.authToken != null)
        {
            AuthenticationManager.instance.getGuestMe()
            return true
        }

        LoadingManager.instance.showLoading()
        //try get existing UM session data
        FDManager.instance.tryGetExistingSessionData()
        LoadingManager.instance.hideLoading()
        //check if fanduel session data is available
        if(!FDManager.instance.hasSessionData) {
            return false
        }

        //check if auth token exists
        if(AuthenticationManager.instance.authToken == null)
            return false

        //try get app session
        var sessionResult = AuthenticationManager.instance.getAppSession()

        //try get me
        var meResult = AuthenticationManager.instance.getMe()

        if(meResult.isError()) {
            //https://kromestudios.atlassian.net/browse/KF-1510
            //is there is no network, still need to show lobby and not reset login
//            resetLogin()
            return false
        }

        //need to refresh login to refresh restricted status
        if(!AuthenticationManager.instance.refreshLoginForRestrictedStatus())
            return false

        AuthenticationManager.instance.isGuest = false

        if (meResult.isSuccess()) {
            // Refresh experiment data upon a login refresh. See FX-3346 and FX-3347.
            ExperimentManager.instance.refreshExperimentUser()
        }

        return true
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSessionChangedEventHandler(event: OnSessionChangedEvent) {
        Logger.d(TAG, "onSessionChangedEventHandler: $event")

//        println("FDManager.instance.webviewVisible: ${FDManager.instance.webviewVisible}")

        // Don't bring the UI to the front if the webview is visible or if the current activity is
        // UM related. See FX-3521
        if (!FDManager.instance.webviewVisible
            && !ActivityManager.instance.isCurrentActivityUmRelated()) {
            uiManager.bringToFront()
        }

        if(!event.loggedIn)
        {
            if(AuthenticationManager.instance.authToken != null || FDManager.instance.hasSessionData) {
                resetLogin()

                //FIXME: this is a hack to force the awwebview to forget the user
                resetWebview()
            }

            if (navManager.currentBackStackId != Routes.WELCOME_SCREEN) {
                navManager.navigateClearBackStack(Routes.WELCOME_SCREEN)
            }

        } else {
            GeoComplyManager.instance.requestGeolocation()
        }
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onGeocomplyTokenExchangeHandler(event: OnGeocomplyTokenExchangeEvent){
        Logger.d(TAG, "onGeocomplyTokenExchangeHandler: $event")

        //this is where we go to lobby screen after geocomply token exchange at login time
        if(navManager.currentDestinationRoute == Routes.LOGIN_SCREEN) {
            PreferencesManager.instance.checkAndRemoveExpiredKeys()
            navManager.navigateClearBackStack(Routes.GAMES_SCREEN)
            ScoresViewModel.instance.loadAll()
            GoalsViewModel.instance.fetchGoals()
            if(ExperimentManager.instance.isReplayEnabled.value && !PreferencesManager.instance.getIsReplayViewed()){
                GoalsViewModel.instance.openReplayPopup()
            }
            else if(ExperimentManager.instance.isDailyRewardEnabled.value)
            {
                GoalsViewModel.instance.checkAndNavigateToDailyRewardIfAvailable()
            }

            //show login popup for restricted status
            if(AuthenticationManager.instance.restrictedStatus) {
                AlertDialogManager.instance.showDialogCustom(composeFunction = @Composable {
                    AlertDialogRestrictedStatus(onAPressed = {
                        FDManager.instance.showWebviewFD(FDWebviewPage.Deposit)
                    },
                    onBPressed = { })
                })
            }
        }
    }

    fun resetWebview() {

        WebView(MainApplication.context).apply { clearHistory() }

        val cookieManager = CookieManager.getInstance()

        // Clear all cookies asynchronously
        cookieManager.removeAllCookies { success ->
            if (success) {
                Logger.d("All cookies removed successfully!")
            } else {
                Logger.e("Failed to remove cookies.")
            }
        }

        // Flush cookies to persistent storage if necessary
        cookieManager.flush()

    }


    fun fanduelSignupFromGuestMode() {
        setLoadingProgress(1f)
        if(LeaderboardViewModel.instance.isShowing()){
            LeaderboardViewModel.instance.closeLeaderboard()

            if(AuthenticationManager.instance.showLobbyFtue){
                LeaderboardViewModel.instance.closeLeaderboardFtue()
            }
        }
        NavManager.instance.navigate(Routes.LOGIN_SCREEN)
        FDManager.instance.signup()
    }

}