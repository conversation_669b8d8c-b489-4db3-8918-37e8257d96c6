package com.gametaco.app_android_fd.viewmodel

import AppsFlyerManager
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.APIPlayedGameIDResponse
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntry
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntryResult
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceStatus
import com.gametaco.app_android_fd.data.entity.APITournamentPlayerProfile
import com.gametaco.app_android_fd.data.entity.APITournamentUser
import com.gametaco.app_android_fd.data.entity.APITournamentUserStatus
import com.gametaco.app_android_fd.data.entity.EntryFee
import com.gametaco.app_android_fd.manager.AlertDialogManager
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.BrazeEventName
import com.gametaco.app_android_fd.manager.BrazeManager
import com.gametaco.app_android_fd.manager.ErrorManager
import com.gametaco.app_android_fd.manager.PermissionsManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.manager.analytics.AppsFlyerEventFactory
import com.gametaco.app_android_fd.models.GoalDataModel
import com.gametaco.app_android_fd.models.LeaderboardDataModel
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.utilities.ResourceState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.Date

class LeaderboardViewModel : ViewModel() {
    private val authenticationManager: AuthenticationManager
        get() = AuthenticationManager.instance
    private val worldWinnerAPI: WorldWinnerAPI
        get() = WorldWinnerAPI.instance

    companion object {
        const val TAG = "LeaderboardViewModel"
        val instance: LeaderboardViewModel by lazy { LeaderboardViewModel() }
        const val MaxLeaderboardSpots = 11
    }
    init {
        Logger.d(TAG, "Init LeaderboardViewModel")
    }


    private val tournamentInstanceEntryId: MutableStateFlow<String?> = MutableStateFlow(null)

    private val isLoading = MutableStateFlow(true)

    private val _successData = MutableStateFlow<APITournamentInstanceEntry?>(null)
    private val _successIsAfterGame = MutableStateFlow<Boolean?>(null)
    private var onTournamentEntryLoadedAfterGameCalled : Boolean = false

    private val _connectedGoals = MutableStateFlow<List<String>>(listOf())
    val connectedGoals = _connectedGoals.asStateFlow()
    private val isAfterGame = MutableStateFlow(false)
    private val isLegacy = MutableStateFlow(false)

    private var _profileId: MutableStateFlow<String?> = MutableStateFlow(null)
    val profileId = _profileId.asStateFlow()

    private var _profileData:MutableStateFlow<APITournamentPlayerProfile?> = MutableStateFlow(null)
    val profileData = _profileData.asStateFlow()

    private val _showReferFriend = MutableStateFlow(false)
    val showReferFriend = _showReferFriend.asStateFlow()

    private var _replayURL: MutableStateFlow<String?> = MutableStateFlow(null)
    val replayURL = _replayURL.asStateFlow()

    val viewReplaySuccessString : String = "Success"
    val viewReplayFailureString : String = "Failure"
    val viewReplayNoAttemptString : String = "NoAttempt"

    var viewedReplayButtons : Boolean = false
    var viewedSelfReplay : String = viewReplayNoAttemptString
    var viewedCompetitorReplay : String = viewReplayNoAttemptString
    var isCompetitorReplayRequested : Boolean = false

    fun isShowingReplay():Boolean{
        return replayURL.value != null
    }
    fun showReplay(url : String?, isCompetitor:Boolean){
        isCompetitorReplayRequested = isCompetitor
        _replayURL.value = url
    }
    fun closeReplay(){
        _replayURL.value = null
        AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Modal_Closed.value,
            properties = mapOf("name" to "Replay"))
        )
    }

    fun replayAttempted(success : Boolean,errorMessage:String? = null)
    {
        val data = _successData.value

        if(success) {
            if(isCompetitorReplayRequested) {
                viewedCompetitorReplay = viewReplaySuccessString
            } else {
                viewedSelfReplay = viewReplaySuccessString
            }
            AnalyticsManager.instance.logEvent(
                AnalyticsEvent(
                    analyticsEvent = AnalyticsEventName.Replay_Modal_Viewed.value,
                    properties = mapOf(
                        "Game Name" to data?.game_display_name.toString(),
                        "Contest ID" to data?.tournament_instance_id.toString(),
                        "Game ID" to data?.game_id.toString(),
                        "Game Play ID" to tournamentInstanceEntryId.value.toString(),
                        "Game Mode" to data?.game_mode_name.toString(),
                        "Replay Type" to if(isCompetitorReplayRequested) "opponent" else "user",
                    )
                )
            )
        } else {
            if(isCompetitorReplayRequested) {
                viewedCompetitorReplay = viewReplayFailureString
            } else {
                viewedSelfReplay = viewReplayFailureString
            }
            AnalyticsManager.instance.logEvent(
                AnalyticsEvent(
                    analyticsEvent = AnalyticsEventName.Replay_Video_Load_Error.value,
                    properties = mapOf(
                        "Error" to errorMessage.toString(),
                        "Game Name" to data?.game_display_name.toString(),
                        "Contest ID" to data?.tournament_instance_id.toString(),
                        "Game ID" to data?.game_id.toString(),
                        "Game Play ID" to tournamentInstanceEntryId.value.toString(),
                        "Game Mode" to data?.game_mode_name.toString(),
                        "Replay Type" to if(isCompetitorReplayRequested) "opponent" else "user",
                    )
                )
            )
        }
    }

    fun showProfile(user_id:String,game_mode_id:String? = null){
        _profileId.value = user_id

        Logger.d("showProfile: user_id:" + user_id + " game_mode_id:" + game_mode_id)
        if(user_id.isNotEmpty()){
            //api request
            viewModelScope.launch (Dispatchers.IO) {
                val res = worldWinnerAPI.getTournamentPlayerProfile(user_id,game_mode_id)
                if(res.isSuccess()){
                    _profileData.value = (res as ResourceState.Success).data

                    Logger.d("data is " + _profileData.value)
                }else if(res.isError()){
                    Logger.e("Leaderboard", "Load Leaderboard Profile Data Error")
                    if(!ErrorManager.instance.handleResourceStateError(res)) {
                        AlertDialogManager.instance.showDialog(
                            "Error",
                            (res as ResourceState.Error).error,
                            "Confirm",
                            {} )
                    }
                }
            }

        }
    }

    fun isProfileShowing() : Boolean {
        return _profileId.value != null
    }

    fun closeProfile(){
        _profileId.value = null
        _profileData.value = null
    }



    fun closeLeaderboard(){
        Logger.d(TAG, "closeLeaderboard")
        if(isShowing())
        {
            //send Results_Closed analytic event, only if leaderboard is showing
            AnalyticsManager.instance.logEvent(
                AnalyticsEvent(
                    analyticsEvent = AnalyticsEventName.Results_Closed.value,
                    properties = mapOf(
                        "Viewed Replay Buttons" to viewedReplayButtons,
                        "Viewed Self Replay" to viewedSelfReplay,
                        "Viewed Competitor Replay" to viewedCompetitorReplay
                    )
                )
            )
            GoalsViewModel.instance.checkAndNavigateToDailyRewardIfAvailable()
        }
        tournamentInstanceEntryId.value = null
        isLoading.value = false
        onTournamentEntryLoadedAfterGameCalled = false
        _showReferFriend.value = false
    }

    fun isShowing() : Boolean {
        return tournamentInstanceEntryId.value != null
    }

    private var currentTournamentEntryFlowJob: Job? = null
    fun showLeaderboard(
        tournamentInstanceEntryId: String,
        isAfterGame: Boolean = true,
        isLegacy:Boolean = false
    ){
        Logger.d(TAG, "showLeaderboard")
        viewedReplayButtons = false
        viewedSelfReplay = viewReplayNoAttemptString
        viewedCompetitorReplay = viewReplayNoAttemptString

//        println("showLeaderboard: ${tournamentInstanceEntryId} ${isAfterGame}")
        this.tournamentInstanceEntryId.value = tournamentInstanceEntryId

        isLoading.value = true
        this.isAfterGame.value = isAfterGame
        this.isLegacy.value = isLegacy

        //deal with rewards for leaderboard
        GoalsViewModel.instance.fetchGoals()
        fetchConnectedGoals(tournamentInstanceEntryId)

        if(AuthenticationManager.instance.isGuest) {
            isLoading.value = false

            val tournamentAnalytics = TournamentManager.instance.getCurrentTournamentStats()
            if (tournamentAnalytics != null) {
                AnalyticsManager.instance.logEvent(
                    AnalyticsEvent(
                        analyticsEvent = AnalyticsEventName.Game_Guest_Ended.value,
                        properties = mapOf(
                            "Game Name" to tournamentAnalytics.gameName,
                            "Tournament Instance Entry ID" to tournamentAnalytics.tournamentInstanceEntryId,
                            "Game Mode" to tournamentAnalytics.gameMode,
                            "Tournament Instance Id" to tournamentAnalytics.gameId,
                            "Stake" to tournamentAnalytics.stake,
                            "Lobby Selection" to "Unknown"
                        )
                    )
                )
            }
            return
        }

        if(isLegacy){
            //////////////////test codes below////////////////
//            val wrappedRes:ResourceState<APITournamentInstanceEntry> = ResourceState.Success(
//                makeLegacyMockData()
//            )
//            onTournamentEntryLoaded(wrappedRes,isAfterGame)
            //////////////////test codes above////////////////

            currentTournamentEntryFlowJob?.cancel()
            currentTournamentEntryFlowJob = viewModelScope.launch {
                val res = worldWinnerAPI.getPlayedGameByID(tournamentInstanceEntryId)
                if(res.isSuccess()){
                    val data = (res as ResourceState.Success).data
                    val wrappedRes:ResourceState<APITournamentInstanceEntry> = ResourceState.Success(convertLegacyToAPITournamentInstanceEntry(data))
                    onTournamentEntryLoaded(wrappedRes,isAfterGame)

                }else if(res.isError()){
                    val error = (res as ResourceState.Error)
                    onTournamentEntryLoaded(ResourceState.Error(error.error,error.code),isAfterGame)
                }
            }
        }else{
            currentTournamentEntryFlowJob?.cancel()
            currentTournamentEntryFlowJob = viewModelScope.launch {
                worldWinnerAPI.getTournamentEntryFlow(tournamentInstanceEntryId).debounce(2000).collect { result ->
//                    println("getTournamentEntryFlow")
                    onTournamentEntryLoaded(result, isAfterGame)
                }
            }
        }

        if(isAfterGame){
            PreferencesManager.instance.accumulateGamePlayed()
            Logger.d("PreferencesManager.instance.getGamePlayed(): ${PreferencesManager.instance.getGamePlayed()}")
            if(PreferencesManager.instance.getGamePlayed() == 2){
                PermissionsManager.instance.requestNotificationPermission()
            }
        }
    }

    /**
     * Note: this function can be called multiple times for the same result. Actions such as
     * sending events and similar should be handled in [onTournamentEntryLoadedAfterGame] or similar.
     */
    private fun onTournamentEntryLoaded(
        result: ResourceState<APITournamentInstanceEntry>,
        isAfterGame: Boolean,
    ) {
        if (result.isSuccess()) {
            isLoading.value = false
            _successData.value = (result as ResourceState.Success).data
            _successIsAfterGame.value = isAfterGame

            TournamentManager.instance.setTournamentInstanceEntry(result.data)
        } else if (result.isError()) {
            isLoading.value = false
            Logger.e("Scores", "Load Leaderboard Data Error")
            if(!ErrorManager.instance.handleResourceStateError(result)) {
            }
        }
    }

    private fun onTournamentEntryLoadedAfterGame(entryData: APITournamentInstanceEntry?) {
        if (onTournamentEntryLoadedAfterGameCalled)
            return

        Logger.i("LeaderboardViewModel", "onTournamentEntryLoadedAfterGame")

        val entryData = _successData.value

        PreferencesManager.instance.setScoresScreenLastSeen(Date())

        if (entryData?.is_winner == true) {
            PreferencesManager.instance.accumulateWinTime()

            val winTimes = PreferencesManager.instance.getWinTimes()
            val lastSeenWinTimes = PreferencesManager.instance.getLastSeenWinTimes()
//            println("winTimes: "+winTimes + " lastSeenWinTimes:" + lastSeenWinTimes)
            if (winTimes > 0 && winTimes % 2 == 0 && lastSeenWinTimes != winTimes){
//                println("showReferFriend")
                PreferencesManager.instance.setLastSeenWinTime(winTimes)
                _showReferFriend.value = true
            }
        }

        val event = JSONObject()
        event.put("game_id",entryData?.game_id)
        event.put("tournament_id",entryData?.tournament_id)
        event.put("external_user_id",AuthenticationManager.instance.apiMe?.fanduel_id)
        event.put("player_game_id",entryData?.tournament_instance_id)
        event.put("user_id",AuthenticationManager.instance.apiMe?.id)
        event.put("prize_amount",entryData?.prize_amount)
        event.put("reference_id",entryData?.id)
        event.put("message_type",if(entryData?.is_winner == true || entryData?.is_tie == true) "WINNINGS" else "LOSS")//todo: wait for clarification
        event.put("step",1)//todo: wait for clarification
        BrazeManager.instance.logEvent(BrazeEventName.Game_Ended.value,event)

        AnalyticsManager.instance.logEvent(
            AnalyticsEvent(analyticsEvent = AnalyticsEventName.Game_Ended.value,
                properties = mapOf(
                    "Game Name" to entryData?.game_display_name.toString(),
                    "Game Mode" to entryData?.game_mode_name.toString(),
                    "Tournament Instance ID" to entryData?.tournament_instance_id.toString(),
                    "Tournament Instance Entry ID" to entryData?.tournament_id.toString(),
                    "Stake" to entryData?.entry_fee.toString())))

        AnalyticsManager.instance.logEvent(
            AnalyticsEvent(analyticsEvent = AnalyticsEventName.Game_Result_Leaderboard.value,
                properties = mapOf(
                    "Game Name" to entryData?.game_display_name.toString(),
                    "Contest ID" to tournamentInstanceEntryId,
                    "Game Mode" to entryData?.game_mode_name.toString(),
                    "Tournament Instance ID" to entryData?.tournament_instance_id.toString(),
                    "Tournament Instance Entry ID" to entryData?.tournament_id.toString(),
                    "Scoring Breakdown Visible" to entryData?.expanded_results.isNullOrEmpty().not().toString())))

        if (TournamentManager.instance.userGameStats?.total_games_played == 1) {
            AppsFlyerManager.instance.logEvent(AppsFlyerEventFactory.instance.firstGamePlayed)
        }

        if (entryData?.entry_fee?.toDouble()!! > 0.0 && TournamentManager.instance.userGameStats?.total_cash_games_played == 1) {
            AppsFlyerManager.instance.logEvent(AppsFlyerEventFactory.instance.firstPaidGame)
        }

        onTournamentEntryLoadedAfterGameCalled = true
    }

    fun fetchConnectedGoals(tournamentInstanceEntryId:String){
        if(AuthenticationManager.instance.isGuest)
            return
        viewModelScope.launch {
            val res = WorldWinnerAPI.instance.getGoalsStatus(tournamentInstanceEntryId)

            if (res.isSuccess()) {
                val data = (res as ResourceState.Success).data
                _connectedGoals.value = data.ids
//                println("data.ids:${data.ids}")
            } else if (res.isError()) {
                if(!ErrorManager.instance.handleResourceStateError(res)) {

                }
                Logger.e("ConnectedGoals", "Load Leaderboard connected goals Error")
            }
        }
    }

//    private val tied: StateFlow<Boolean> = _data.map { value ->
//        val me = value?.results?.find {
//            it.getUserName() == AuthenticationManager.instance.apiMe?.username
//        }
//
//        if(me != null){
//
//            val player = value.results.find {
//                it.getUserName() != me.getUserName() && it.score == me.score
//            }
//            player != null
//        } else {
//            false
//        }
//    }.stateIn(viewModelScope, started = SharingStarted.WhileSubscribed(), initialValue = false)

    private val placeholderOpponentCount: StateFlow<Int> =
        _successData.map { value ->
            if(value?.tournament_instance_status != APITournamentInstanceStatus.Closed.rawValue){
                val maxEntries = _successData.value?.maximum_slots ?:0
                val currentEntries = Math.max(_successData.value?.results?.size ?: 0,_successData.value?.current_slots_count ?: 0)
                maxEntries - currentEntries
            } else {
                0
            }
        }.stateIn(viewModelScope, started = SharingStarted.WhileSubscribed(), initialValue = 0)

    private fun playAgain(){
        val tournament_id = _successData.value?.tournament_id
        if(tournament_id != null){
            TournamentManager.instance.joinTournament(
                tournamentId = tournament_id,
                gameId = _successData.value?.game_id,
                entryFee = _successData.value?.entryFee ?: EntryFee(),
            )
        }
        closeLeaderboard()
    }

    fun remainingOpponentSpots(dataModel: LeaderboardDataModel): Int {
        val numResults = dataModel.data?.results?.count() ?: 0
        val opponentCount = dataModel.placeholderOpponentCount

        val spacesToAdd = MaxLeaderboardSpots - numResults
        if (spacesToAdd > 0) {
            val remainingSpots = minOf(spacesToAdd, opponentCount)
            return remainingSpots
        }

        return opponentCount
    }

    fun closeLeaderboardFtue(){
        val gameId = TournamentManager.instance.getGameId()
//                    println("onCloseFtue gameId: ${gameId}")
        if (!gameId.isNullOrEmpty()){
            PreferencesManager.instance.addGameToSeen(gameId)
        }
        authenticationManager.showLobbyFtue = false
        BrazeManager.instance.logEvent(BrazeEventName.FTUE_COMPLETE.value)
        AppsFlyerManager.instance.logEvent(AppsFlyerEventFactory.instance.ftueCompleted)
    }
    val leaderboardDataModel: StateFlow<LeaderboardDataModel> =
        combine(
            tournamentInstanceEntryId,
            isLoading,
            _successData,
            GoalsViewModel.instance.goals,
            _connectedGoals,
            placeholderOpponentCount,
            authenticationManager.showLobbyFtueFlow,
            isAfterGame
        ) { results ->
            val tournamentInstanceEntryId = results[0] as String?
            val isLoading = results[1] as Boolean
            val data = results[2] as APITournamentInstanceEntry?
            @Suppress("UNCHECKED_CAST") val goals = results[3] as ResourceState<GoalDataModel>
            @Suppress("UNCHECKED_CAST") val connectedGoals = results[4] as List<String>
            val placeholderOpponentCount = results[5] as Int
            val showFtue = results[6] as Boolean
            val isAfterGame = results[7] as Boolean

            LeaderboardDataModel(
                isLoading = isLoading,
                tournamentInstanceEntryId = tournamentInstanceEntryId,
                data = data,
                goals = goals,
                connectedGoals = connectedGoals,
                placeholderOpponentCount = placeholderOpponentCount,
                showFtue = showFtue,
                onCloseFtue = {
                    closeLeaderboardFtue()
                    closeLeaderboard()
                },
                onClose = { closeLeaderboard() },
                onPlayAgain = { playAgain() },
                isAfterGame = isAfterGame,
            )
        }.stateIn(viewModelScope, started = SharingStarted.WhileSubscribed(), initialValue = LeaderboardDataModel.Empty)

    init {
        combine(_successData, _successIsAfterGame) { data, isAfterGame ->
            if (data != null && isAfterGame == true) {
                onTournamentEntryLoadedAfterGame(data)
            }
            Unit
        }
            .onEach { /* No-op */ }
            .launchIn(viewModelScope)
    }

    private fun convertLegacyToAPITournamentInstanceEntry(data: APIPlayedGameIDResponse):APITournamentInstanceEntry{
        val myResult = data.results.find { it.username == AuthenticationManager.instance.apiMe?.username }
        val sameResult = data.results.find { it.username != AuthenticationManager.instance.apiMe?.username && ((it.prize_amount?:"0").toDouble()) > 0 && it.score == myResult?.score}

        return APITournamentInstanceEntry(
                id = data.id ?:"",
                tournament_instance_id = data.tournament_instance_id,
                started_at = data.tournament_instance_opened_at,//data.started_at,
                ended_at = data.tournament_instance_closed_at,//data.ended_at,
                entry_fee = data.entry_fee,
                maximum_slots = data.maximum_entries,
                is_winner = data.is_winner,
                is_tie = sameResult != null,
                tournament_id = data.tournament_id,
                tournament_instance_status = data.tournament_instance_status,
                tournament_instance_closed_at = data.tournament_instance_closed_at,
                score = data.score,
                current_slots_count = data.current_entries_count,
                game_icon = data.game_icon,
                game_id = data.game_id ?: "",
                results = data.results.map {
                    APITournamentInstanceEntryResult(
                        user = APITournamentUser(id = "1", username = it.username, avatar_url = it.avatar_url),
                        score = it.score,
                        order = null,
                        prize_amount = it.prize_amount,
                        status = APITournamentUserStatus.COMPLETED,
                        replay_url = null
                    )
                },
                is_joinable = false,
                can_reenter = false,
                prize_amount = data.prize_amount,
                game_display_name = data.game_display_name,
                game_mode_id = null,
                game_mode_name = data.game_mode_name,
                game_mode_description = data.game_mode_description,
                game_mode_icon = data.game_mode_icon,
                refund_reason = data.refund_reason,
                reference_id = data.reference_id,
                expanded_results = null,
                gradient_top_color = null,
                gradient_bottom_color = null,
                maximum_entries_per_player = 1,
                has_additional_ticket = false,
                is_for_ticket_holders_only = false,
            )
    }
}