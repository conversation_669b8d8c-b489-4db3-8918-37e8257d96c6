package com.gametaco.app_android_fd.manager
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.log.Logger
import kotlinx.coroutines.flow.MutableStateFlow

class LoadingManager {
    companion object {
        const val TAG = "LoadingManager"
        val instance: LoadingManager
            get() = resolve()
    }

    private val _loadingState = MutableStateFlow(false)
    fun showLoading() {
        Logger.d(TAG,"show loading...")
        _loadingState.value = true
    }

    fun hideLoading() {
        Logger.d(TAG,"hide loading...")
        _loadingState.value = false
    }

    fun isShowing() : <PERSON><PERSON>an {
        return _loadingState.value
    }
}