package com.gametaco.app_android_fd.manager

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import com.gametaco.app_android_fd.di.resolve

class LocationManager(
    private val context: Context,
    private val activityManager: ActivityManager,
    private val permissionsManager: PermissionsManager,
    private val alertDialogManager: AlertDialogManager,
) {

    companion object {
        val instance: LocationManager
            get() = resolve()
    }


    fun checkPermissions(showRequestDialog : <PERSON>olean, callback: (Boolean) -> Unit) {
        permissionsManager.checkLocationPermissions(showRequestDialog, callback)
    }

    fun onLocationPermissionFail(){
    }

}