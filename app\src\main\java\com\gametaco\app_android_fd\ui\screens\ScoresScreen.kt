package com.gametaco.app_android_fd.ui.screens

import PullRefreshIndicator
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowDown
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.gametaco.app_android_fd.LocalNavManager
import com.gametaco.app_android_fd.LocalUIManager
import com.gametaco.app_android_fd.data.AppConstants
import com.gametaco.app_android_fd.data.entity.APITournamentEntryResult
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceStatus
import com.gametaco.app_android_fd.data.entity.APITournamentStatus
import com.gametaco.app_android_fd.data.entity.TournamentStartInformation
import com.gametaco.app_android_fd.data.navigation.NavigationData
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.SystemBarState
import com.gametaco.app_android_fd.ui.components.ContentBox
import com.gametaco.app_android_fd.ui.components.ContentListBody
import com.gametaco.app_android_fd.ui.components.ContentTitle
import com.gametaco.app_android_fd.ui.components.NavBar
import com.gametaco.app_android_fd.ui.components.ScoresListElement
import com.gametaco.app_android_fd.ui.components.TopBar
import com.gametaco.app_android_fd.ui.modifiers.onDisappear
import com.gametaco.app_android_fd.ui.theme.AppandroidwwTheme
import com.gametaco.app_android_fd.ui.theme.theme_light_background
import com.gametaco.app_android_fd.utils.format
import com.gametaco.app_android_fd.utils.toDate
import com.gametaco.app_android_fd.utils.toDollarString
import com.gametaco.app_android_fd.utils.toDollarWithCommaString
import com.gametaco.app_android_fd.viewmodel.LoginViewModel
import com.gametaco.app_android_fd.viewmodel.ScoresViewModel
import com.gametaco.utilities.STR
import kotlinx.coroutines.launch
import pullRefresh
import rememberPullRefreshState
import resources.R
import java.util.Date

@Composable
fun ScoresScreen(
    scoresViewModel: ScoresViewModel
){
    val navManager = LocalNavManager.current
    val uiManager = LocalUIManager.current

    val refreshScope = rememberCoroutineScope()
    var refreshing by remember { mutableStateOf(false) }

    fun refresh() = refreshScope.launch {
        refreshing = true
        scoresViewModel.loadAll()
        refreshing = false
    }
    val state = rememberPullRefreshState(refreshing, ::refresh)

    LaunchedEffect(Unit) {
        uiManager.setSystemBarState(SystemBarState.TransparentDarkStatusBarIcons)
    }

    AppandroidwwTheme {
        Scaffold(
            topBar = { TopBar(title = STR(R.string.scores)) },
            bottomBar = { NavBar(NavigationData.instance, navManager) },
            containerColor = theme_light_background,
        ) { paddingValues ->
            Surface(
                modifier = Modifier
                    .fillMaxSize()
                    .pullRefresh(state = state)
                    .padding(paddingValues)
                    .onDisappear {
                        PreferencesManager.instance.setScoresScreenLastSeen(Date())
                        NavigationData.instance.setNotificationCountScores(0)
                    }
            ) {
                Box(modifier = Modifier.pullRefresh(state = state)) {
                    val isLoading by scoresViewModel.isLoading.collectAsState()
                    val isLoadingMore by scoresViewModel.isLoadingMore.collectAsState()
                    if (isLoading) {
                        LoadingScreen()
                    } else {
                        val entryList by scoresViewModel.entryList.collectAsState()
                        val listState = rememberLazyListState()
                        val noScores = entryList.results.isEmpty()

                        LaunchedEffect(listState) {
                            snapshotFlow { listState.layoutInfo.visibleItemsInfo }
                                .collect { visibleItems ->
                                    val lastVisibleItem = visibleItems.lastOrNull()
                                    if (lastVisibleItem != null && lastVisibleItem.key == "END") {
                                        scoresViewModel.loadMore()
                                    }
                                }
                        }

                        BoxWithConstraints(
                            modifier = Modifier
                                .background(if (noScores) MaterialTheme.colorScheme.background else Color.Transparent)
                                .fillMaxHeight()
                        ) {
                            var tabletPadding = PaddingValues()
                            val boxWithConstraintsScope = this
                            val maxWidth = boxWithConstraintsScope.maxWidth
                            if (maxWidth > AppConstants.VIEW_MAX_WIDTH.dp) {
                                tabletPadding = PaddingValues(
                                    horizontal = (maxWidth - AppConstants.VIEW_MAX_WIDTH.dp) / 2f
                                )
                            }

                            LazyColumn(
                                state = listState,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(MaterialTheme.colorScheme.background)
                                    .padding(tabletPadding)
                                    .padding(horizontal = 16.dp),
                                verticalArrangement = Arrangement.spacedBy(16.dp),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                if (noScores) {
                                    item {
                                        Spacer(modifier = Modifier.height(36.dp))
                                        Box(modifier = Modifier.fillMaxWidth()) {
                                            Image(
                                                modifier = Modifier
                                                    .width(264.dp)
                                                    .align(Alignment.Center),
                                                painter = painterResource(R.drawable.img_scores_trophy),
                                                contentDescription = null
                                            )
                                            Column(
                                                modifier = Modifier.fillMaxWidth(),
                                                horizontalAlignment = Alignment.CenterHorizontally
                                            ) {
                                                Spacer(modifier = Modifier.height(82.dp))
                                                Text(
                                                    text = STR(R.string.no_scores_caps),
                                                    textAlign = TextAlign.Center,
                                                    style = MaterialTheme.typography.bodyMedium.copy(
                                                        color = MaterialTheme.colorScheme.outline,
                                                        fontSize = 22.sp,
                                                        fontWeight = FontWeight(900),
                                                        letterSpacing = 1.sp
                                                    ),
                                                )
                                            }
                                        }
                                    }
                                } else {
                                    item{} // Adds 16.dp top padding using the column spacing

                                    //no started
                                    if (scoresViewModel.notStartedEntries.isNotEmpty()) {
                                        item {
                                            val list = mutableListOf<@Composable () -> Unit>()
                                            for (entry in scoresViewModel.notStartedEntries) {
                                                list.add {
                                                    BuildScoreElement(entry)
                                                }
                                            }

                                            ContentBox(
                                                titleComposable = {
                                                    ContentTitle(
                                                        title = STR(R.string.entered_not_started),
                                                        style = MaterialTheme.typography.titleMedium
                                                    )
                                                },
                                                bodyComposable = {
                                                    ContentListBody(
                                                        listElements = list,
                                                        elementSpacingHeight = 0.dp,
                                                        precedingSpacerHeight = 0.dp
                                                    )
                                                },
                                                bodyPaddingValues = PaddingValues(
                                                    start = 0.dp,
                                                    top = 0.dp,
                                                    end = 0.dp,
                                                    bottom = 0.dp
                                                )
                                            )
                                        }
                                    }

                                    //in Progress
                                    if (scoresViewModel.inProgressEntries.isNotEmpty()) {
                                        item {
                                            val list = mutableListOf<@Composable () -> Unit>()
                                            for (entry in scoresViewModel.inProgressEntries) {
                                                list.add {
                                                    BuildScoreElement(entry)
                                                }
                                            }
                                            ContentBox(
                                                titleComposable = {
                                                    ContentTitle(
                                                        title = STR(
                                                            R.string.in_progress,
                                                            list.size
                                                        ),
                                                        style = MaterialTheme.typography.titleMedium
                                                    )
                                                },
                                                bodyComposable = {
                                                    ContentListBody(
                                                        listElements = list,
                                                        elementSpacingHeight = 0.dp,
                                                        precedingSpacerHeight = 0.dp
                                                    )
                                                },
                                                bodyPaddingValues = PaddingValues(
                                                    start = 0.dp,
                                                    top = 0.dp,
                                                    end = 0.dp,
                                                    bottom = 0.dp
                                                )
                                            )
                                        }
                                    }

                                    if (scoresViewModel.closedEntries.isNotEmpty()) {
                                        item {
                                            val dic =
                                                mutableMapOf<String, MutableList<@Composable () -> Unit>>()

                                            for (entry in scoresViewModel.closedEntries) {
                                                val key = entry.displayDate().format()
                                                if (dic[key] == null) {
                                                    dic[key] = mutableListOf()
                                                }
                                                dic[key]?.add {
                                                    BuildScoreElement(entry)
                                                }
                                            }
                                            for (pair in dic) {
                                                ContentBox(
                                                    titleComposable = {
                                                        ContentTitle(
                                                            title = pair.key,
                                                            style = MaterialTheme.typography.titleMedium
                                                        )
                                                    },
                                                    bodyComposable = {
                                                        ContentListBody(
                                                            listElements = pair.value,
                                                            elementSpacingHeight = 0.dp,
                                                            precedingSpacerHeight = 0.dp
                                                        )
                                                    },
                                                    bodyPaddingValues = PaddingValues(
                                                        start = 0.dp,
                                                        top = 0.dp,
                                                        end = 0.dp,
                                                        bottom = 0.dp
                                                    )
                                                )
                                                Spacer(Modifier.height(16.dp))
                                            }
                                        }
                                    }

                                    if (isLoadingMore) {
                                        item {
                                            CircularProgressIndicator(
                                                modifier = Modifier
                                                    .size(30.dp),
                                                strokeWidth = 2.dp,
                                                color = MaterialTheme.colorScheme.primary
                                            )
                                        }
                                    } else {
                                        if (scoresViewModel.hasMoreEntries || scoresViewModel.hasMoreLegacyEntries) {
                                            item {
                                                Column(
                                                    horizontalAlignment = Alignment.CenterHorizontally,
                                                    modifier = Modifier.clickable {
                                                        scoresViewModel.loadMore()
                                                    }) {
                                                    Text(
                                                        text = STR(R.string.swipe_for_more),
                                                        color = MaterialTheme.colorScheme.primary,
                                                        style = MaterialTheme.typography.titleMedium,
                                                    )
                                                    Icon(
                                                        imageVector = Icons.Rounded.KeyboardArrowDown,
                                                        contentDescription = "",
                                                        tint = MaterialTheme.colorScheme.primary
                                                    )
                                                }
                                            }
                                        }
                                    }

                                    item(key = "END") {
                                        Spacer(modifier = Modifier.height(12.dp))
                                    }
                                }
                            }
                        }
                    }
                    PullRefreshIndicator(
                        modifier = Modifier.align(alignment = Alignment.TopCenter),
                        refreshing = refreshing,
                        state = state,
                    )
                }
            }

            if (AuthenticationManager.instance.isGuest) {
            BoxWithConstraints(
            ) {
                var tabletPadding = PaddingValues()
                val boxWithConstraintsScope = this
                val maxWidth = boxWithConstraintsScope.maxWidth
                if (maxWidth > AppConstants.VIEW_MAX_WIDTH.dp) {
                    tabletPadding = PaddingValues(
                        horizontal = (maxWidth - AppConstants.VIEW_MAX_WIDTH.dp) / 2f
                    )
                }

                Surface(
                    modifier = Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.background)
                        .padding(paddingValues)
                        .padding(tabletPadding)
                        .padding(24.dp)
                ) {
                        ContentBox(
                            //AuthenticationManager.instance.isGuest would break the preview so add LocalInspectionMode.current to fix
                            bodyBlockedIfGuest = true,
                            guestBlockTitle = STR(R.string.join_now_to_see_your_scores),
                            guestBlockLabel = STR(R.string.join_now_and_set_your_high_score),
                            guestBlockDesc = STR(R.string.join_to_compete_for_cash),
                            guestBlockClick = {
                                LoginViewModel.instance.fanduelSignupFromGuestMode()
                            },
                            titleComposable = {
                                Column {
                                    Text(
                                        text = STR(R.string.play_to_compete),
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                    Spacer(Modifier.height(6.dp))
                                    Text(
                                        text = STR(R.string.scores_guest_subtitle),
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                }
                            },
                            bodyComposable = {
                                Column(
                                    verticalArrangement = Arrangement.spacedBy(12.dp),
                                    modifier = Modifier.padding(12.dp)
                                )
                                {
                                    Image(
                                        painter = painterResource(id = R.drawable.img_guest_scores),
                                        contentDescription = null,
                                        contentScale = ContentScale.Fit,
                                        modifier = Modifier.fillMaxWidth()
                                    )
                                    //                                ContentListBody(listElements = fakeGoalList) //Guest Screen w/ Mock Data
                                }
                            },
                            bodyPaddingValues = PaddingValues(
                                bottom = 12.dp
                            )
                        )
                    }
                }
            }
        }
    }
}
@Composable 
fun BuildScoreElement(entry: APITournamentEntryResult){
//    println("entry: ${entry}")
    var statusMessage = ""
    var statusSubMessage:String? = null
    var statusIcon:Int? = null
    var isError = false
    var isResult = false
    var tournament_start_information: TournamentStartInformation? = null
    var value:String? = null

    if(entry.tournament_instance_status ==  APITournamentInstanceStatus.Open.rawValue && entry.tournament_start_information != null){
        statusMessage = STR(R.string.not_started_caps)
        statusIcon = R.drawable.ic_stopwatch2
        tournament_start_information = entry.tournament_start_information
    }else if(entry.tournament_instance_status ==  APITournamentInstanceStatus.Open.rawValue || entry.tournament_instance_status ==  APITournamentInstanceStatus.Closing.rawValue){
        statusMessage = STR(R.string.in_progress_caps)
        statusIcon = R.drawable.ic_stopwatch2
    }else if(entry.is_winner || entry.is_tie){
//        statusMessage = STR(if(entry.is_winner) R.string.you_won_caps else R.string.you_tied_caps)
        statusMessage = STR(R.string.you_won_caps)
        statusIcon = R.drawable.ic_trophy_app
        isResult = true
        value = entry.winnings?.prize_amount?.toDollarString()
    }else if(entry.tournament_instance_status ==  APITournamentInstanceStatus.Closed.rawValue && entry.tournament_start_information != null){
        statusMessage = STR(R.string.declined_text)
        statusIcon = R.drawable.ic_error_hexagon
        isError = true
    }else if(entry.tournament_instance_status ==  APITournamentInstanceStatus.Refunded.rawValue || entry.status == APITournamentStatus.refunded.rawValue){
        statusMessage = STR(R.string.expired_text)
        if(entry.entry_fee.toDouble() > 0){
            statusSubMessage = STR(R.string.refunded_caps)
        }
        statusIcon = R.drawable.ic_stopwatch1
        isError = true
    }

    val lastSeen = PreferencesManager.instance.getScoresScreenLastSeen()
    val hasBadge = entry.tournament_instance_closed_at?.toDate()?.after(lastSeen) == true
    val entryFee = entry.entry_fee.toDouble()
    val entryFeeString : String = if (entryFee > 0) {
        entryFee.toDollarWithCommaString()
    } else {
        STR(R.string.free_caps)
    }
     ScoresListElement(
        tournamentInstanceEntryId = entry.id,
        tournament_start_information = tournament_start_information,
        gameIcon = entry.game_icon ?: "",
        title = entry.tournament_brand ?: "",
        subtitle = entryFeeString,
        gameMode = entry.game_mode_name ?:"",
        entry_waived = entry.ticket_info != null,
        hasBadge = hasBadge,
        isError = isError,
        statusMessage = statusMessage,
        statusSubMessage = statusSubMessage,
        statusIcon = statusIcon,
        isResult = isResult,
        value = value,
        gameId = entry.game_id,
        isLegacy = entry.is_legacy ?: false,
        hasReplay = entry.has_replay ?: false
    )
}
@Preview
@Composable
fun ScoresScreenPreview(){
    PreviewRoot {
        ScoresScreen(scoresViewModel = ScoresViewModel.instance)
    }
}

//mock data:
//val enteredNotStartedElements: List<@Composable ()->Unit> = listOf ({
//    ScoresListElement(
//        gameIcon = "",
//        title = "Head to Head",
//        subtitle = "FREE Entry | Standard",
//        statusMessage = "NOT STARTED",
//        statusIcon = R.drawable.ic_stopwatch2
//    )
//})
//val inProgressElements: List<@Composable ()->Unit> = listOf (
//    { ScoresListElement(
//        gameIcon = "",
//        title = "Head to Head",
//        subtitle = "\$4.00 Entry | Standard",
//        statusMessage = "IN PROGRESS",
//        statusIcon = R.drawable.ic_stopwatch2
//    )},
//    { ScoresListElement(
//        gameIcon = "",
//        title = "Head to Head",
//        subtitle = "Friend Challenge",
//        statusMessage = "IN PROGRESS",
//        statusIcon = R.drawable.ic_stopwatch2
//    )}
//)
//val historyElements: List<@Composable ()->Unit> = listOf (
//    { ScoresListElement(
//        gameIcon = "",
//        title = "Head to Head",
//        subtitle = "\$1.00 Entry | 3-Hole Showdown",
//        isResult = true,
//        hasBadge = true,
//        value = "$2.00",
//        statusMessage = "YOU WON!",
//        statusIcon = R.drawable.ic_trophy_app
//    )},
//    { ScoresListElement(
//        gameIcon = "",
//        title = "Head to Head",
//        subtitle = "\$4.00 Entry | Daily Shot",
//        isResult = true,
//        hasBadge = true,
//        value = "$1.50",
//        statusMessage = "YOU TIED!",
//        statusIcon = R.drawable.ic_trophy_app
//    )},
//    { ScoresListElement(
//        gameIcon = "",
//        title = "Multiplayer",
//        subtitle = "\$4.00 Entry | Standard",
//    )},
//    { ScoresListElement(
//        gameIcon = "",
//        title = "Head to Head",
//        subtitle = "FREE Entry | Standard",
//        isResult = true,
//        statusMessage = "YOU WON!",
//        statusIcon = R.drawable.ic_trophy_app
//    )},
//    { ScoresListElement(
//        gameIcon = "",
//        title = "Head to Head",
//        subtitle = "FREE Entry | Daily Putt",
//    )},
//    { ScoresListElement(
//        gameIcon = "",
//        title = "Head to Head",
//        subtitle = "Friend Challenge",
//    )},
//    { ScoresListElement(
//        gameIcon = "",
//        title = "Head to Head",
//        subtitle = "Friend Challenge",
//        isResult = true,
//        statusMessage = "YOU WON!",
//        statusIcon = R.drawable.ic_trophy_app
//    )},
//    { ScoresListElement(
//        gameIcon = "",
//        title = "Head to Head",
//        subtitle = "Friend Challenge",
//        isError = true,
//        statusMessage = "Expired",
//        statusIcon = R.drawable.ic_stopwatch1
//    )},
//    { ScoresListElement(
//        gameIcon = "",
//        title = "Head to Head",
//        subtitle = "Friend Challenge",
//        isError = true,
//        statusMessage = "Declined",
//        statusIcon = R.drawable.ic_error_hexagon
//    )},
//)
