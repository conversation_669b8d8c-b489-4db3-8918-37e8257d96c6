package com.gametaco.app_android_fd.ui.screens

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import com.gametaco.app_android_fd.models.LoginDataModel
import com.gametaco.app_android_fd.ui.theme.AppandroidwwTheme
import com.gametaco.app_android_fd.viewmodel.LoginViewModel

@Composable
fun LoginScreen(loginViewModel: LoginViewModel) {
    val model = loginViewModel.loginDataModel.collectAsState().value
    LoginScreen(model)
}

@Composable
fun LoginScreen(model: LoginDataModel) {
    val loadingProgress = model.progress

    AppandroidwwTheme {
        if(loadingProgress >= 1.0f) {
            LoadingScreen()
        } else {
            SplashScreen(loadingProgress)
        }
    }
}