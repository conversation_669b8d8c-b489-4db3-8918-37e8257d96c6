
import androidx.compose.foundation.Image
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp


@Preview
@Composable
private fun VectorPreview() {
	Image(IcBack, null)
}

private var _IcBack: ImageVector? = null

public val IcBack: ImageVector
	get() {
		if (_IcBack != null) {
			return _IcBack!!
		}
		_IcBack = ImageVector.Builder(
			name = "IcBack",
			defaultWidth = 48.dp,
			defaultHeight = 48.dp,
			viewportWidth = 48f,
			viewportHeight = 48f
		).apply {
			path(
				fill = SolidColor(Color(0xFF005FC8)),
				fillAlpha = 1.0f,
				stroke = null,
				strokeAlpha = 1.0f,
				strokeLineWidth = 1.0f,
				strokeLineCap = StrokeCap.Butt,
				strokeLineJoin = StrokeJoin.Miter,
				strokeLineMiter = 1.0f,
				pathFillType = PathFillType.EvenOdd
			) {
				moveTo(36.548225f, 46.659f)
				curveTo(38.3372f, 44.8713f, 38.3372f, 41.9732f, 36.5482f, 40.1855f)
				lineTo(20.353012f, 24.001488f)
				lineTo(36.551247f, 7.814533f)
				curveTo(38.3402f, 6.0269f, 38.3402f, 3.1284f, 36.5512f, 1.3407f)
				curveTo(34.7623f, -0.4469f, 31.8619f, -0.4469f, 30.0729f, 1.3408f)
				lineTo(10.732032f, 20.668198f)
				curveTo(9.1061f, 22.293f, 8.958f, 24.8353f, 10.2876f, 26.6273f)
				curveTo(10.4927f, 27.0036f, 10.7546f, 27.3573f, 11.0732f, 27.6757f)
				lineTo(30.069916f, 46.659f)
				curveTo(31.8588f, 48.447f, 34.7593f, 48.447f, 36.5482f, 46.659f)
				close()
			}
		}.build()
		return _IcBack!!
	}

