package com.gametaco.app_android_fd.viewmodel

import com.gametaco.app_android_fd.utils.log.Logger
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


class WelcomeViewModel : ViewModel() {

    init {
        Logger.d(TAG, "Init WelcomeViewModel")

        if (PreferencesManager.instance.getAppFirstLaunch()) {
            AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.First_Launch.value))
            PreferencesManager.instance.setAppFirstLaunch(false)
        }
    }

    companion object {
        const val TAG = "WelcomeViewModel"
        val instance: WelcomeViewModel by lazy { WelcomeViewModel() }
    }

    fun loginPressed(navManager: NavManager) {
        navManager.navigateClearBackStack(Routes.LOGIN_SCREEN, Routes.WELCOME_SCREEN)
        LoginViewModel.instance.loginPressed()
    }

    fun createAccountPressed(navManager: NavManager) {
        navManager.navigateClearBackStack(Routes.LOGIN_SCREEN, Routes.WELCOME_SCREEN)
        FDManager.instance.signup()
    }

    fun guestPressed(navManager: NavManager) {
        viewModelScope.launch(Dispatchers.Main) {
            //goto login screen
            LoginViewModel.instance.setLoadingProgress(1f)
            navManager.navigateClearBackStack(Routes.LOGIN_SCREEN, Routes.WELCOME_SCREEN)
            AuthenticationManager.instance.guestLogin()
        }
    }



}