//package com.gametaco.app_android_fd.manager.payments
//
//import com.fanduel.android.awwebview.links.AWWebViewLinkHandler
//import com.fanduel.android.awwebview.types.SessionData
//import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
//import com.gametaco.app_android_fd.manager.AuthenticationManager
//import com.gametaco.app_android_fd.manager.FDManager
//import com.gametaco.app_android_fd.manager.GeoComplyManager
//import kotlinx.coroutines.CoroutineScope
//import kotlinx.coroutines.Dispatchers
//import kotlinx.coroutines.launch
//import java.util.Date
//
//class PaymentManager private constructor() {
//    companion object {
//        val instance: PaymentManager by lazy { PaymentManager() }
//        const val TAG = "PaymentManager"
//        val sdkService: WorldWinnerAPI = WorldWinnerAPI()
//        const val fanduelAccountUrlDev: String = "account.games.devstack-gtstaging-web.use1.dev.us.fdbox.net"
//        const val fanduelAccountUrlProd: String = "account.skillgames.fanduel.com"
//    }
//
//    private var sessionId: String = FDManager.instance.sessionDataSessionID!!
//    private val fanduelUrlBase: String = "https://www.fanduel.com"
//    private val trustlyUrlPath: String = "/wallet/deposit/trustly-callback"
//
//    //private var onBoardingViewModel: OnboardingViewModel? = null
//    private var onLogin: (suspend (String, String) -> Unit)? = null
//
//    fun handleLink(link: String): Boolean {
//        return if (link.contains(fanduelUrlBase) && link.contains(trustlyUrlPath)) {
//            AWWebViewLinkHandler.instance.handleLink(link)
//        } else false
//    }
//
//    fun bindLogin(action: suspend (String, String) -> Unit) {
//        onLogin = action
//    }
//
//    //fun bindOnBoardingViewModel(model: OnboardingViewModel) {
//    //    onBoardingViewModel = model
//    //}
//
//    fun setUserData(
//        userId: String,
//        username: String,
//        firstName: String?,
//        lastName: String?,
//        email: String?,
//        balance: Double?,
//        hasDeposited: Double?
//    ) {
//        println("[PaymentManager] setUserData::")
//    }
//
//    fun tmxProfilingResult(sessionId: String, successful: Boolean) {
//        println("[PaymentManager] tmxProfilingResult::")
//    }
//
//    fun idScanResult(result: Boolean) {
//        println("[PaymentManager] idScanResult::")
//    }
//
//    fun sessionChanged(id: String?, sessionId: String?, loginToken: String?, dateCreated: Date?) {
//        if (id == null || sessionId == null || loginToken == null) {
//            return
//        }
//
//        this.sessionId = sessionId
//
//        CoroutineScope(Dispatchers.IO).launch {
//            onLogin?.invoke(id, loginToken)
//            val userId = AuthenticationManager.instance?.apiMe?.id ?: return@launch
//            GeoComplyManager.instance.requestGeolocation()
//        }
//    }
//
//    fun logEvent(eventName: String, data: Any?, vendorTag: String?) {
//        println("[PaymentManager] logEvent::")
//    }
//
//    fun balanceMayHaveChanged() {
//        println("[PaymentManager] balanceMayHaveChanged::")
//    }
//
//    fun loginFlowCompleted() {
//        println("[PaymentManager] loginFlowCompleted::")
//    }
//
//    fun closeRequested(sessionData: SessionData) {
//        println("[PaymentManager] closeRequested::")
//    }
//}
