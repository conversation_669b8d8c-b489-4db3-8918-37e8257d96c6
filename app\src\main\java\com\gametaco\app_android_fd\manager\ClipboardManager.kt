package com.gametaco.app_android_fd.manager

import android.content.ClipData
import android.content.Context
import android.content.ClipboardManager as ClipboardManagerSystem

class ClipboardManager(
    val context: Context,
) {
    fun copyToClipboard(text: String) {
        val clip = ClipData.newPlainText("label", text)
        val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManagerSystem
        clipboard.setPrimaryClip(clip)
    }
}