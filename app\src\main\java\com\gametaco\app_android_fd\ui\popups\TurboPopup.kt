import android.util.DisplayMetrics
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.gametaco.app_android_fd.MainApplication.Companion.context
import com.gametaco.app_android_fd.ui.components.CommonButton
import com.gametaco.app_android_fd.ui.modifiers.clickableWithoutRipple
import com.gametaco.app_android_fd.ui.theme.AppFont
import resources.R

val displayMetrics: DisplayMetrics = context.resources.displayMetrics
val screenWidthPx = displayMetrics.widthPixels.toFloat()
val screenHeightPx = displayMetrics.heightPixels.toFloat()

@Composable
fun TurboPopup(onClose: () -> Unit, onPlayNow: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.radialGradient(
                    colors = listOf(Color(0xaa333333), Color(0xaa333333)),
                    center = Offset(-.2f, -.9f),
                    radius = 700.0f
                )
            )
            .clickableWithoutRipple {

            }
        ,
        contentAlignment = Alignment.Center
    ) {
        Column(
            modifier = Modifier
                .padding(24.dp, 0.dp)
//                .defaultMinSize(Dp.Unspecified, 560.dp)
                .background(
                    Brush.radialGradient(
                        colors = listOf(Color(0xff00ffc0), Color(0xff0070eb)),
                        center = Offset(-.2f, -.9f),
                        radius = 700.0f
                    ), shape = RoundedCornerShape(8.dp)
                )
            ,
            horizontalAlignment = Alignment.CenterHorizontally,
//            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(18.dp, 18.dp, 18.dp, 6.dp),
                horizontalArrangement = Arrangement.End
            ) {
                Text("Close", style = MaterialTheme.typography.titleMedium.copy(color = Color.White, fontWeight = FontWeight.Light),modifier = Modifier.clickable { onClose() },)

            }
            Text(
                text = "INTRODUCING",
                color = Color.White,
                fontFamily = AppFont.Balboa,
                fontSize = 50.sp,
                fontWeight = FontWeight.Bold,
            )
            Text(
                text = "TURBOS",
                color = Color(0xFFF8DD38),
                fontFamily = AppFont.Balboa,
                fontSize = 86.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.offset(0.dp,-5.dp)
            )

            Text(
                text = "EXPERIENCE THE EXCITEMENT",
                color = Color.White,
                fontFamily = AppFont.Knowkout,
                fontSize = 27.sp,
                modifier = Modifier.offset(0.dp,-5.dp)
            )
            Text(
                text = "OF SHORTER GAMES ON SELECT TITLES",
                color = Color.White,
                fontFamily = AppFont.Knowkout,
                fontSize = 21.sp,
            )

            Box {
                val scale = 2 / (screenHeightPx / screenWidthPx)

                Box{
                    Image(painter = painterResource(R.drawable.img_turbo_2), contentDescription = null,contentScale = ContentScale.FillWidth, modifier = Modifier
                        .size(380.dp, 394.dp)
                        .offset(0.dp, -30.dp), colorFilter = ColorFilter.tint(Color(0xff110fcf)))

                    Image(painter = painterResource(R.drawable.img_turbo_1), contentDescription = null,contentScale = ContentScale.FillWidth, modifier = Modifier
                        .size(301.dp * 0.80f * scale, 364.dp * 0.80f * scale)
                        .offset(0.dp, 50.dp))
                }

                Column(modifier = Modifier.padding(24.dp, 42.dp, 24.dp / scale, 6.dp).fillMaxWidth(), horizontalAlignment = Alignment.End) {
                    Text(
                        text = "\nPlay your favorite games in Turbo Mode to\ncomplete gameplay in 60 seconds or less.",
                        color = Color.White,
                        fontFamily = AppFont.Knowkout,
                        fontSize = 16.sp,
                    )
                    Text(
                        text = "\n\nJump in, play, and win cash\nfaster than ever before!",
                        color = Color.White,
                        fontFamily = AppFont.Knowkout,
                        fontSize = 16.sp,
                    )
                }

                Box(modifier = Modifier
                    .background(
                        Brush.linearGradient(
                            colors = listOf(Color(0x000070eb), Color(0xff0070eb)),
                            start = Offset(0f, 0f),
                            end = Offset(0f, Float.POSITIVE_INFINITY)
                        ), shape = RoundedCornerShape(8.dp)
                    )
                    .align(Alignment.BottomEnd)
                    .padding(20.dp, 32.dp)

                ){
                    CommonButton(title = "PLAY NOW", fillColor = Color(0xffF8DD38), textColor = Color(0xff001C55), titleStyle = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Bold)) {
                        onClose()
                        onPlayNow()
                    }
                }
            }
        }
    }
}

@Preview(widthDp = 360, heightDp = 800)
//@Preview()
@Composable
fun TurboPopupPreview(){
    TurboPopup({

    },{})
}
