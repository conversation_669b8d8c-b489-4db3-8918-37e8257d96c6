package com.gametaco.app_android_fd.ui.shapes

import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.LayoutDirection

class ParallelogramShape(private val skewFactor: Float  = 0.025f) : Shape {
    override fun createOutline(
        size: androidx.compose.ui.geometry.Size,
        layoutDirection: LayoutDirection,
        density: Density
    ): Outline {
        val path = Path().apply {
            moveTo(size.width * skewFactor, 0f)
            lineTo(size.width, 0f)
            lineTo(size.width * (1 - skewFactor), size.height)
            lineTo(0f, size.height)
            close()
        }
        return Outline.Generic(path)
    }
}