package com.gametaco.app_android_fd.manager

import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.models.DialogData

class AlertDialogManager {

    companion object {
        const val TAG = "AlertDialogManager"
        val instance: AlertDialogManager
            get() = resolve()
    }

    private val _dialogState = mutableStateOf<DialogData?>(null)
    val dialogState: State<DialogData?> = _dialogState

    fun showDialog(title: String, message: String, confirmButtonText: String, onConfirm: () -> Unit, cancelButtonText : String?=null, onCancel: (() -> Unit)? = null) {
        _dialogState.value = DialogData(title, message, confirmButtonText, onConfirm, cancelButtonText?:"", onCancel)
    }

    fun showDialogCustom(composeFunction: @Composable () -> Unit) {
        _dialogState.value = DialogData(composeFunction = composeFunction)
    }


    fun dismissDialog() {
        AnalyticsManager.instance.logEvent(
            AnalyticsEvent(analyticsEvent = AnalyticsEventName.Modal_Closed.value,
            properties = mapOf("name" to _dialogState.value?.title.toString()))
        )

        _dialogState.value = null
    }

    fun isShowing() : Boolean {
        return dialogState.value != null
    }
}





