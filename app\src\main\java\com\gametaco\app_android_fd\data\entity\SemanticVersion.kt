package com.gametaco.app_android_fd.data.entity

data class SemanticVersion(val major: Int, val minor: Int, val patch: Int) : Comparable<SemanticVersion> {
    companion object {
        private val allowedCharacters = "[.0-9]+".toRegex()

        // Factory method to parse version string and create a SemanticVersion instance
        fun fromString(versionString: String): SemanticVersion? {
            val cleanString = versionString.filter { it.toString().matches(allowedCharacters) }
            val components = cleanString.split('.').mapNotNull { it.toIntOrNull() }

            return if (components.size >= 3) {
                SemanticVersion(components[0], components[1], components[2])
            } else {
                null
            }
        }
    }

    override fun compareTo(other: SemanticVersion): Int {
        if (this.major != other.major) {
            return this.major.compareTo(other.major)
        }
        if (this.minor != other.minor) {
            return this.minor.compareTo(other.minor)
        }
        return this.patch.compareTo(other.patch)
    }
}