package com.gametaco.app_android_fd.viewmodel

import com.gametaco.app_android_fd.utils.log.Logger
import androidx.lifecycle.ViewModel
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.NavManager

class AccountViewModel : ViewModel() {

    init {
        Logger.d(TAG, "Init AccountViewModel")
    }

    fun loadWebview(url : String) {
        FDManager.instance.showWebviewFD(page = null, sourceURL = url)
    }

    companion object {
        const val TAG = "AccountViewModel"
        val instance: AccountViewModel by lazy { AccountViewModel() }
    }
}