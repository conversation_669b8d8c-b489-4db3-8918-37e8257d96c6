package com.worldwinner.fanduel.uitest

import com.worldwinner.fanduel.uitest.appium.AppiumManager
import io.appium.java_client.AppiumBy
import io.appium.java_client.android.AndroidDriver
import kotlinx.coroutines.delay
import org.openqa.selenium.By
import org.openqa.selenium.interactions.PointerInput
import org.openqa.selenium.support.ui.ExpectedConditions
import org.openqa.selenium.support.ui.WebDriverWait
import org.slf4j.LoggerFactory
import java.time.Duration
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

/**
 * The logic with each function is it is a "test" of its own. For all but the root screen tests,
 * there ideally will be a single precondition call at the start of the test to perform all
 * required UI navigation to navigate to the screen to be tested.
 */
class UiTestManagerImpl(
    private val appiumManager: AppiumManager,
) : UiTestManager {

    companion object {
        val validEmail = "<EMAIL>"
        val invalidEmail = "fdqa06@testcom"
        val validPassword = "Password123"
        val incorrectPassword = "abc"
        val expectedGameCount = 18
    }

    private val logger = LoggerFactory.getLogger(UiTestManagerImpl::class.java)
    fun log(message: String) {
        logger.info(message)
    }

    private suspend fun runUiTest(
        vararg preconditions: suspend () -> UiTestResult,
        testBody: suspend () -> UiTestResult
    ): UiTestResult {
        for (precondition in preconditions) {
            when (val result = precondition()) {
                UiTestResult.Pass -> continue
                else -> {
                    logger.warn("Precondition ${precondition::class.simpleName} failed with result: $result")
                    return result
                }
            }
        }
        return testBody()
    }

    private lateinit var driver: AndroidDriver
    override fun setAndroidDriver(driver: AndroidDriver) {
        this.driver = driver
    }

    private fun Fail(message: String): UiTestResult.Fail {
        log(message)
        return UiTestResult.Fail(message)
    }

    override suspend fun runToBootApp(): UiTestResult {
        // Perform actions to boot the app
        return if (true /* app is booted */) {
            UiTestResult.Pass
        } else {
            UiTestResult.Fail("App did not boot")
        }
    }
    // Helper function to check visibility of elements
    fun checkElementVisibility(xpath: String, waitDuration: Duration): Boolean {
        return try {
            val wait = WebDriverWait(driver, waitDuration)
            val textElement = wait.until(
                ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath))
            )
            textElement.isDisplayed
        } catch (e: Exception) {
            println("Failed to find the text element: ${e.message}")
            false
        }
    }

    // Helper function to check visibility of elements
    fun checkElementVisibilityWithTimeout(xpath: String, waitDuration: Duration): Boolean {
        return try {
            val wait = WebDriverWait(driver, waitDuration)
            val textElement = wait.until(
                ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath))
            )
            textElement.isDisplayed
        } catch (e: Exception) {
            println("Failed to find the text element: ${e.message}")
            false
        }
    }

    // Function to perform swipe action (left swipe in this case)
    fun swipeLeft() {
        val screenSize = driver.manage().window().size
        val startX = (screenSize.width * 0.8).toInt()  // Start at 80% of screen width
        val endX = (screenSize.width * 0.2).toInt()    // Swipe left to 20% of screen width
        val startY = (screenSize.height * 0.5).toInt() // Swipe at the middle of the screen

        // Define the finger input
        val finger = PointerInput(PointerInput.Kind.TOUCH, "finger")
        val swipe = org.openqa.selenium.interactions.Sequence(finger, 1)

        // Press at start position
        swipe.addAction(finger.createPointerMove(Duration.ZERO, PointerInput.Origin.viewport(), startX, startY))
        swipe.addAction(finger.createPointerDown(PointerInput.MouseButton.LEFT.asArg()))

        // Move to end position with a duration
        swipe.addAction(finger.createPointerMove(Duration.ofMillis(500), PointerInput.Origin.viewport(), endX, startY))
        swipe.addAction(finger.createPointerUp(PointerInput.MouseButton.LEFT.asArg()))

        // Perform the swipe action
        driver.perform(listOf(swipe))
    }

    override suspend fun runTextValidationOnLoginPage(): UiTestResult {
        return runUiTest(::runToGrantFirstRunLocationPermission) {
            val waitDuration = 30.seconds.toJavaDuration()

            // First group of elements (no swipe needed)
            val elementsGroup1 = listOf(
                "//android.widget.TextView[@text=\"Your favorite games,\nall in one app.\"]",
                "//android.widget.TextView[@text=\"Log in with your FanDuel account\"]",
                "//android.widget.TextView[@text=\"Create a FanDuel account\"]",
                "//android.widget.TextView[@text=\"New here? Try a game as a guest\"]",
                "//android.view.ViewGroup/android.view.View/android.view.View/android.widget.TextView"
            )

            // Check visibility for first group
            for (xpath in elementsGroup1) {
                val isVisible = checkElementVisibility(xpath, waitDuration)
                if (isVisible) {
                    println("$xpath is visible on the screen.")
                } else {
                    println("$xpath is NOT visible on the screen.")
                }
            }

            // Perform swipe after the first group of elements
            swipeLeft()

            // Second group of elements (requires swipe)
            val elementsGroup2 = listOf(
                "//android.widget.TextView[@text=\"Play for fun.\nCompete for cash.\"]",
                "//android.widget.TextView[@text=\"Play against other real people\nfor real cash.\"]"
            )

            // Check visibility for second group after swipe
            for (xpath in elementsGroup2) {
                val isVisible = checkElementVisibility(xpath, waitDuration)
                if (isVisible) {
                    println("$xpath is visible on the screen.")
                } else {
                    println("$xpath is NOT visible on the screen.")
                }
            }

            // Perform another swipe if needed
            swipeLeft()

            // Third group of elements (requires another swipe)
            val elementsGroup3 = listOf(
                "//android.widget.TextView[@text=\"Skill-based\nmatchmaking.\"]",
                "//android.widget.TextView[@text=\"Play head-to-head games with\nplayers of a similar skill level.\"]"
            )

            // Check visibility for third group after second swipe
            for (xpath in elementsGroup3) {
                val isVisible = checkElementVisibilityWithTimeout(xpath, waitDuration)
                if (isVisible) {
                    println("$xpath is visible on the screen.")
                } else {
                    println("$xpath is NOT visible on the screen.")
                }
            }

             UiTestResult.Pass
        }
    }

    override suspend fun runToGrantFirstRunLocationPermission(): UiTestResult {
        log("bootAppAndGrantLocationPermission")
        return runUiTest(::runToBootApp) {
            log("Finding permission dialog")
            driver.findElement(By.xpath("//android.widget.Button[@text='While using the app']"))
                .click()

            log("Permission granted, delaying for 2 seconds")
            delay(2.seconds)

            UiTestResult.Pass
        }
    }

    override suspend fun runToSignIn_success(): UiTestResult {
        return runToSignIn(email = validEmail, password = validPassword)
    }

    override suspend fun runToSignIn_failure_invalid_email(): UiTestResult {
        val result = runToSignIn(email = invalidEmail, password = validPassword)
        return if (result is UiTestResult.Fail) {
            UiTestResult.Pass
        } else {
            UiTestResult.Fail("Expected failure with invalid email")
        }
    }

    override suspend fun runToSignIn_failure_incorrect_password(): UiTestResult {
        val result = runToSignIn(email = validEmail, password = incorrectPassword)
        return if (result is UiTestResult.Fail) {
            UiTestResult.Pass
        } else {
            UiTestResult.Fail("Expected failure with incorrect password")
        }
    }
    override suspend fun runToSignIn_failure_invalid_email_incorrect_password(): UiTestResult {
        val result = runToSignIn(email = invalidEmail, password = incorrectPassword)

        return if (result is UiTestResult.Fail) {
            try {
                val wait = WebDriverWait(driver, 10.seconds.toJavaDuration()) // Wait for elements
                val loginScreenElement = wait.until(
                    ExpectedConditions.visibilityOfElementLocated(By.xpath("//android.widget.TextView[@text='Log in to FanDuel']"))
                )

                if (loginScreenElement.isDisplayed) {
                    UiTestResult.Pass
                } else {
                    UiTestResult.Fail("Expected 'Log in to FanDuel' screen to be visible, but it is not.")
                }
            } catch (e: Exception) {
                UiTestResult.Fail("Error while verifying 'Log in to FanDuel' screen: ${e.message}")
            }
        } else {
            UiTestResult.Fail("Expected failure with incorrect password, but test passed.")
        }
    }


    private suspend fun runToSignIn(email: String, password: String): UiTestResult {
        return runUiTest(::runToGrantFirstRunLocationPermission) {
            log("Waiting for Sign In screen to load")
            try {
                // Initial login button click
                val waitForLogin = WebDriverWait(driver, 5.seconds.toJavaDuration())
                val loginButton = waitForLogin.until(
                    ExpectedConditions.elementToBeClickable(
                        By.xpath("//android.view.ViewGroup/android.view.View/android.view.View/android.view.View[2]/android.widget.Button")
                    )
                )
                log("Clicking initial login button")
                loginButton.click()

                // Switch to WebView context for login form
                val waitForWebview = WebDriverWait(driver, 40.seconds.toJavaDuration())

                // Wait for WebView to be present
                log("Waiting for WebView to be present")
                waitForWebview.until(
                    ExpectedConditions.presenceOfElementLocated(
                        By.id("com.fanduel.skillgames:id/webview")
                    )
                )

                // Add a small delay to let WebView load
                delay(2.seconds)

                // Locate and fill email field
                log("Locating email field")
                val emailField = waitForWebview.until(
                    ExpectedConditions.visibilityOfElementLocated(
                        By.xpath("//android.widget.EditText[@resource-id=\"login-email\"]")
                    )
                )
                emailField.sendKeys(email)
                log("Entered email")

                // Locate and fill password field
                log("Locating password field")
                val passwordField = waitForWebview.until(
                    ExpectedConditions.visibilityOfElementLocated(
                        By.xpath("//android.widget.EditText[@resource-id=\"login-password\"]")
                    )
                )
                passwordField.sendKeys(password)
                log("Entered password")

                // Locate and click submit button
                log("Locating submit button")
                val submitButton = waitForWebview.until(
                    ExpectedConditions.visibilityOfElementLocated(
                        By.xpath("//android.widget.Button[@text=\"button-submit\"]")
                    )
                )
                submitButton.click()
                log("Clicked submit button")

                // Wait for login process to complete
                delay(20.seconds)

                log("Login process completed")

                // Now verify that the Turbo Popup appears (indicating a successful sign in)
                log("Verifying post-long sign in page")
                val waitForTurbo = WebDriverWait(driver, 30.seconds.toJavaDuration())

                log("Looking for \"Loading your favorite games\" text")
                val loadingLabel = waitForTurbo.until(
                    ExpectedConditions.visibilityOfElementLocated(
                        By.xpath("//android.widget.TextView[@text='Loading your favorite games…']")
                    )
                )
                if (!loadingLabel.isDisplayed) {
                    return@runUiTest Fail("Post-sign in text not visible")
                }

                log("WebView dismissed, login likely successful")

                UiTestResult.Pass
            } catch (e: Exception) {
                return@runUiTest Fail("Failed during login process: ${e.message}")
            }
        }
    }

    override suspend fun runToTurboPopup(): UiTestResult {
        return runUiTest(::runToSignIn_success) {
            log("Verifying Turbo Popup")
            val wait = WebDriverWait(driver, 40.seconds.toJavaDuration())

            try {
                // Verify Play Now button is visible
                log("Looking for Play Now button")
                val playNowButton = wait.until(
                    ExpectedConditions.visibilityOfElementLocated(
                        By.xpath("//android.widget.TextView[@text='PLAY NOW']")
                    )
                )

                if (!playNowButton.isDisplayed) {
                    return@runUiTest Fail("Play Now button not visible")
                }

                // Click Close button
                log("Looking for Close button")
                val closeButton = wait.until(
                    ExpectedConditions.visibilityOfElementLocated(
                        By.xpath("//android.widget.TextView[@text='Close']")
                    )
                )

                closeButton.click()
                log("Clicked Close button")

                UiTestResult.Pass
            } catch (e: Exception) {
                return@runUiTest Fail("Failed during Turbo Popup verification: ${e.message}")
            }
        }
    }

    override suspend fun runToLobbyTab(): UiTestResult {
        return runToRootTab("Play")
    }

    override suspend fun runToRewardsTab(): UiTestResult {
        return runToRootTab("Rewards")
    }

    override suspend fun checkSectionsOnRewardsPage(): UiTestResult {
        return runUiTest {
            println("checkSectionsOnRewardsPage() - start")
            runToRootTab("Rewards")
            println("checkSectionsOnRewardsPage() - after runToRootTab")
            try {
                val waitForCloseButton = WebDriverWait(driver, 40.seconds.toJavaDuration())

                println("Dismiss the tutorial overlay by clicking the scrim elements")
                // Dismiss the scrim elements
                val scrimElements = listOf(
                    "//android.view.ViewGroup/android.view.View",
                    "//android.view.ViewGroup/android.view.View/android.view.View",
                    "//android.view.ViewGroup/android.view.View/android.view.View/android.view.View[1]",
                    "//android.view.ViewGroup/android.view.View/android.view.View/android.view.View[1]/android.view.View",
                    "//android.view.ViewGroup/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.view.View",
                )
                for (name in scrimElements) {
                    try {
                        val element = waitForCloseButton.until(
                            ExpectedConditions.visibilityOfElementLocated(By.xpath(name))
                        )
                        element.click()
                    } catch (e: Exception) {
                        println("Scrim element '$name' not found: ${e.message}")
                    }
                }

                val elements = listOf(
                    Pair("Active rewards", "//android.widget.TextView[@text=\"Active rewards\"]"),
                    Pair("Rewards Icon", "//android.view.View[@content-desc=\"Reward Icon Base\"]"),
                    Pair("1st Deposit", "//android.widget.TextView[@text=\"1st Deposit\"]"),
                    Pair(
                        "Make Your First Deposit",
                        "//android.widget.TextView[@text=\"Make your first deposit and get a 100% match up to $10.00 instantly.\"]"
                    ),
                    Pair("Deposit Now", "//android.widget.TextView[@text=\"Deposit Now\"]")
                )

                println("checkSectionsOnRewardsPage() - checking elements visibility")

                for ((name, xpath) in elements) {
                    println("Checking visibility for element: $name")
                    val element = waitForCloseButton.until(
                        ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath))
                    )
                    if (element?.isDisplayed == true) {
                        println("The element '$name' is visible on the screen.")
                    } else {
                        println("The element '$name' is NOT visible.")
                        return@runUiTest Fail("Mandatory element '$name' is not visible on the screen.")
                    }
                }

                println("checkSectionsOnRewardsPage() - checking scrollable elements")

                // Perform scrolling once
                for (i in 1..2) { // Scroll twice
                    driver.findElement(
                        AppiumBy.androidUIAutomator(
                            "new UiScrollable(new UiSelector().scrollable(true)).scrollToEnd(1)"
                        )
                    )
                }
                val scrollElements = listOf(
                    Pair(
                        "3 Favorites Bonus",
                        "//android.widget.TextView[@text=\"3 Favorites Bonus\"]"
                    ),
                    Pair(
                        "Lifetime rewards",
                        "//android.widget.TextView[@text=\"Lifetime rewards claimed\"]"
                    ),
                    Pair(
                        "Recently claimed rewards",
                        "//android.widget.TextView[@text=\"Recently claimed rewards\"]"
                    )
                )

                val waitForFindElement = WebDriverWait(driver, 40.seconds.toJavaDuration())

                println("checkSectionsOnRewardsPage() - checking scrollable elements visibility")

                for ((name, xpath) in scrollElements) {
                    val element = waitForFindElement.until(
                        ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath))
                    )
                    if (element?.isDisplayed == true) {
                        println("The element '$name' is visible on the screen.")
                    } else {
                        println("The element '$name' is NOT visible.")
                        return@runUiTest Fail("Mandatory element '$name' is not visible on the screen.")
                    }
                }

                println("checkSectionsOnRewardsPage() - all elements checked successfully")

                UiTestResult.Pass
            } catch (e: Exception) {
                println("Error occurred: ${e.message}")
                return@runUiTest Fail("Error occurred while checking elements: ${e.message}")
            }
        }
    }


    override suspend fun runToScoresTab(): UiTestResult {
        return runToRootTab("Scores")
    }

    override suspend fun runToAccountTab(): UiTestResult {
        return runToRootTab("Account")
    }

    override suspend fun checkAccountPageSections(): UiTestResult {
        return runUiTest {
            runToRootTab("Account")
            try {
                val wait = WebDriverWait(driver, 40.seconds.toJavaDuration())
                val elementsBeforeScroll = listOf(
                    "//android.widget.TextView[@text=\"Playable balance\"]",
                    "//android.widget.ScrollView/android.widget.Button[1]",
                    "//android.widget.TextView[@text=\"DEPOSIT\"]",
                    "//android.widget.ScrollView/android.widget.Button[2]",
                    "//android.widget.TextView[@text=\"WITHDRAW\"]",
                    "//android.widget.TextView[@text=\"Transaction History\"]",
                    "//android.widget.TextView[@text=\"Tax Center\"]",
                    "//android.widget.TextView[@text=\"Activity Statement\"]",
                    "//android.widget.TextView[@text=\"Earn Cash: Refer Friends\"]",
                    "//android.widget.TextView[@text=\"Account Settings\"]",
                    "//android.widget.TextView[@text=\"Notification Settings\"]",
                    "//android.widget.TextView[@text=\"Haptics\"]",
                    "//android.widget.TextView[@text=\"Support\"]",
                    "//android.widget.TextView[@text=\"My Spend\"]",
                    "//android.widget.TextView[@text=\"Responsible Play\"]"
                )
                for (xpath in elementsBeforeScroll) {
                    try {
                        val element = wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath)))
                        if (element.isDisplayed) {
                            println("Element found: $xpath")
                        } else {
                            println("Element NOT visible: $xpath")
                            return@runUiTest Fail("Error occurred while checking elements: Element not visible - $xpath")
                        }
                    } catch (e: Exception) {
                        println("Element not found: $xpath")
                        return@runUiTest Fail("Error occurred while checking elements: ${e.message}")
                    }
                }

                driver.findElement(
                    AppiumBy.androidUIAutomator("new UiScrollable(new UiSelector().scrollable(true)).scrollToEnd(2)")
                )

                val elementsAfterScroll = listOf(
                    "//android.widget.TextView[@text=\"Training Guides\"]",
                    "//android.widget.TextView[@text=\"Rules and Scoring\"]",
                    "//android.widget.TextView[@text=\"About Faceoff\"]",
                    "//android.widget.TextView[@text=\"Terms of Use\"]",
                    "//android.widget.TextView[@text=\"Privacy\"]",
                    "//android.widget.TextView[@text=\"Log Out\"]"
                )
                for (xpath in elementsAfterScroll) {
                    try {
                        val element = wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath)))
                        if (element.isDisplayed) {
                            println("Element found: $xpath")
                        } else {
                            println("Element NOT visible: $xpath")
                            return@runUiTest Fail("Mandatory element '$xpath' is not visible on the screen.")
                        }
                    } catch (e: Exception) {
                        return@runUiTest Fail("Mandatory element '$xpath' is not visible on the screen.")
                    }
                }
            } catch (e: Exception) {
                println("Error occurred while checking account page sections: ${e.message}")
                return@runUiTest Fail("Error occurred while checking account page sections: ${e.message}")
            }

            UiTestResult.Pass// Ensuring a success return if everything works fine
        }
    }


    override suspend fun runToLobby_verifyRecentlyPlayed(): UiTestResult {
        return runUiTest(::runToLobbyTab) {
            log("Verifying Recently Played section")
            val wait = WebDriverWait(driver, 5.seconds.toJavaDuration())

            val recentlyPlayed = wait.until(
                ExpectedConditions.visibilityOfElementLocated(
                    By.xpath("//android.widget.TextView[@text=\"Recently Played\"]")
                )
            )

            UiTestResult.Pass
        }
    }

    override suspend fun runToLobby_verifyPopularGames(): UiTestResult {
        return runUiTest(::runToLobbyTab) {
            log("Verifying Popular Games & Tournaments section")
            val wait = WebDriverWait(driver, 5.seconds.toJavaDuration())

            val popularGames = wait.until(
                ExpectedConditions.visibilityOfElementLocated(
                    By.xpath("//android.widget.TextView[@text=\"Popular Games & Tournaments\"]")
                )
            )

            UiTestResult.Pass
        }
    }

    override suspend fun runToLobby_verifyAllGames(): UiTestResult {
        return runUiTest(::runToLobbyTab) {
            log("Verifying All Games section")
            val wait = WebDriverWait(driver, 20.seconds.toJavaDuration())

            // Scroll to All Games section
            driver.findElement(
                AppiumBy.androidUIAutomator(
                    "new UiScrollable(new UiSelector().scrollable(true))" +
                            ".scrollIntoView(new UiSelector().textStartsWith(\"All Games\"));"
                )
            )

            // Verify All Games is visible
            val allGames = wait.until(
                ExpectedConditions.visibilityOfElementLocated(
                    AppiumBy.xpath("//android.widget.TextView[starts-with(@text, 'All Games')]")
                )
            )

            if (!allGames.isDisplayed) {
                return@runUiTest Fail("All Games section not visible after scrolling")
            }

            UiTestResult.Pass
        }
    }

    override suspend fun runToLobby_verifyGameCount(): UiTestResult {
        return runUiTest(::runToLobbyTab) {
            log("Verifying game count")
            val wait = WebDriverWait(driver, 40.seconds.toJavaDuration())

            // Scroll downward multiple times to reveal items
            try {
                repeat(3) {
                    driver.findElement(
                        AppiumBy.androidUIAutomator(
                            "new UiScrollable(new UiSelector().scrollable(true)).scrollForward()"
                        )
                    )
                    // Small delay between scrolls to let items load
                    delay(1.seconds)
                }
            } catch (e: Exception) {
                log("Failed during scrolling: ${e.message}")
                // Don't fail here - we might still have scrolled enough
            }

            // Find all game buttons at once
            try {
                val gameButtons = wait.until(
                    ExpectedConditions.presenceOfAllElementsLocatedBy(
                        By.xpath("//android.view.ViewGroup/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.Button")
                    )
                )
                val visibleGames = gameButtons.count { it.isDisplayed }
                log("Found $visibleGames visible games")

                if (visibleGames < expectedGameCount) {
                    return@runUiTest Fail("Expected $expectedGameCount games but found $visibleGames")
                }
                UiTestResult.Pass
            } catch (e: Exception) {
                return@runUiTest Fail("Failed to find game buttons: ${e.message}")
            }
        }
    }

    override suspend fun runToCloseDialog_whenVisible(): UiTestResult {
        return runUiTest {
            try {
                log("Checking for Close button")
                val wait = WebDriverWait(driver, 40.seconds.toJavaDuration())
                val closeButton = wait.until(
                    ExpectedConditions.visibilityOfElementLocated(
                        By.xpath("//android.widget.TextView[@text='Close']")
                    )
                )

                if (closeButton.isDisplayed) {
                    closeButton.click()
                    log("Close button clicked")
                }

                UiTestResult.Pass
            } catch (e: Exception) {
                log("Close button not found or not clickable: ${e.message}")
                UiTestResult.Pass // Not finding the close button is OK
            }
        }
    }

    override suspend fun runToAccount_logout_confirmationDialog_canBeCancelled(): UiTestResult {
        return runUiTest(::runToAccount_logout_confirmationDialog_displays) {
            log("Looking for logout option...");
            try {
                log("Waiting for 'No' button to appear...")
                val wait = WebDriverWait(driver, 10.seconds.toJavaDuration())
                val noButton = wait.until(
                    ExpectedConditions.visibilityOfElementLocated(
                        By.xpath("//android.view.ViewGroup/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.widget.Button")
                    )
                )

                if (noButton.isDisplayed) {
                    noButton.click()
                    log("No Button is clicked")
                } else {
                    log("No Button was not displayed")
                    return@runUiTest Fail("No Button is not displayed")
                }
                UiTestResult.Pass
            } catch (e: Exception) {
                return@runUiTest Fail("Failed during logout process: ${e.message}")
            } // If everything works, return success
        }
    }
    override suspend fun runToAccount_logout_confirmationDialog_displays(): UiTestResult {
        return runUiTest(::runToAccountTab) {
            log("Looking for logout option")
            val wait = WebDriverWait(driver, 10.seconds.toJavaDuration())

            try {
                // Scroll to Log Out button
                wait.until(
                    ExpectedConditions.presenceOfElementLocated(
                        AppiumBy.androidUIAutomator(
                            "new UiScrollable(new UiSelector().scrollable(true).className(\"android.widget.ScrollView\"))" +
                                    ".scrollIntoView(new UiSelector().text(\"Log Out\"))"
                        )
                    )
                )
                val logoutButton =
                    driver.findElement(By.xpath("//android.widget.TextView[@text=\"Log Out\"]"))
                logoutButton.click()
                log("Clicked logout button")

// Wait for the confirmation dialog to appear


                val areYouSurePrompt = wait.until(
                    ExpectedConditions.visibilityOfElementLocated(
                        By.xpath("//android.widget.TextView[@text=\"Are you sure you want to logout?\"]")
                    )
                )

                if (areYouSurePrompt.isDisplayed) {
                    log("Logout confirmation dialog displayed")

                    // Locate "Yes" and "No" buttons
                    val yesButton = wait.until(
                        ExpectedConditions.visibilityOfElementLocated(
                            By.xpath("//android.view.ViewGroup/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.widget.Button")
                        )
                    )

                    val noButton = wait.until(
                        ExpectedConditions.visibilityOfElementLocated(
                            By.xpath("//android.view.ViewGroup/android.view.View/android.view.View/android.view.View/android.view.View[2]/android.widget.Button")
                        )
                    )

                    if (yesButton.isDisplayed && noButton.isDisplayed) {
                        log("Both 'Yes' and 'No' buttons are displayed")
                    } else {
                        log("One or both buttons are not displayed")
                    }
                } else {
                    log("Logout confirmation dialog NOT displayed")
                }

                UiTestResult.Pass
            } catch (e: Exception) {
                return@runUiTest Fail("Failed during logout process: ${e.message}")
            }
        }
    }


        override suspend fun runToAccount_logout_verifyLoggedOut(): UiTestResult {
        return runUiTest({ runToAccount_logoutOption(true) }) {
            try {
                log("Verifying logged out state")
                val wait = WebDriverWait(driver, 30.seconds.toJavaDuration())

                val welcomeText = wait.until(
                    ExpectedConditions.visibilityOfElementLocated(
                        By.xpath("//android.widget.TextView[@text=\"Your favorite games,\nall in one app.\"]")
                    )
                )

                if (!welcomeText.isDisplayed) {
                    return@runUiTest Fail("Welcome text not visible after logout")
                }

                UiTestResult.Pass
            } catch (e: Exception) {
                return@runUiTest Fail("Failed to verify logged out state: ${e.message}")
            }
        }
    }


    private suspend fun runToRootTab(tabName: String, verifyTabOpens: Boolean = true): UiTestResult {
        return runUiTest(::runToTurboPopup) {
            log("Navigating to $tabName tab")
            val wait = WebDriverWait(driver, 30.seconds.toJavaDuration())
            
            // Map tab names to their XPath indices
            val tabIndex = when (tabName) {
                "Play" -> "1"
                "Rewards" -> "2"
                "Scores" -> "3"
                "Account" -> "4"
                else -> throw IllegalArgumentException("Unknown tab: $tabName")
            }
            
            val tabXPath = "//android.view.ViewGroup/android.view.View/android.view.View/android.view.View/android.view.View[2]/android.view.View/android.view.View[$tabIndex]/android.view.View[2]"
            
            try {
                val tabElement = wait.until(
                    ExpectedConditions.visibilityOfElementLocated(By.xpath(tabXPath))
                )
                tabElement.click()
                log("Clicked on $tabName tab")
                
                if (verifyTabOpens) {
                    // Add verification logic here if needed
                }
                
                UiTestResult.Pass
            } catch (e: Exception) {
                return@runUiTest Fail("Failed to navigate to $tabName tab: ${e.message}")
            }
        }
    }

    private suspend fun runToAccount_logoutOption(clickLogout: Boolean): UiTestResult {
        return runUiTest(::runToAccountTab) {
            log("Looking for logout option")
            val wait = WebDriverWait(driver, 10.seconds.toJavaDuration())
            
            try {
                // Scroll to Log Out button
                wait.until(ExpectedConditions.presenceOfElementLocated(
                    AppiumBy.androidUIAutomator(
                        "new UiScrollable(new UiSelector().scrollable(true).className(\"android.widget.ScrollView\"))" +
                        ".scrollIntoView(new UiSelector().text(\"Log Out\"))"
                    )
                ))
                
                if (clickLogout) {
                    val logoutButton = driver.findElement(By.xpath("//android.widget.TextView[@text=\"Log Out\"]"))
                    logoutButton.click()
                    log("Clicked logout button")
                    
                    // Click Yes on confirmation
                    val yesButton = driver.findElement(By.xpath("//android.widget.TextView[@text=\"Yes\"]"))
                    yesButton.click()
                    log("Confirmed logout")
                }
                
                UiTestResult.Pass
            } catch (e: Exception) {
                return@runUiTest Fail("Failed during logout process: ${e.message}")
            }
        }
    }

}
