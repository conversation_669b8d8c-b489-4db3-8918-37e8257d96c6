package com.gametaco.app_android_fd.ui.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.gametaco.app_android_fd.ui.components.Background
import com.gametaco.app_android_fd.ui.components.ProgressIndicator
import com.gametaco.app_android_fd.ui.theme.BlueWelcomeBackground
import resources.R

@Composable
fun SplashScreen(
    loadingProgress: Float,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxSize()
    ) {
        Background(BlueWelcomeBackground)

        Image(
            painter = painterResource(R.drawable.ic_fanduel_full),
            contentDescription = null, // You can provide a description if needed
            modifier = Modifier
                .size(210.dp, 180.dp)
//                .offset(y = 20.dp)
                .align(Alignment.Center),
        )

        Column(
            modifier = Modifier
                .align(Alignment.Center)
                .padding(top = 260.dp),
        ) {
            Text(
                text = stringResource(id = R.string.loading_your_favorite_games),
                style = MaterialTheme.typography.titleMedium,
                color = Color.White,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally),
            )

            ProgressIndicator(
                progress = loadingProgress,
                color = Color.White,
                trackColor = Color.White.copy(alpha = 0.5f),
                modifier = Modifier
                    .width(200.dp)
                    .padding(top = 16.dp)
                    .align(Alignment.CenterHorizontally),
            )
        }
    }
}



@Preview
@Composable
fun SplashScreenPreview() {
    SplashScreen(0.5f)
}
