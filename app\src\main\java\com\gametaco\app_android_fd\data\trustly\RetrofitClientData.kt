package com.gametaco.app_android_fd.data.trustly

import com.gametaco.app_android_fd.data.AppConstants.Trustly.FULL_URL
import okhttp3.OkHttpClient
import okhttp3.Request
import retrofit2.Retrofit
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory

object RetrofitClientData {
    private var retrofit: Retrofit? = null

    fun getClient(): TrustlyApi {
        val client = OkHttpClient.Builder()
            .addInterceptor {
                val request: Request = it.request()
                    .newBuilder()
                    .addHeader("accept", "application/json")
                    .addHeader("Content-Type", "application/json")
                    .build()
                it.proceed(request)
            }
            .build()

        if (retrofit == null) {
            retrofit = Retrofit.Builder()
                .baseUrl(FULL_URL)
                .addConverterFactory(GsonConverterFactory.create())
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                .client(client)
                .build()
        }
        return retrofit!!.create(TrustlyApi::class.java)
    }
}
