package com.gametaco.app_android_fd.ui.theme

import android.os.Build
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import com.gametaco.app_android_fd.LocalUIManager

private val DarkColorScheme = darkColorScheme(
    primary = Purple80,
    secondary = PurpleGrey80,
    tertiary = Pink80,
)

private val LightColorScheme = lightColorScheme(
    primary = theme_light_primary,
    inversePrimary = theme_light_inverse_primary,
    secondary = theme_light_secondary,
    secondaryContainer = theme_light_secondary_container,
    tertiary = theme_light_tertiary,
    tertiaryContainer = theme_light_tertiary_container,
    background = theme_light_background,
    surface = theme_light_surface,
    surfaceTint = theme_light_surface,
    outline = theme_light_outline,
    outlineVariant = theme_light_outline_variant,
    onPrimary = theme_light_on_primary,
    onSurface = theme_light_on_surface,
    inverseOnSurface = theme_light_inverse_on_surface
)

/* Other default colors to override
    background = Color(0xFFFFFBFE),
    surface = Color(0xFFFFFBFE),
    onPrimary = Color.White,
    onSecondary = Color.White,
    onTertiary = Color.White,
    onBackground = Color(0xFF1C1B1F),
    onSurface = Color(0xFF1C1B1F),
*/

@Composable
fun AppandroidwwTheme(
    darkTheme: Boolean = false, //isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    val systemUiController: SystemUiController = rememberSystemUiController()

    val uiManager = LocalUIManager.current
    val useDarkStatusBarIcons by uiManager.useDarkStatusBarIcons.collectAsState()
    val navigationBarColor by uiManager.navigationBarColor.collectAsState()

    val view = LocalView.current
    if (!view.isInEditMode) {
        DisposableEffect(systemUiController, useDarkStatusBarIcons, navigationBarColor) {
            val systemBarColor = Color.Transparent
            systemUiController.setStatusBarColor(
                systemBarColor,
                darkIcons = useDarkStatusBarIcons
            )
            systemUiController.setNavigationBarColor(navigationBarColor)
            onDispose { }
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}