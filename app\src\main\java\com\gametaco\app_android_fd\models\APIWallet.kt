import androidx.compose.runtime.Immutable
import java.math.BigDecimal

// APIWallet.kt
// Copyright © 2024 Game Taco, Inc.

// APIWallet

@Immutable
data class APIWallet(
    val realCashBalanceStr: String,
    val bonusCashBalanceStr: String,
    val rewardsPointsBalance: Int
) {
    // Computed properties translated to custom getters in Kotlin
    val cashBalance: BigDecimal
        get() = realCashBalance.add(bonusCashBalance)

    val realCashBalance: BigDecimal
        get() = realCashBalanceStr.toBigDecimalOrNull() ?: BigDecimal.ZERO

    val bonusCashBalance: BigDecimal
        get() = bonusCashBalanceStr.toBigDecimalOrNull() ?: BigDecimal.ZERO

    val formattedRewardPoints: String
        get() = BigDecimal(rewardsPointsBalance).stripTrailingZeros().toPlainString()

    // Additional analytics or utility functions can be defined here
    val walletBalance: Float
        get() = (realCashBalance.toFloat() + bonusCashBalance.toFloat())
}

// Extension function to convert string to BigDecimal safely
fun String.toBigDecimalOrNull(): BigDecimal? = try {
    BigDecimal(this)
} catch (e: NumberFormatException) {
    null
}
