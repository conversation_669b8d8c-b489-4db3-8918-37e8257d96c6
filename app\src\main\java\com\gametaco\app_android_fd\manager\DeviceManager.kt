package com.gametaco.app_android_fd.manager

import android.webkit.WebView
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.log.Logger
import java.util.UUID

class DeviceManager(
    private val preferencesManager: PreferencesManager,
) {
    companion object {
        val instance: DeviceManager
            get() = resolve()
        const val TAG = "DeviceManager"
    }

    private var deviceId : String? = null

    fun getDeviceId(): String? {

        if(deviceId != null) {
            return deviceId
        } else {

            deviceId = preferencesManager.getDeviceID()

            if(deviceId == null){
                deviceId = UUID.randomUUID().toString()
                preferencesManager.setDeviceID(deviceId)
            }

            Logger.d(TAG, "getDeviceId = " + deviceId!!)
        }
        return deviceId
    }


    private var userAgent : String? = null

    fun getUserAgent() : String? {
        if(userAgent != null) {
            return userAgent
        }

        val webView = WebView(ActivityManager.instance.activity)
        userAgent = webView.settings.userAgentString
        webView.destroy()
        return userAgent
    }

}