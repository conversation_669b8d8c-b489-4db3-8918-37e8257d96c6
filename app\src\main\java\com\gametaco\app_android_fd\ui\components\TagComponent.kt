package com.gametaco.app_android_fd.ui.components

import android.graphics.BlurMaskFilter
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawOutline
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun TagComponent(
    contentAlignment:Alignment = Alignment.Center,
    contentPadding: PaddingValues = PaddingValues(start = 12.dp, end = 12.dp, top = 1.dp),
    offsetX:Dp? = null,
    offsetY:Dp ? = null,
    backgroundColor:Color = Color(0xFF0070EB),
    borderColor:Color = Color(0xFF64AEFF),
    height: Dp,
    content: @Composable ()->Unit
){
    val skewFactor = 0.025f
    val borderThickness = with(LocalDensity.current) { 1.5.dp.toPx() }
    val cornerRadius = with(LocalDensity.current) { 1.dp.toPx() }
    val shadowColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
    val shadowOffset = with(LocalDensity.current) { 2.dp.toPx() }
    val blur = 2.dp

    Box (
        contentAlignment = contentAlignment,
        modifier = Modifier
            .width(IntrinsicSize.Max)
            .height(height)
            .offset(x = offsetX ?: 0.dp, y = offsetY ?: 0.dp)
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
        ) {
            // Render shadow under everything else
            val shadowPath = Path().apply {
                moveTo(x = size.width * skewFactor, y = shadowOffset)
                lineTo(x = size.width, y = shadowOffset)
                lineTo(x = size.width * (1 - skewFactor), y = size.height + shadowOffset)
                lineTo(x = 0f, y = size.height + shadowOffset)
                close()
            }
            val shadowPaint = Paint().apply {
                color = shadowColor
                pathEffect = PathEffect.cornerPathEffect(cornerRadius)
            }
            shadowPaint.asFrameworkPaint().apply {
                // Apply blur to the Paint
                maskFilter = BlurMaskFilter(blur.toPx(), BlurMaskFilter.Blur.NORMAL)
            }
            drawIntoCanvas { canvas ->
                canvas.drawOutline(
                    outline = Outline.Generic(shadowPath),
                    paint = shadowPaint
                )
            }

            // Render border under fill
            val borderPath = Path().apply {
                moveTo(x = size.width * skewFactor, y = 0f)
                lineTo(x = size.width, y = 0f)
                lineTo(x = size.width * (1 - skewFactor), y = size.height)
                lineTo(x = 0f, y = size.height)
                close()
            }
            val borderPaint = Paint().apply {
                color = borderColor
                pathEffect = PathEffect.cornerPathEffect(cornerRadius)
            }
            drawIntoCanvas { canvas ->
                canvas.drawOutline(
                    outline = Outline.Generic(borderPath),
                    paint = borderPaint
                )
            }

            // Render fill last, before rendering content
            val fillSize = Size(
                width = size.width - borderThickness,
                height = size.height - borderThickness
            )
            val fillPath = Path().apply {
                moveTo(x = fillSize.width * skewFactor + borderThickness, y = borderThickness)
                lineTo(x = fillSize.width, y = borderThickness)
                lineTo(x = fillSize.width * (1 - skewFactor), y = fillSize.height)
                lineTo(x = borderThickness, y = fillSize.height)
                close()
            }
            val fillPaint = Paint().apply {
                color = backgroundColor
                pathEffect = PathEffect.cornerPathEffect(cornerRadius)
            }
            drawIntoCanvas { canvas ->
                canvas.drawOutline(
                    outline = Outline.Generic(fillPath),
                    paint = fillPaint
                )
            }
        }
        Box(
            modifier = Modifier
                .padding(contentPadding)
        ){
            content()
        }
    }
}

@Composable
@Preview
fun TagComponentPreview(){
    Box(modifier = Modifier
        .fillMaxSize()
        .background(Color.Gray)){
        Column {
            TagComponent(offsetX = 10.dp, offsetY = 0.dp, backgroundColor = Color(0xFFEA913C), borderColor = Color(0xFFFFB874), height = 20.dp){
                Column {
                    Text(
                        text = "This is a tag",
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
            }
            Spacer(modifier = Modifier.height(10.dp))

            TagComponent(offsetX = 10.dp, offsetY = 0.dp, height = 36.dp){
                Column {
                    Text(
                        text = "This is a tag",
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    Text(
                        text = "This is a tag",
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
            }
        }

    }
}