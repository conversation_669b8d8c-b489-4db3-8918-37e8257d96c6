package com.gametaco.app_android_fd.data.api

import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.data.entity.API400Error
import com.gametaco.app_android_fd.data.entity.API400ErrorCode
import com.gametaco.app_android_fd.data.entity.API400ErrorGeneralError
import com.gametaco.app_android_fd.data.entity.APIAuthToken
import com.gametaco.app_android_fd.data.entity.APIDailyDoorCollectedRewards
import com.gametaco.app_android_fd.data.entity.APIDailyDoorStatus
import com.gametaco.app_android_fd.data.entity.APIDailyDoorStatusRequest
import com.gametaco.app_android_fd.data.entity.APIGameTournamentsResponse
import com.gametaco.app_android_fd.data.entity.APIGameTournamentsResponseV2
import com.gametaco.app_android_fd.data.entity.APIGamesResponse
import com.gametaco.app_android_fd.data.entity.APIGeocomplyLicenseResponse
import com.gametaco.app_android_fd.data.entity.APIGeocomplyTokenExchangeRequest
import com.gametaco.app_android_fd.data.entity.APIGeocomplyTokenExchangeResponse
import com.gametaco.app_android_fd.data.entity.APIGoalsCollectableRequest
import com.gametaco.app_android_fd.data.entity.APIGoalsCollectableResponse
import com.gametaco.app_android_fd.data.entity.APIGoalsCollectedResponse
import com.gametaco.app_android_fd.data.entity.APIGoalsCompletableResponse
import com.gametaco.app_android_fd.data.entity.APIGoalsStatusResponse
import com.gametaco.app_android_fd.data.entity.APIGuestSignupResponse
import com.gametaco.app_android_fd.data.entity.APILoginRequest
import com.gametaco.app_android_fd.data.entity.APIMaintenanceModeStatus
import com.gametaco.app_android_fd.data.entity.APIPlayedGameIDResponse
import com.gametaco.app_android_fd.data.entity.APIPlayedGamesResponse
import com.gametaco.app_android_fd.data.entity.APIPostGoalsCollectableResponse
import com.gametaco.app_android_fd.data.entity.APIPostScoreRequest
import com.gametaco.app_android_fd.data.entity.APIPostScoreRequestGuest
import com.gametaco.app_android_fd.data.entity.APIPostScoreResponseGuest
import com.gametaco.app_android_fd.data.entity.APIRecentlyPlayedGamesResponse
import com.gametaco.app_android_fd.data.entity.APITournamentEntryStartRequest
import com.gametaco.app_android_fd.data.entity.APITournamentEntryStartRequestGuest
import com.gametaco.app_android_fd.data.entity.APITournamentEntryStartResponse
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntry
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntryList
import com.gametaco.app_android_fd.data.entity.APITournamentPlayerProfile
import com.gametaco.app_android_fd.data.entity.APITournamentReEntry
import com.gametaco.app_android_fd.data.entity.APITournamentRegisterReplayURLRequest
import com.gametaco.app_android_fd.data.entity.APITournamentReplayURLResponse
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinRequest
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinRequestGuest
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinResponse
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinResponseGuest
import com.gametaco.app_android_fd.data.entity.APITournamentsReEntryRequest
import com.gametaco.app_android_fd.data.entity.APITournamentsResponse
import com.gametaco.app_android_fd.data.entity.APIUserGameStats
import com.gametaco.app_android_fd.data.entity.APIWalletResponse
import com.gametaco.app_android_fd.data.entity.GameCatalogResponse
import com.gametaco.app_android_fd.data.entity.GameCatalogVersion
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.utilities.ResourceState
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import retrofit2.Response
import java.net.SocketTimeoutException
import java.net.UnknownHostException

@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class WithAuthentication(val value: String)

class WorldWinnerAPI(private val worldWinnerNetworkService: WorldWinnerNetworkService) {
    companion object{
        val instance: WorldWinnerAPI
            get() = resolve()
        const val TAG = "WorldWinnerAPI"
    }

    private var apiService: ApiService = worldWinnerNetworkService.getApiService()
    fun refreshApiService(){
        worldWinnerNetworkService.invalidRetrofit()
        apiService = worldWinnerNetworkService.getApiService()
    }

    private suspend fun <T> executeApiCall(
        apiCallId: String,
        apiCall: suspend () -> Response<T>,
    ): ResourceState<T> {
        val tag = "API:executeApiCall [$apiCallId]"
        try {
            val response = apiCall()
            if (response.isSuccessful) {
                return  ResourceState.Success(response.body()?:null as T)
            } else {
                val errorBody = response.errorBody()?.string() ?: "Unknown error"
                Logger.e("BACKEND ERROR $tag",errorBody ,response.code())
                return ResourceState.Error(errorBody, response.code())
            }
        } catch (e: SocketTimeoutException) {
            Logger.d(tag, e.localizedMessage ?: "Timeout Occurred")
            return ResourceState.Error(e.localizedMessage ?: "Timeout Occurred", 503)
        } catch (e: UnknownHostException) {
            Logger.d(tag,e.localizedMessage ?: "Unknown Host exception",503)
            return ResourceState.Error(e.localizedMessage ?: "Unknown Host exception", 503)
        } catch (e: Exception) {
            Logger.e(tag, e.localizedMessage ?: "Error captured")
            return ResourceState.Error(e.localizedMessage ?: "Some error occurred during the API call", 0)
        }
    }

    inline fun <reified T: Any> executeApiCallFlow(
        apiCallId: String,
        noinline apiCall: suspend () -> Response<T>,
    ): Flow<ResourceState<T>> = flow {
        val tag = "API:executeApiCall [$apiCallId]"
        emit(ResourceState.Loading())
        try {
            val response = apiCall()
            if (response.isSuccessful) {
                emit(ResourceState.Success(response.body()?:null as T))
            } else {
                val errorResponse = response.errorBody()?.string() ?: "Unknown error"
                Logger.e("BACKEND ERROR $tag", errorResponse, response.code())
                emit(ResourceState.Error(errorResponse, response.code()))
            }
        } catch (e: SocketTimeoutException) {
            Logger.d(tag, e.localizedMessage ?: "Timeout Occurred")
            emit(ResourceState.Error(e.localizedMessage ?: "Timeout Occurred", 503))
        } catch (e: UnknownHostException) {
            Logger.d(tag,e.localizedMessage ?: "Unknown Host exception",503)
            emit(ResourceState.Error(e.localizedMessage ?: "Unknown Host exception", 503))
        } catch (e: Exception) {
            Logger.e(tag,e.localizedMessage ?: "Some error in flow",0)
            emit(ResourceState.Error(e.localizedMessage ?: "Some error in flow", 0))
        }
    }

    suspend fun login(authToken: String, skip_staff_update : Boolean) : ResourceState<APIAuthToken> {
        return executeApiCall("login") {
            apiService.login(APILoginRequest(auth_token = authToken, skip_staff_update))
        }
    }

    suspend fun logout() : ResourceState<Void> {
        return executeApiCall("logout") {
            apiService.logout()
        }
    }

    suspend fun getAppSession() : ResourceState<Void> {
        return executeApiCall("getAppSession") {
            apiService.getAppSession()
        }
    }

    suspend fun getGameCatalog() : Flow<ResourceState<GameCatalogResponse>> {
        return executeApiCallFlow("getGameCatalog") {
            apiService.getGameCatalog()
        }
    }

    suspend fun getGameCatalogVersion() : ResourceState<GameCatalogVersion> {
        return executeApiCall("getGameCatalogVersion") {
            apiService.getGameCatalogVersion()
        }
    }

    suspend fun getTournamentEntries(cursor:String? = null) : ResourceState<APITournamentInstanceEntryList> {
        return executeApiCall("getTournamentEntries") {
            apiService.getTournamentEntries(cursor)
        }
    }

    suspend fun getTournamentEntry(id: String) : ResourceState<APITournamentInstanceEntry> {
//        Logger.d("WorldWinnerApi","getTournamentEntry(id: $id) - start")
        return executeApiCall("getTournamentEntry") {
            apiService.getTournamentEntry(id)
        }.also {
//            Logger.d("WorldWinnerApi", "getTournamentEntry(id: $id) - add to cache")
            updateTournamentEntryCache(id, it)
        }
    }

    private val tournamentEntryCache = mutableMapOf<String, ResourceState<APITournamentInstanceEntry>>()
    private fun updateTournamentEntryCache(id: String, result: ResourceState<APITournamentInstanceEntry>) {
        val existing = tournamentEntryCache[id]
        if (existing is ResourceState.Success && result is ResourceState.Success) {
            if (existing.data == result.data) {
                return
            }
        }

        tournamentEntryCache[id] = result
    }

    fun getTournamentEntryFlow(id: String): Flow<ResourceState<APITournamentInstanceEntry>> = flow {
        val cachedResult = tournamentEntryCache[id]

        if (cachedResult == null) {
            emit(ResourceState.Loading())
        } else {
            emit(cachedResult)
        }
        val result = getTournamentEntry(id)
        emit(result)
    }

    suspend fun postTournamentReEntries(id: String, request: APITournamentsReEntryRequest) : ResourceState<APITournamentReEntry> {
        return executeApiCall("postTournamentReEntries") {
            apiService.postTournamentReEntries(id, request)
        }
    }

    suspend fun postTournamentEntryScore(request: APIPostScoreRequest) : ResourceState<APIUserGameStats> {
        return executeApiCall("postTournamentEntryScore") {
            apiService.postTournamentEntryScore(request)
        }
    }

    suspend fun postTournamentEntryStart(request: APITournamentEntryStartRequest) : ResourceState<APITournamentEntryStartResponse> {
        return executeApiCall("postTournamentEntryStart") {
            apiService.postTournamentEntryStart(request)
        }
    }

    suspend fun getGames() : ResourceState<APIGamesResponse> {
        return executeApiCall("getGames") {
            apiService.getGames()
        }
    }

    suspend fun getTournamentsByGameIdSorted(id : String) : ResourceState<APIGameTournamentsResponse> {
        return executeApiCall("getTournamentsByGameIdSorted") {
            apiService.getTournamentsByGameIdSorted(id)
        }
    }
    suspend fun getTournamentsByGameIdSortedV2(id : String) : ResourceState<APIGameTournamentsResponseV2> {
        return executeApiCall("getTournamentsByGameIdSortedV2") {
            apiService.getTournamentsByGameIdSortedV2(id)
        }
    }
    suspend fun getGeocomplyLicense() : ResourceState<APIGeocomplyLicenseResponse> {
        return executeApiCall("getGeocomplyLicense") {
            apiService.getGeocomplyLicense()
        }
    }

    suspend fun postGeocomplyTokenExchange(request: APIGeocomplyTokenExchangeRequest) : ResourceState<APIGeocomplyTokenExchangeResponse> {
        return executeApiCall("postGeocomplyTokenExchange") {
            apiService.postGeocomplyTokenExchange(request)
        }
    }

    suspend fun getGoalsCollectable() : ResourceState<APIGoalsCollectableResponse> {
        return executeApiCall("getGoalsCollectable") {
            apiService.getGoalsCollectable()
        }
    }

    suspend fun postGoalsCollectable(request: APIGoalsCollectableRequest) : ResourceState<APIPostGoalsCollectableResponse> {
        return executeApiCall("postGoalsCollectable") {
            apiService.postGoalsCollectable(request)
        }
    }

    suspend fun getGoalsCollected() : ResourceState<APIGoalsCollectedResponse> {
        return executeApiCall("getGoalsCollected") {
            apiService.getGoalsCollected()
        }
    }

    suspend fun getGoalsCompletable() : ResourceState<APIGoalsCompletableResponse> {
        return executeApiCall("getGoalsCompletable") {
            apiService.getGoalsCompletable()
        }
    }
    suspend fun getDailyDoorStatus(request: APIDailyDoorStatusRequest) : ResourceState<APIDailyDoorStatus> {
        return executeApiCall("getDailyDoorStatus") {
            apiService.getDailyDoorStatus(request.current_date,request.is_digest)
        }
    }
    suspend fun getDailyDoorCollectedRewards() : ResourceState<APIDailyDoorCollectedRewards> {
        return executeApiCall("getDailyDoorCollectedRewards") {
            apiService.getDailyDoorCollectedRewards()
        }
    }
    suspend fun getMe() : ResourceState<APIMe> {
        return executeApiCall("getMe") {
            apiService.getMe()
        }
    }

    suspend fun getPlayedGames() : ResourceState<APIPlayedGamesResponse> {
        return executeApiCall("getPlayedGames") {
            apiService.getPlayedGames()
        }
    }

    suspend fun getPlayedGameByID(id: String) : ResourceState<APIPlayedGameIDResponse> {
        return executeApiCall("getPlayedGameByID") {
            apiService.getPlayedGameByID(id)
        }
    }
    suspend fun getRecentPlayedGames() : ResourceState<APIRecentlyPlayedGamesResponse> {
        return executeApiCall("getRecentPlayedGames") {
            apiService.getRecentlyPlayedGames()
        }
    }
    suspend fun postPotentialDepositStart() : ResourceState<Void> {
        return executeApiCall("postPotentialDepositStart") {
            apiService.postPotentialDepositStart()
        }
    }

    suspend fun postPotentialDepositEnd() : ResourceState<Void> {
        return executeApiCall("postPotentialDepositEnd") {
            apiService.postPotentialDepositEnd()
        }
    }

    suspend fun getTournamentsByGameID(game_id: String) : ResourceState<APITournamentsResponse> {
        return executeApiCall("getTournamentsByGameID") {
            apiService.getTournamentsByGameID(game_id)
        }
    }

    suspend fun getTournamentsByTournamentIDs(tournamentIds: String) : ResourceState<APITournamentsResponse> {
        return executeApiCall("getTournamentsByTournamentIDs") {
            apiService.getTournamentsByTournamentIDs(tournamentIds)
        }
    }

    suspend fun postJoinTournament(request: APITournamentsJoinRequest) : ResourceState<APITournamentsJoinResponse> {
        return executeApiCall("postJoinTournament") {
            apiService.postJoinTournament(request)
        }
    }

    suspend fun getWallet() : ResourceState<APIWalletResponse> {
        return executeApiCall("getWallet") {
            apiService.getWallet()
        }
    }

    suspend fun postPotentialDeposit() : ResourceState<Void> {
        return executeApiCall("postPotentialDeposit") {
            apiService.postPotentialDeposit()
        }
    }

    suspend fun getGoalsStatus(id: String) : ResourceState<APIGoalsStatusResponse> {
        return executeApiCall("getGoalsStatus") {
            apiService.getGoalsStatus(id)
        }
    }

    suspend fun getMaintenanceModeStatus() : ResourceState<APIMaintenanceModeStatus> {
        return executeApiCall("getMaintenanceModeStatus") {
            apiService.getMaintenanceModeStatus(AppEnv.current.maintenanceURL)
        }
    }

    suspend fun getTournamentPlayerProfile(id: String,game_mode_id: String? = null):ResourceState<APITournamentPlayerProfile>{
        return executeApiCall("getTournamentPlayerProfile") {
            apiService.getTournamentPlayerProfile(id,game_mode_id)
        }
    }

//  ==== GUEST =====================================================================================

    suspend fun getGuestMe() : ResourceState<APIGuestMe> {
        return executeApiCall("getGuestMe") {
            apiService.getGuestMe()
        }
    }

    suspend fun getGuestTournamentsByGameID(game_id: String) : ResourceState<APITournamentsResponse> {
        return executeApiCall("getGuestTournamentsByGameID") {
            apiService.getGuestTournamentsByGameID(game_id)
        }
    }

    suspend fun getGuestTournamentsByTournamentIDs(tournamentIds: String) : ResourceState<APITournamentsResponse> {
        return executeApiCall("getGuestTournamentsByTournamentIDs") {
            apiService.getGuestTournamentsByTournamentIDs(tournamentIds)
        }
    }

    suspend fun postGuestJoinTournament(request: APITournamentsJoinRequestGuest) : ResourceState<APITournamentsJoinResponseGuest> {
        return executeApiCall("postGuestJoinTournament") {
            apiService.postGuestJoinTournament(request)
        }
    }

    suspend fun getGuestGames() : ResourceState<APIGamesResponse> {
        return executeApiCall("getGuestGames") {
            apiService.getGuestGames()
        }
    }

    suspend fun postGuestSignup() : ResourceState<APIGuestSignupResponse> {
        return executeApiCall("postGuestSignup") {
            apiService.postGuestSignup()
        }
    }

    suspend fun postGuestTournamentEntryStart(request: APITournamentEntryStartRequestGuest) : ResourceState<APITournamentEntryStartResponse> {
        return executeApiCall("postGuestTournamentEntryStart") {
            apiService.postGuestTournamentEntryStart(request)
        }
    }

    suspend fun postGuestTournamentEntryScore(request: APIPostScoreRequestGuest) : ResourceState<APIPostScoreResponseGuest> {
        return executeApiCall("postGuestTournamentEntryScore") {
            apiService.postGuestTournamentEntryScore(request)
        }
    }


    suspend fun getReplayUploadURL(tournament_instance_entry_id: String, is_user_viewable: Boolean) : ResourceState<APITournamentReplayURLResponse> {
        return executeApiCall("getReplayUploadURL") {
            apiService.getReplayUploadURL(tournament_instance_entry_id, is_user_viewable)
        }
    }

    suspend fun postRegisterReplayUploadURL(request: APITournamentRegisterReplayURLRequest) : ResourceState<Void> {
        return executeApiCall("postRegisterReplayUploadURL") {
            apiService.postRegisterReplayUploadURL(request)
        }
    }

    fun stringTo400Error(errorString: String): API400Error? {
        try {
            val moshi = Moshi.Builder()
                .add(KotlinJsonAdapterFactory())
                .build()
            val jsonAdapter = moshi.adapter(API400Error::class.java)
            return jsonAdapter.fromJson(errorString)
        }catch (e:Exception){
            Logger.e("json decode error : ${e}")
            return API400Error(field_errors = null, general_error = API400ErrorGeneralError(message = errorString,code = API400ErrorCode.UNKNOWN_ERROR))
        }
    }
}
