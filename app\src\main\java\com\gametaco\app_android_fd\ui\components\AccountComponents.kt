package com.gametaco.app_android_fd.ui.components

import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.FDWebviewPage
import com.gametaco.app_android_fd.manager.HapticFeedbackType
import com.gametaco.app_android_fd.manager.HapticsManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.utilities.STR
import resources.R

@Composable
fun AccountOption(
    gameIcon: Int,
    title: String,
    isAvailableForGuest: Boolean,
    webviewPage : FDWebviewPage,
    webviewURL: String? = null,
    hasdivider: Boolean = true,
    clickFunction: (() -> Unit)? = null
) {
    val isEnabled: Boolean = isAvailableForGuest || !(LocalInspectionMode.current || AuthenticationManager.instance.isGuest)
    Box (modifier = Modifier.wrapContentSize()) {
    }
    Box(
        modifier = Modifier
            .background(Color.White)
            .drawWithContent {
                drawContent()
                if (!isEnabled) drawRect(Color(0xFF969DA3), alpha = .5f)
            }
    )
    {
        Column(
            modifier = Modifier
                .clickable(
                enabled = true,
                onClick = {
                    if (isEnabled) {
                        if (clickFunction != null) {
                            clickFunction?.invoke()
                        } else if (webviewURL != null) {
                            FDManager.instance.showWebviewFD(null,webviewURL,title)
                        } else if (webviewPage != FDWebviewPage.None) {
                            FDManager.instance.showWebviewFD(webviewPage,null,title)
                        }
                    }
                }
            ),
        ) {
            Row (
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp, 0.dp)
                    .height(42.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge.copy(fontSize = 18.sp),
                    color = if (isEnabled) Color.Black else Color(0xFF969DA3),
                    modifier = Modifier
                        .padding(top = 2.dp)
                )
                Image(
                    painter = painterResource(gameIcon),
                    contentDescription = null,
                    colorFilter = ColorFilter.tint(Color.Gray),
                    modifier = Modifier.size(24.dp)
                )
            }
            Spacer(modifier = Modifier.height(2.dp))
            if(hasdivider)
            {
                HorizontalDivider(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp, 0.dp, 0.dp, 0.dp)
                        .background(MaterialTheme.colorScheme.surface),
                    thickness = 1.dp,
                    color = MaterialTheme.colorScheme.outline
                )
            }
        }
    }
}


@Composable
fun AccountOptionBorderDivider()
{
    HorizontalDivider(
        modifier = Modifier
            .fillMaxWidth(),
        thickness = 1.dp,
        color = MaterialTheme.colorScheme.outline
    )
}

@Preview
@Composable
fun HapticsPreview()
{
//    HapticsScreen(isExpanded = true) {
//
//    }
    BalanceDetailsPopup(isExpanded = true) {
        
    }
}
@Composable
fun HapticsScreen(
    isExpanded:Boolean,
    onClose: ()-> Unit
){
    val togglesList:List<@Composable () -> Unit> = listOf {
        ToggleSwitch(
            title = STR(R.string.haptics),
            initialValue = PreferencesManager.instance.getIsHapticsEnabled(),
            valueSetFunction = { value -> PreferencesManager.instance.setIsHapticsEnabled(value)
                if (value){
                    HapticsManager.instance.playEffect(HapticFeedbackType.SELECTION)
                }
            },
            valueGetFunction = { PreferencesManager.instance.getIsHapticsEnabled()}
        )
    }

    BottomSheetModal(
        title = STR(R.string.haptics),
        titleStyle = MaterialTheme.typography.titleMedium,
        showBackButton = true,
        showDivider = true,
        expanded = isExpanded,
        closeAction = onClose,
        contentBackgroundColor = MaterialTheme.colorScheme.surfaceVariant,
        enterTransition = slideInHorizontally(initialOffsetX = { fullWidth ->  fullWidth}),
        exitTransition = slideOutHorizontally(targetOffsetX = { fullWidth ->  fullWidth}),
    ){
        Spacer(Modifier.height(24.dp))
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
        )
        {
            AccountOptionBorderDivider()
            Spacer(modifier = Modifier.height(12.dp))
            ContentListBody(listElements = togglesList, precedingSpacerHeight = 0.dp)
            Spacer(modifier = Modifier.height(12.dp))
            AccountOptionBorderDivider()
        }
    }
}

@Composable
fun BalanceDetailsPopup(
    isExpanded:Boolean,
    onClose: ()-> Unit
)
{
    BottomSheetModal(title = STR(R.string.playable_balance), expanded = isExpanded, closeAction = onClose) {
        Surface()
        {
            Column(
                modifier = Modifier
                    .fillMaxHeight(.9f)
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(
                    space = 24.dp,
                    alignment = Alignment.Top
                ),
            ) {
                Text(STR(R.string.these_are_all_the_funds), style = MaterialTheme.typography.bodySmall)
                BalanceDetailsTextBlock(title = STR(R.string.shared_deposits), body = STR(R.string.you_can_use_deposits_made_to))
                BalanceDetailsTextBlock(title = STR(R.string.bonuses), body = STR(R.string.you_can_use_bonuses_from))
                BalanceDetailsTextBlock(title = STR(R.string.shared_winnings), body = STR(R.string.you_can_use_any_of_your_winnings))
            }
        }
    }
}

@Composable
fun BalanceDetailsTextBlock(
    title:String,
    body:String
)
{
    Column()
    {
        Text(
            text = title,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Bold
        )
        Spacer(Modifier.height(4.dp))
        Text(
            text = body,
            style = MaterialTheme.typography.bodySmall
        )
    }
}
