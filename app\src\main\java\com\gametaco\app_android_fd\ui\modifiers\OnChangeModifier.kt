package com.gametaco.app_android_fd.ui.modifiers

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

@Composable
fun <T>Modifier.onChange(viewModel: ViewModel,data: StateFlow<T>,cb:(T) -> Unit): Modifier {
    LaunchedEffect(Unit) {
        viewModel.viewModelScope.launch {
            data.collect{
                cb(it)
            }
        }
    }
    return this
}

@Composable
fun <T>Modifier.onChange(data: T,cb:(T) -> Unit): Modifier {
    LaunchedEffect(data) {
        cb(data)
    }
    return this
}