name: Save Commit SHA

on:
  workflow_dispatch:

jobs:
  save-commit-sha:
    runs-on: self-hosted
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Save current commit SHA
        run: |
          echo $(git rev-parse HEAD) > _last_build_sha.txt

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: "us-west-2"

      - name: Check if AWS CLI is installed
        id: check_aws_cli
        run: |
          if ! command -v aws &> /dev/null; then
            echo "AWS CLI is not installed"
            echo "install_aws_cli=true" >> $GITHUB_ENV
          else
            echo "AWS CLI is already installed"
            echo "install_aws_cli=false" >> $GITHUB_ENV
          fi

      - name: Install AWS CLI
        if: ${{ env.install_aws_cli == 'true' }}
        run: |
          sudo apt-get update
          sudo apt-get install -y awscli

      - name: Upload last build SHA to S3
        run: |
          aws s3 cp _last_build_sha.txt s3://faceoff-android-dev-release/_last_build_sha.txt