package com.gametaco.app_android_fd.ui.components
import android.net.Uri
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.braze.jetpackcompose.contentcards.cards.BrazeCustomContentCard
import com.braze.jetpackcompose.contentcards.styling.ContentCardStyling
import com.braze.models.cards.Card
import com.gametaco.app_android_fd.manager.BrazeEventName
import com.gametaco.app_android_fd.manager.BrazeManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.ui.modifiers.onAppear
import com.gametaco.app_android_fd.utils.log.Logger
import org.json.JSONObject


@Composable
fun BrazeBanner(
    card: Card,
    modifier: Modifier = Modifier
){
    modifier.onAppear { BrazeManager.instance.requestContentCardsRefresh() }

    BrazeCustomContentCard(card = card,
        clickHandler = { card ->
            try{
                val uri = Uri.parse(card.url)
                val event = JSONObject()
                event.put("BannerID",card.id)
                event.put("action token",uri.host)
                event.put("arg token",uri.query)
                BrazeManager.instance.logEvent(BrazeEventName.Promo_Banner_Clicked.value,event)
                AnalyticsManager.instance.logEvent(
                    AnalyticsEvent(analyticsEvent = AnalyticsEventName.Promotion_Opened.value,
                        properties = mapOf("Promotion ID" to card.id, "Promotion Name" to (card.extras.get("promotion_name") ?:"Unknown"),
                            "Message" to uri.host.toString(), "Promotion Position" to "0"))
                )
            }catch (e:Exception){
                Logger.e("Braze Card Click Error:" + e.message)
            }
            false
        }, ContentCardStyling(
            listPadding = 0.dp,
            borderRadius = 18.dp,
            borderSize = 1.dp,
            shadowRadius = 18.dp,
            shadowColor = Color(0,0,0,80),
            borderColor = MaterialTheme.colorScheme.outline
        )
    )
}

