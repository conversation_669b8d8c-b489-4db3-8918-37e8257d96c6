package com.worldwinner.fanduel.uitest

import org.junit.jupiter.api.Test

class FanduelSignInUiTest : FanduelUiTestBase() {


    // location permission
    @Test
    fun runToGrantFirstRunLocationPermission() = uiTest {
        uiTestManager.runToGrantFirstRunLocationPermission()
    }

    // test validation on login page
    @Test
    fun runTextValidationOnLoginPage() = uiTest {
        uiTestManager.runTextValidationOnLoginPage()
    }

    // Login
    @Test
    fun runToSignIn_success() = uiTest {
        uiTestManager.runToSignIn_success()
    }

    @Test
    fun runToSignIn_failure_invalid_email() = uiTest {
        uiTestManager.runToSignIn_failure_invalid_email()
    }

    @Test
    fun runToSignIn_failure_invalid_email_incorrect_password() = uiTest {
        uiTestManager.runToSignIn_failure_invalid_email_incorrect_password()
    }

    @Test
    fun runToSignIn_failure_incorrect_password() = uiTest {
        uiTestManager.runToSignIn_failure_incorrect_password()
    }

}