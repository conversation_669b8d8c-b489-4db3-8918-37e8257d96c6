package com.gametaco.app_android_fd.manager

import android.os.Handler
import android.os.Looper
import com.gametaco.app_android_fd.BuildConfig
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.APIMaintenanceModeStatus
import com.gametaco.app_android_fd.data.entity.SemanticVersion
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.CoroutineScopes
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.utilities.ResourceState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class MaintenanceModeManager(
    private val worldWinnerAPI: WorldWinnerAPI,
    private val navManager: NavManager,
    private val tournamentManager: TournamentManager,
    private val coroutineScopes: CoroutineScopes,
) {

    companion object {
        val instance: MaintenanceModeManager
            get() = resolve()
        const val TAG = "MaintenanceModeManager"
    }

    private val handler = Handler(Looper.getMainLooper())
    private val pollInterval: Long = 30000  // 30 seconds in milliseconds

    var isPolling = false
    var hasResponse = false
    var responseIsError = false

    private val _maintenanceStatus= MutableStateFlow(APIMaintenanceModeStatus("", false, "", "", "", "", ""))
    val maintenanceStatus: StateFlow<APIMaintenanceModeStatus> = _maintenanceStatus.asStateFlow()

    var maintenanceResponse : ResourceState<APIMaintenanceModeStatus>? = ResourceState.Loading()

//    var maintenanceDownTest = APIMaintenanceModeStatus(
//        force_update_message = "Please install the latest version of FanDuel Faceoff to keep playing.",
//        maintenance_mode = true,
//        maintenance_mode_title = "We'll be back soon!",
//        maintenance_mode_message = "Sorry! We're currently down for maintenance.  Services are expected to be fully available again by 08:00 EST.",
//        android_minimum_app_version = "2.0.0",
//        android_soft_minimum_app_version = "2.0.0",
//        android_download_url = "https://<example.com>/android"
//    )
//
//
//    var maintenanceVersionTest = APIMaintenanceModeStatus(
//        force_update_message = "Please install the latest version of FanDuel Faceoff to keep playing.",
//        maintenance_mode = false,
//        maintenance_mode_title = "We'll be back soon!",
//        maintenance_mode_message = "Sorry! We're currently down for maintenance.  Services are expected to be fully available again by 08:00 EST.",
//        android_minimum_app_version = "20.0.0",
//        android_soft_minimum_app_version = "2.0.0",
//        android_download_url = "https://<example.com>/android"
//    )


    fun reset(){
        isPolling = false
        hasResponse = false
        responseIsError = false
    }

    fun pollMaintenanceModeStatus() {
        if (isPolling) return

        coroutineScopes.io.launch {
            // Initial call before the pollInterval timer starts
            fetchAndHandleMaintenanceModeStatus()
        }
        handler.postDelayed(object : Runnable {
            override fun run() {
                if (tournamentManager.getTournamentEntryState() == null) { //only poll maintenance while not in-game
                    coroutineScopes.io.launch {
                        fetchAndHandleMaintenanceModeStatus()
                    }
                }
                handler.postDelayed(this, pollInterval)
            }
        }, pollInterval)
        isPolling = true
    }

    private suspend fun fetchAndHandleMaintenanceModeStatus() {
        maintenanceResponse = worldWinnerAPI.getMaintenanceModeStatus()

        responseIsError = false
        when(val response = maintenanceResponse) {
            is ResourceState.Success -> {
//                handleMaintenanceMode(maintenanceDownTest)
//                handleMaintenanceMode(maintenanceVersionTest)
               handleMaintenanceMode(response.data)
                hasResponse = true
            }
            is ResourceState.Error -> {
                Logger.d(TAG, "Error in fetchAndHandleMaintenanceModeStatus: ${response.error}")
                responseIsError = true
                hasResponse = true
            }
            is ResourceState.Loading -> { }
            null -> { }
        }
    }

    private fun handleMaintenanceMode(status: APIMaintenanceModeStatus) {

        _maintenanceStatus.value = status

        //do nothing during tournament flow
        if(tournamentManager.getTournamentEntryState() != null)
            return

        //already on maintenance screen
        if(navManager.currentDestinationRoute == Routes.MAINTENANCE_SCREEN)
            return

        //maintenance mode required
        if (status.maintenance_mode) {
            // Run on the main thread
            Handler(Looper.getMainLooper()).post {
                navManager.navigateClearBackStack(Routes.MAINTENANCE_SCREEN)
            }
            return
        }

        //app update required
        if (isUnderMinimumVersion(status)) {
            // Run on the main thread
            Handler(Looper.getMainLooper()).post {
                navManager.navigateClearBackStack(Routes.MAINTENANCE_SCREEN)
            }
            return
        }
    }


    fun maintenanceRequired() : Boolean {
        if(responseIsError || !hasResponse)
            return false

        //maintenance mode required
        if (_maintenanceStatus.value.maintenance_mode) {
            return true
        }

        //app update required
        if (isUnderMinimumVersion(_maintenanceStatus.value)) {
            return true
        }

        return false
    }

    fun isUnderMinimumVersion(status: APIMaintenanceModeStatus): Boolean {
        val currentAppVersion = BuildConfig.VERSION_NAME
        val currentAppSemanticVersion = SemanticVersion.fromString(currentAppVersion)!!
        val minAppSemanticVersion = SemanticVersion.fromString(status.android_minimum_app_version)!!

        return currentAppSemanticVersion < minAppSemanticVersion
    }


}