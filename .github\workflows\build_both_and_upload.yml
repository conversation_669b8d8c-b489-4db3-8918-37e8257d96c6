name: Build and Upload (QA and Production)

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to build'
        required: true
        default: 'main'

jobs:
  unity_build:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Unity Build
        uses: peter-evans/workflow-dispatch@v1
        with:
          workflow: build_and_upload.yml
          ref: ${{ github.event.inputs.branch }}
          inputs: '{"buildType": "Unity"}'
          wait-for-completion: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  production_build:
    runs-on: ubuntu-latest
    needs: unity_build
    steps:
      - name: Wait for 1 minute
        run: sleep 60

      - name: Trigger Production Build
        uses: peter-evans/workflow-dispatch@v1
        with:
          workflow: build_and_upload.yml
          ref: ${{ github.event.inputs.branch }}
          inputs: '{"buildType": "Production"}'
          wait-for-completion: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
