package com.gametaco.app_android_fd.ui.screens

import TournamentEntryState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layout
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.compose.AsyncImagePainter.State
import com.gametaco.app_android_fd.LocalUIManager
import com.gametaco.app_android_fd.data.entity.entryFeeLabel
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.models.GameplayDataModel
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.viewmodel.GameplayViewModel
import com.gametaco.utilities.STR
import resources.R

const val TAG_GAMEPLAY_SCREEN = "GameplayScreen"

@Composable
fun WwProgressBar(
    progress: Float, // This is a Float ranging from 0.0 to 1.0
    text: String = "",
    backgroundColor: Color = Color(0x30FFFFFF)
) {
    val progressBarHeight = 20.dp
    val cornerRadius = 20.dp
//    val fillGradient = Brush.horizontalGradient(
//        colors = listOf(Color(0xFF3BFFF3), Color(0xFF00E0FF))
//    )

    Box(
//        contentAlignment = Alignment.Center,  // Centers contents both horizontally and vertically
        modifier = Modifier.fillMaxSize()  // Ensures the Box takes up the full width of its parent
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .layout { measurable, constraints ->
                    val placeable = measurable.measure(constraints)
                    layout(constraints.maxWidth, constraints.maxHeight) {
                        placeable.placeRelative(
                            x = 0,
                            y = (constraints.maxHeight * .6f).toInt()
                        )
                    }
                }
        )
        {
            Text(
                modifier = Modifier.fillMaxWidth(),
                text = text,
                textAlign = androidx.compose.ui.text.style.TextAlign.Center,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onPrimary,
                fontWeight = FontWeight.Bold,
            )
            Spacer(modifier = Modifier.height(8.dp))
            Box(
                modifier = Modifier
                    .height(8.dp)
                    .width(150.dp)
                    .background(color = backgroundColor, shape = RoundedCornerShape(cornerRadius))
            ) {
                if (progress < 1.0f) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(progress)
                            .height(progressBarHeight)
                            .background(
                                color = MaterialTheme.colorScheme.onPrimary,
                                shape = RoundedCornerShape(cornerRadius)
                            )
                            .padding(top = 3.dp)
                    )
                } else {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(progressBarHeight)
                            .background(
                                color = MaterialTheme.colorScheme.onPrimary,
                                shape = RoundedCornerShape(cornerRadius)
                            )
                            .padding(top = 3.dp)
                    )
                }
            }
            Spacer(modifier = Modifier.height(16.dp))

            val entryFeeState = TournamentManager.instance.tournamentInstanceEntryFee.collectAsState(null)
            val entryFee = entryFeeState.value
            if (entryFee != null) {
                Box(
                    modifier = Modifier
                        .background(Color(0x4D0A0A0A), RoundedCornerShape(12.dp))
                        .padding(10.dp, 6.dp)
                ) {
                    Text(
                        text = entryFeeLabel(entryFee.isFree,entryFee.string,"Entry"), textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyLarge.copy(
                            fontWeight = FontWeight.W700,
                            fontSize = 16.sp
                        ),
                        color = MaterialTheme.colorScheme.onPrimary,
                    )
                }
            }
        }
    }
}


@Composable
fun GameplayScreen(gameplayViewModel: GameplayViewModel) {
    val dataModel = gameplayViewModel.gameplayDataModel.collectAsState()
    dataModel.value?.also {
        GameplayScreen(gameplayDataModel = it)
    }
}

@Composable
fun getLoadingString(tournamentEntryState: TournamentEntryState?) : String {

    return when {
        tournamentEntryState is TournamentEntryState.JoinRequested -> STR(R.string.finding_match)
        else -> STR(R.string.loading_game)
    }
}

@Composable
fun GameplayScreen(gameplayDataModel: GameplayDataModel){
    var TAG = TAG_GAMEPLAY_SCREEN
    val uiManager = LocalUIManager.current
    val loadingProgress = gameplayDataModel.loadingProgress
    val backgroundURL = gameplayDataModel.backgroundURL
    var hideLoadingScreenContent = gameplayDataModel.hideLoadingScreenContent

    var backgroundImageCompletedAnyState by remember { mutableStateOf(false) }
    var backgroundImageSuccess by remember { mutableStateOf(false) }

    var tournamentEntryState = TournamentManager.instance.tournamentEntryStateState.value


    // Once all images are loaded, perform additional tasks
    LaunchedEffect(backgroundImageCompletedAnyState) {
        if (backgroundImageCompletedAnyState) {
            gameplayDataModel.onAllImagesLoaded()
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {

        if (hideLoadingScreenContent)
            return

        Logger.d(TAG, "backgroundURL: $backgroundURL")

        // Background image
        AsyncImage(
            model = backgroundURL,
            contentDescription = null,
            modifier = Modifier.fillMaxHeight(),
            contentScale = ContentScale.Crop,
            onState = { state ->
                Logger.d(TAG, "Background Image State: ${state}")

                if(state is State.Empty) {
                    backgroundImageSuccess= false
                    backgroundImageCompletedAnyState = false
                }

                backgroundImageCompletedAnyState = true
                backgroundImageSuccess = state is State.Success
            }
        )

        // Show the loading screen up until the background image was successfully displayed
        if (!backgroundImageSuccess) {
            LoadingScreen()
        } else {

            // Centered loading bar
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
                    .offset(y = 30.dp)
            ) {
                WwProgressBar(
                    progress = loadingProgress,
                    text = getLoadingString(tournamentEntryState),
                )
            }
        }
    }
}


@Preview
@Composable
fun GameplayScreenPreview(){
    PreviewRoot {
//        GameplayScreen(GameplayViewModel.instance)
        WwProgressBar(progress = 0.5f,
            text = STR(R.string.loading_game),
        )
    }
}
