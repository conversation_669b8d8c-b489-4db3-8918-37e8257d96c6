package com.gametaco.utilities

import androidx.compose.runtime.Immutable

@Immutable
sealed class ResourceState <T> {
    @Immutable
    class Loading<T> : ResourceState<T>()
    @Immutable
    data class Success<T> (val data:T) : ResourceState<T>()
    @Immutable
    data class Error<T> (val error:String, val code:Int) : ResourceState<T>()


    // This function checks if the current state is a Success state.
    fun isSuccess(): Boolean = this is Success<T>

    // This function checks if the current state is a Loading state.
    fun isLoading(): Bo<PERSON>an = this is Loading<T>

    // This function checks if the current state is an Error state.
    fun isError(): <PERSON><PERSON>an = this is Error<T>
}