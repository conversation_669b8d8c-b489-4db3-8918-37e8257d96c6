package com.gametaco.app_android_fd.ui.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.sp
import resources.R

object AppFont {
    val FanduelDisplay = FontFamily(
        Font(R.font.fandueldisplay_extrablacksemiexpanded, FontWeight.Black),
    )
    val ProximaNova = FontFamily(
        Font(R.font.proximanova_black, FontWeight.Black),
        Font(R.font.proximanova_bold, FontWeight.Bold),
        Font(R.font.proximanova_boldit, FontWeight.Bold, style = FontStyle.Italic),
        <PERSON>ont(R.font.proximanova_extrabold, FontWeight.ExtraBold),
        Font(R.font.proximanova_light, FontWeight.Light),
        Font(R.font.proximanova_lightitalic, FontWeight.Light, style = FontStyle.Italic),
        Font(R.font.proximanova_regitalic, style = FontStyle.Italic),
        Font(R.font.proximanova_regular),
        Font(R.font.proximanova_regularitalic, style = FontStyle.Italic),
        Font(R.font.proximanova_semibold, FontWeight.SemiBold),
        Font(R.font.proximanova_semibolditalic, FontWeight.SemiBold, style = FontStyle.Italic),
    )
    val ProximaNovaCond = FontFamily(
        Font(R.font.proximanovacond_light, FontWeight.Light),
        Font(R.font.proximanovacond_lightit, FontWeight.Light, style = FontStyle.Italic),
        Font(R.font.proximanovacond_regular),
        Font(R.font.proximanovacond_regularit, style = FontStyle.Italic),
        Font(R.font.proximanovacond_semibold, FontWeight.SemiBold),
        Font(R.font.proximanovacond_semiboldit, FontWeight.SemiBold, style = FontStyle.Italic),
    )
    val Shentox = FontFamily(
        Font(R.font.shentox_bold, FontWeight.Bold)
    )
    val Balboa = FontFamily(
        Font(R.font.balboa_extrabold,FontWeight.Bold),
        Font(R.font.balboa_light,FontWeight.Light)
    )
    val Knowkout = FontFamily(
        Font(R.font.knockout_htf29_juniorliteweight),
        Font(R.font.knockout_htf49_liteweight,FontWeight.Light),
        Font(R.font.knockout_ftf69_fullliteweight, FontWeight.Bold),
    )
}

// Set of Material typography styles to start with
val Typography = Typography(
    titleLarge = TextStyle(
        fontFamily = AppFont.ProximaNova,
        fontWeight = FontWeight.Bold,
        fontSize = 36.sp,
        lineHeight = 40.sp
    ),
    titleMedium = TextStyle(
        fontFamily = AppFont.ProximaNova,
        fontWeight = FontWeight.Bold,
        fontSize = 18.sp,
        lineHeight = 24.sp
    ),
    titleSmall = TextStyle(
        fontFamily = AppFont.ProximaNova,
        fontWeight = FontWeight.Bold,
        fontSize = 16.sp,
        lineHeight = 24.sp,
//        letterSpacing = 0.5.sp
    ),
    bodyLarge = TextStyle(
        fontFamily = AppFont.ProximaNova,
        fontWeight = FontWeight.Medium,
        fontSize = 16.sp,
        lineHeight = 20.sp
    ),
    bodyMedium = TextStyle(
        fontFamily = AppFont.ProximaNova,
        fontWeight = FontWeight.Bold,
        fontSize = 14.sp,
        lineHeight = 18.sp
    ),
    bodySmall = TextStyle(
        fontFamily = AppFont.ProximaNova,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 18.sp
    ),
    headlineLarge = TextStyle(
        fontFamily = AppFont.ProximaNova,
        fontWeight = FontWeight.Bold,
        fontSize = 14.sp,
        lineHeight = 24.sp
    ),
    headlineMedium = TextStyle(
        fontFamily = AppFont.ProximaNova,
        fontWeight = FontWeight.Medium,
        fontSize = 12.sp,
        lineHeight = 14.sp
    ),
    headlineSmall = TextStyle(
        fontFamily = AppFont.ProximaNovaCond,
        fontWeight = FontWeight.Medium,
        fontSize = 12.sp,
        lineHeight = 14.sp,
        letterSpacing = 1.0.sp,
    ),
    displayLarge = TextStyle(
        fontFamily = AppFont.ProximaNovaCond,
        fontWeight = FontWeight.Medium,
        fontStyle = FontStyle.Italic,
        fontSize = 30.sp,
        lineHeight = 42.sp,
        letterSpacing = 2.3.sp,
    ),
    displayMedium = TextStyle(
        fontFamily = AppFont.ProximaNovaCond,
        fontWeight = FontWeight.Bold,
        fontStyle = FontStyle.Italic,
        fontSize = 14.sp,
        lineHeight = 16.sp,
        letterSpacing = 1.1.sp,
    ),
    displaySmall = TextStyle(
        fontFamily = AppFont.ProximaNovaCond,
        fontWeight = FontWeight.Bold,
        fontSize = 8.sp,
        lineHeight = 10.sp,
        letterSpacing = 1.0f.sp,
    ),
    labelLarge = TextStyle(
        fontFamily = AppFont.ProximaNova,
        fontWeight = FontWeight.Bold,
        fontSize = 10.sp,
        lineHeight = 12.sp
    ),
    labelMedium = TextStyle(
        fontFamily = AppFont.ProximaNova,
        fontWeight = FontWeight.Medium,
        fontSize = 10.sp,
        lineHeight = 12.sp
    ),
    labelSmall = TextStyle(
        fontFamily = AppFont.ProximaNovaCond,
        fontWeight = FontWeight.Medium,
        fontSize = 10.sp,
        lineHeight = 12.sp,
        letterSpacing = 1.0f.sp,
    ),
)

val labelMediumAnnotated:TextStyle = Typography.labelMedium.copy(
    textDecoration = TextDecoration.Underline,
    fontWeight = FontWeight.Bold
)

val bodyLargeAnnotated:TextStyle = Typography.bodyLarge.copy(
    textDecoration = TextDecoration.Underline,
    fontWeight = FontWeight.Bold
)

//Typography Display Function - Paste into preview and call
/*
@Composable
fun TypographyTestScreen()
{
    Column(
        modifier = Modifier.background(Color.White)
    )
    {
        Text(
            text = "Body Large",
            style = Typography.bodyLarge
        )
        Text(
            text = "Body Medium",
            style = Typography.bodyMedium
        )
        Text(
            text = "Body Small",
            style = Typography.bodySmall
        )
        Text(
            text = "Display Large",
            style = Typography.displayLarge
        )
        Text(
            text = "Display Medium",
            style = Typography.displayMedium
        )
        Text(
            text = "Display Small",
            style = Typography.displaySmall
        )
        Text(
            text = "Headline Large",
            style = Typography.headlineLarge
        )
        Text(
            text = "Headline Medium",
            style = Typography.headlineMedium
        )
        Text(
            text = "Headline Small",
            style = Typography.headlineSmall
        )
        Text(
            text = "Label Large",
            style = Typography.labelLarge
        )
        Text(
            text = "Label Medium",
            style = Typography.labelMedium
        )
        Text(
            text = "Label Small",
            style = Typography.labelSmall
        )
        Text(
            text = "Title Large",
            style = Typography.titleLarge
        )
        Text(
            text = "Title Medium",
            style = Typography.titleMedium
        )
        Text(
            text = "Title Small",
            style = Typography.titleSmall
        )
    }
}
 */