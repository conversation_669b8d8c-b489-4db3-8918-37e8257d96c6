package com.gametaco.app_android_fd.manager

import com.amplitude.experiment.Experiment
import com.amplitude.experiment.ExperimentClient
import com.amplitude.experiment.ExperimentConfig
import com.amplitude.experiment.ExperimentUser
import com.amplitude.experiment.Variant
import com.gametaco.app_android_fd.MainApplication
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.manager.analytics.OnAmplitudeUserChangedEvent
import com.gametaco.app_android_fd.utils.CoroutineScopes
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.viewmodel.GoalsViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.withContext
import okhttp3.internal.wait
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException


open class ExperimentBase(var flagKey: String) {
    var cachedVariant: Variant? = null

    fun trackExposure() {
        if (cachedVariant != null) {
            ExperimentManager.instance.trackExposure(cachedVariant, flagKey)
        }

    }

    fun isAvailable() : Boolean {
        return cachedVariant?.value.isNullOrEmpty() == false
    }
}

class ExperimentBool(flagKey: String, var trueVariantName : String, var falseVariantName : String) : ExperimentBase(flagKey) {

    init{
        cachedVariant = ExperimentManager.instance.getVariant(flagKey)
    }

    fun isActive() : Boolean {

        if (cachedVariant?.value == trueVariantName) {
            return true
        }

        if (cachedVariant?.value == falseVariantName) {
            return false
        }

        return false
    }



}


class ExperimentManager(private val coroutineScopes: CoroutineScopes,
                        private val analyticsManager: AnalyticsManager) {
    companion object {
        val instance: ExperimentManager get() = resolve()
        const val TAG = "ExperimentManager"
    }

    var experimentClient : ExperimentClient? = null
//    var hideGuestModeButton : ExperimentBool? = null
    var turbosMode : ExperimentBool? = null
    var videoReplaysUI : ExperimentBool? = null
    var dailyRewardExperiment : ExperimentBool? = null


    private val _experimentsReady = MutableStateFlow(false)
    val experimentsReady = _experimentsReady.asStateFlow()

    var experimentUser : ExperimentUser? = null

    private val _isDailyRewardEnabled = MutableStateFlow(false)
    val isDailyRewardEnabled = _isDailyRewardEnabled.asStateFlow()

    private val _isReplayEnabled = MutableStateFlow(false)
    val isReplayEnabled = _isReplayEnabled.asStateFlow()

    fun initialize(){
        EventBus.getDefault().register(this);
        coroutineScopes.io.launch {
            initializeExperiment()
        }
    }

    suspend fun initializeExperiment() {

        val experimentConfig = ExperimentConfig.builder()
//            .debug(true)
            .automaticExposureTracking(false)
//            .automaticFetchOnAmplitudeIdentityChange(true)
//            .fetchOnStart(true)
            .build()
        try {


            var currentSessionId : Long? = null

            // Poll until session id available
            while (true) {
                currentSessionId = analyticsManager.amplitude?.sessionId

                if (currentSessionId != null && currentSessionId != -1L) {
                    break
                }
                delay(100) // Wait 100ms before checking again
            }

            experimentClient = Experiment.initialize(application = MainApplication.instance,
                apiKey = AppEnv.current.amplitude_experiments_key,
                config = experimentConfig)

            refreshExperimentUser()

        } catch (e: Exception) {
            _experimentsReady.value = true
            e.printStackTrace()
        }
    }

    private val refreshMutex = Mutex()
    @Volatile private var refreshInProgress: Boolean = false
    suspend fun refreshExperimentUser() {
        // prevents queuing
        if (!refreshMutex.tryLock()) {
            return
        }
        try {
            if (refreshInProgress) return  // double-check inside lock

            refreshInProgress = true

            Logger.d("info","experiments_key" + AppEnv.current.amplitude_experiments_key)
            Logger.d("info","userId" + analyticsManager.amplitude?.userId)
            experimentUser = ExperimentUser.builder()
                .userId(analyticsManager.amplitude?.userId)
                .deviceId(analyticsManager.amplitude?.deviceId)
                .build()

            //call fetch; fetches flags
            experimentClient?.fetch(experimentUser)?.wait()

//            Logger.d("done?" + experimentClient)
            //initialise experiment variants
//            hideGuestModeButton = ExperimentBool("hide-guest-mode-button", "guest-button-hidden", "guest-button-visible")
//            turbosMode = ExperimentBool("turbos-experiment", "turbos-mode-visible", "turbos-mode-hidden")
            videoReplaysUI = ExperimentBool("video-replays-ui", "on", "")
            dailyRewardExperiment = ExperimentBool("daily-reward", "daily-reward", "no-daily-reward")

            Logger.d("videoReplaysUI:" + videoReplaysUI?.cachedVariant)
            Logger.d("dailyRewardExperiment:" + dailyRewardExperiment?.cachedVariant)
            Logger.d("dailyRewardExperiment value:" + dailyRewardExperiment?.cachedVariant?.value)
            if(dailyRewardExperiment?.isAvailable() == true ){
                _isDailyRewardEnabled.value = dailyRewardExperiment?.cachedVariant?.value != dailyRewardExperiment?.falseVariantName
                Logger.d("ExperimentManager","isDailyRewardEnabled:" + _isDailyRewardEnabled.value )
                if(dailyRewardExperiment?.cachedVariant?.value == dailyRewardExperiment?.falseVariantName){
                    // we got the "hidden" variant. Mark as disabled, but still track exposure
                    Logger.d("ExperimentManager - tracking exposure for hidden daily reward variant")
                    dailyRewardExperiment?.trackExposure()
                }
            }else{
                _isDailyRewardEnabled.value = false
            }

            if(videoReplaysUI?.isAvailable() == true && videoReplaysUI?.isActive() == true){
                _isReplayEnabled.value = true
            }else{
                _isReplayEnabled.value = false
            }

            _experimentsReady.value = true
        } catch (e: Exception) {
            e.printStackTrace()
            _experimentsReady.value = true
        } finally {
            refreshInProgress = false
            refreshMutex.unlock()
        }
    }

    fun getVariant( variantName : String?) : Variant? {

        if(experimentClient == null){
            return null
        }

        if(variantName == null || variantName.isEmpty()) {
            return null
        }
        try {
            return experimentClient?.variant(variantName)
        }catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }


    fun trackExposure(variant : Variant?, flagKey : String?){

        if(flagKey == null || variant == null)
            return

        val properties = mutableMapOf<String, Any>()

        properties["flag_key"] = flagKey
        properties["variant"] = variant.value ?: ""
        properties["experiment_key"] = variant.expKey ?: ""
        AnalyticsManager.instance.logEvent(
            AnalyticsEvent(analyticsEvent = "\$exposure",
            properties = properties)
        )
    }

    suspend fun <T> Future<T>.wait(timeoutMs: Long = 60000): T? {

        return withContext(Dispatchers.IO) {
            try {
                get(timeoutMs, TimeUnit.MILLISECONDS)
            } catch (e: TimeoutException) {
                throw(e)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun OnAmplitudeUserChangedCallback(event : OnAmplitudeUserChangedEvent?){
        coroutineScopes.io.launch {
            refreshExperimentUser()
        }
    }

    fun OnShutdown(){

        Logger.d(TAG, "OnShutdownCallback")

        //no session data, track guest mode button experiment exposure
//        if(!FDManager.instance.hasSessionData)
//        {
//            Logger.d(TAG, "OnShutdownCallback hideGuestModeButton?.trackExposure()")
//            hideGuestModeButton?.trackExposure()
//        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSessionChangedEventHandler(event: OnSessionChangedEvent) {
        Logger.d(TAG, "onSessionChangedEventHandler")

        if(!event.loggedIn) {
            _experimentsReady.value = false
        }
    }

}