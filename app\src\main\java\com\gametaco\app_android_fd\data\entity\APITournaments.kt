package com.gametaco.app_android_fd.data.entity

import com.gametaco.app_android_fd.data.entity.API400ErrorCode.values


data class APITournamentsRequest_GameId (
    val game_id: String
)

data class APITournamentsRequest_TournamentIds (
    val tournament_ids: String
)

data class APITournamentsResponse(
    val results: List<APITournament>,
    val count: Int
)

data class APITournament(
    val id: String,
    val prizes: List<Prize>,
    val name: String?,
    val entry_fee: String?,
    var brand_description: String?,
    var brand: String?,
    val prize: String?,
    val inaccessibility_criteria: APITournamentInaccessibilityCriteria?,
    val game_mode: APITournamentGameMode?,
    val game_id: String?,
    val game_name: String?,
    var game_display_name: String?,
    var game_mode_name:String?,
    val maximum_slots: Int?,
    val ends_at: String?,
    val entry_id:String?,
    val is_for_staff:Boolean?,
    val is_turbo:Boolean?,
    var is_free_entry:Boolean?,
    var free_entry_ticket_without_expiry_number:Int = 0,
    var free_tickets:List<APIGameTournamentEntryTicketsInfo>? = null,
    var free_entry_ticket_expires_at:String? = null,
    var group_type: APITournamentGroupType?,
    val maximum_entries_per_player:Int?,
    val is_for_ticket_holders_only: Boolean = false,
){
    val entryFee: EntryFee by lazy { EntryFee(entry_fee) }

    val showInTournamentList: Boolean
        get() = !is_for_ticket_holders_only || (is_for_ticket_holders_only && is_free_entry == true)

    val brandLabel: String
        get() {
            var totalPrize = 0f
            for (prize in prizes){
                totalPrize += prize.prize_amount.toFloat() * (prize.end - prize.start + 1)
            }
            if(maximum_slots == 2){
                if(totalPrize < 20){
                    return "HEAD TO HEAD!"
                }else{
                    return "2 PLAYER PREMIUM!"
                }
            }else if(maximum_slots != null && maximum_slots > 2){
                if(totalPrize < 20){
                    return "MULTIPLAYER!"
                }else{
                    return "MULTIPLAYER SUPER REWARDS!"
                }
            }
            else if(game_mode_name?.uppercase()?.contains("DAILY") == true){
//            else if(group_type?.name == APITournamentGroupType.FEATURED.name){
                return "WIN A PIECE OF THE DAILY PRIZE!"
            }
            else if(group_type?.name == APITournamentGroupType.REPEAT_AND_BEAT.name){
                val modeRegex = Regex("<([^>]+)>")
                val modeMatch = modeRegex.find(this.name.orEmpty())
                val mode = modeMatch?.groupValues?.getOrNull(1)

                val entries = this.maximum_entries_per_player
                val entriesStr = entries?.toString() ?: "N/A"
                val timeWord = if (entries == 1) "TIME" else "TIMES"

                return if (mode != null) {
                    "${mode.uppercase()}: ENTER UP TO $entriesStr $timeWord!"
                } else {
                    "ENTER UP TO $entriesStr $timeWord!"
                }
            }
            else if(maximum_slots == null){
                return "UNLIMITED!"
            }
            else{
                return "UNDEFINED!"
            }
        }
}

enum class GAME_NAME(val value: String){
    ATARI_BREAKOUT_BLITZ("Atari Breakout Blitz"),
    BLOCK_TRAIL("Block Trail"),
    BOGGLE("Boggle"),
    BUENA_MATCH("Buena Match"),
    CUE_MASTERS("Cue Masters"),
    DEEP_SEA_SEARCH("Deep Sea Search"),
    FAIRWAY_FRENZY("Fairway Frenzy"),
    FIELD_GOAL_FACEOFF("Field Goal Faceoff"),
    FREECELL_SOLITAIRE("Freecell Solitaire"),
    GALAXY_MATCH("Galaxy Match"),
    GRAND_SLAM_SUPERSTAR("Grand Slam Superstar"),
    GRONK_SPIKE_CORNHOLE("Gronk Spike Cornhole"),
    PRO_POPPER("Pro Popper"),
    PUZZLE_PYRAMID("Puzzle Pyramid"),
    SCRABBLE_CUBES("SCRABBLE Cubes"),
    SOLITAIRE_SPRINT("Solitaire Sprint"),
    SWISH_SHOWDOWN("Swish Showdown"),
    WHEEL_OF_FORTUNE("Wheel of Fortune!"),
}
data class APITournamentPrize(
    val start: Int,
    val end: Int,
    val prize_amount: String
)

data class APITournamentInaccessibilityCriteria(
    val locationIneligibility: APITournamentIneligibilityDetail?,
    val played_games_ineligibility: APITournamentIneligibilityDetail?,
    val maximum_entries_ineligibility: APITournamentIneligibilityDetail?
)

data class APITournamentIneligibilityDetail(
    val entries: Int?,
    val played_games_count: Int?,
    val required_games_count: Int?,
    val description: String
)

data class APITournamentGameMode(
    val id: String,
    val name: String?,
    val description: String?,
    val icon: String?,
    val is_default: Boolean,
    val short_name: String?,
    val display_order: Int
)

data class APITournamentsReEntryRequest(
    val geocomply_token: String
)

data class APITournamentsJoinRequest(
    val tournament_id: String,
    val geocomply_token: String
)

data class APIGameOptions(
    val join: Map<String, String>,
    val start: Map<String, String>?
)

data class APITournamentsJoinResponse(
    val id: String,
    val user_id: String,
    val tournament_instance_id: String,
    val game_name: String,
    val game_options: APIGameOptions,
    val global_game_options: Map<String, Any>?,

)



data class APITournamentsJoinRequestGuest(
    val tournament_id: String
)


data class APITournamentsJoinResponseGuest(
    val id: String,
    val guest_user_id: String,
    val game_name: String,
    val game_options: APIGameOptions,
    val global_game_options: Map<String, Any>?
)



enum class APITournamentState{
    eligibility,
    fundsIneligibility,
    locationIneligibility,
    maximumEntriesIneligibility,
    playedGamesIneligibility
}



enum class API400ErrorCode(val value: String) {

    NONE("NONE"),
    TOURNAMENT_JOIN_LOCATION_CANNOT_BE_DETERMINED("TOURNAMENT_JOIN_LOCATION_CANNOT_BE_DETERMINED"),
    TOURNAMENT_JOIN_JWT_SIGNATURE_NOT_VERIFIED("TOURNAMENT_JOIN_JWT_SIGNATURE_NOT_VERIFIED"),
    TOURNAMENT_JOIN_GEOLOCATION_PACKET_EXPIRED("TOURNAMENT_JOIN_GEOLOCATION_PACKET_EXPIRED"),
    TOURNAMENT_JOIN_GEOLOCATION_CHECKS_FAILED("TOURNAMENT_JOIN_GEOLOCATION_CHECKS_FAILED"),
    TOURNAMENT_JOIN_GEOLOCATION_IP_ADDRESS_MISMATCH("TOURNAMENT_JOIN_GEOLOCATION_IP_ADDRESS_MISMATCH"),
    TOURNAMENT_JOIN_GEOLOCATION_SESSION_ID_MISMATCH("TOURNAMENT_JOIN_GEOLOCATION_SESSION_ID_MISMATCH"),
    TOURNAMENT_JOIN_GEOLOCATION_STATE_MISMATCH("TOURNAMENT_JOIN_GEOLOCATION_STATE_MISMATCH"),
    TOURNAMENT_JOIN_INELIGIBLE_STATE("TOURNAMENT_JOIN_INELIGIBLE_STATE"),
    TOURNAMENT_JOIN_PAID_CONTESTS_NOT_ALLOWED("TOURNAMENT_JOIN_PAID_CONTESTS_NOT_ALLOWED"),
    TOURNAMENT_JOIN_GAME_NOT_ALLOWED("TOURNAMENT_JOIN_GAME_NOT_ALLOWED"),
    TOURNAMENT_JOIN_LOCATION_SERVICE_FAILED("TOURNAMENT_JOIN_LOCATION_SERVICE_FAILED"),
    TOURNAMENT_JOIN_IDENTITY_NOT_VERIFIED("TOURNAMENT_JOIN_IDENTITY_NOT_VERIFIED"),
    TOURNAMENT_JOIN_INSUFFICIENT_FUNDS("TOURNAMENT_JOIN_INSUFFICIENT_FUNDS"),
    TOURNAMENT_JOIN_MAX_UNSTARTED_GAMES("TOURNAMENT_JOIN_MAX_UNSTARTED_GAMES"),
    TOURNAMENT_JOIN_MAX_OPEN_INSTANCES("TOURNAMENT_JOIN_MAX_OPEN_INSTANCES"),
    TOURNAMENT_JOIN_MAX_OPEN_INSTANCES_FOR_USER("TOURNAMENT_JOIN_MAX_OPEN_INSTANCES_FOR_USER"),
    TOURNAMENT_JOIN_TOURNAMENT_EXPIRED("TOURNAMENT_JOIN_TOURNAMENT_EXPIRED"),
    TOURNAMENT_JOIN_TOURNAMENT_NOT_FOUND("TOURNAMENT_JOIN_TOURNAMENT_NOT_FOUND"),
    REWARD_CLAIM_AGE_UNDERAGE("REWARD_CLAIM_AGE_UNDERAGE"),
    UNKNOWN_FANDUEL_ERROR_OCCURRED("UNKNOWN_FANDUEL_ERROR_OCCURRED"),
    UNKNOWN_ERROR("UNKNOWN_ERROR");

    companion object {
        // Function to convert JSON string value to enum
        fun fromValue(value: String) = values().firstOrNull { it.value == value }
    }

    val errorMessage: String
        get() = when (this) {

            NONE -> "none"
            TOURNAMENT_JOIN_LOCATION_CANNOT_BE_DETERMINED -> "Tournament Joint Location cannot be determined"
            TOURNAMENT_JOIN_JWT_SIGNATURE_NOT_VERIFIED -> "JWT Signature not verified"
            TOURNAMENT_JOIN_GEOLOCATION_PACKET_EXPIRED -> "Geolocation packet expired"
            TOURNAMENT_JOIN_GEOLOCATION_CHECKS_FAILED -> "Geolocation checks failed"
            TOURNAMENT_JOIN_GEOLOCATION_IP_ADDRESS_MISMATCH -> "IP Address mismatch"
            TOURNAMENT_JOIN_GEOLOCATION_SESSION_ID_MISMATCH -> "Session ID Mismatch"
            TOURNAMENT_JOIN_GEOLOCATION_STATE_MISMATCH -> "State mismatch"
            TOURNAMENT_JOIN_INELIGIBLE_STATE -> "Cannot play this game in your state"
            TOURNAMENT_JOIN_PAID_CONTESTS_NOT_ALLOWED -> "You are not allowed to join paid contests"
            TOURNAMENT_JOIN_GAME_NOT_ALLOWED -> "You are not allowed to join this game"
            TOURNAMENT_JOIN_LOCATION_SERVICE_FAILED -> "Failed to resolve your location"
            TOURNAMENT_JOIN_IDENTITY_NOT_VERIFIED -> "Failed to verify your identity"
            TOURNAMENT_JOIN_INSUFFICIENT_FUNDS -> "Insufficient funds to join this tournament"
            TOURNAMENT_JOIN_MAX_UNSTARTED_GAMES -> "You have joined but not started too many games"
            TOURNAMENT_JOIN_MAX_OPEN_INSTANCES -> "Too many open tournaments"
            TOURNAMENT_JOIN_MAX_OPEN_INSTANCES_FOR_USER -> "Too many open tournaments for this user"
            TOURNAMENT_JOIN_TOURNAMENT_EXPIRED -> "Tournament expired"
            TOURNAMENT_JOIN_TOURNAMENT_NOT_FOUND -> "Tournament not found"
            REWARD_CLAIM_AGE_UNDERAGE -> "You cannot claim this reward due to being under 21"
            UNKNOWN_FANDUEL_ERROR_OCCURRED -> "Unknown error occurred on Fanduel"
            UNKNOWN_ERROR -> "Unknown error occurred"

        }
}


data class API400ErrorGeneralError(
    val message : String?,
    val code : API400ErrorCode?
)


data class API400Error(
    val field_errors : Map<String, List<String>>?,
    val general_error : API400ErrorGeneralError?
)

data class APIGameTournamentsBrand(
    val name : String,
    val description: String,
    val tournaments: List<APITournament>
)

data class APIGameTournamentsGroup(
    val name : String,
    val description: String?,
    val short_name: String?,
    val brands : List<APIGameTournamentsBrand>,

)
data class APIGameTournamentsGroupV2(
    val name : String,
    val description: String?,
    val short_name: String?,
    val group_type: APITournamentGroupType,
    val order:Int,
    val tournaments : List<APITournament>,
) {
    /**
     * Create a unique key for the group. Required as a workaround for the case where multiple items
     * in a [APIGameTournamentsGroupV2] list may have the same [APIGameTournamentsGroupV2.name], but
     * a different [APIGameTournamentsGroupV2.group_type]. See FX-3203.
     */
    val groupKey: String
        get() {
            return group_type.name + ":" + name + ":" + hashCode()
        }
}

enum class APITournamentGroupType(val value:String){
    STANDARD("STANDARD"),
    FEATURED("FEATURED"),
    REPEAT_AND_BEAT("REPEAT_AND_BEAT"),
    TURBOS("TURBOS")
}

data class APIGameTournamentsResponse(
    val name : String,
    val display_name : String,
    var groups : List<APIGameTournamentsGroup>,
    val practice_tournament_id : String?
)

data class APIGameTournamentsResponseV2(
    val name : String,
    val display_name : String,
    var groups : List<APIGameTournamentsGroupV2>,
    val practice_tournament_id : String?,
    val tournament_entry_tickets : List<String>,
    val tournament_entry_tickets_info : List<APIGameTournamentEntryTicketsInfo>?
)

data class APIGameTournamentEntryTicketsInfo(
    val id:String,
    val tournament_id: String,
    val expires_at:String? = null,
)
data class APITournamentRegisterReplayURLRequest(
    val tournament_instance_entry_id : String
)


data class APITournamentReplayURLResponse(
    val presigned_url : String?
)


