package com.gametaco.app_android_fd.data.navigation

import com.gametaco.app_android_fd.MainApplication.Companion.context
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.models.NavigationDataModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import resources.R

class NavigationData {

    private val _stateData: MutableStateFlow<NavigationStateData> = MutableStateFlow(
        NavigationStateData(
            accountValueHidden = PreferencesManager.instance.getIsBalanceHidden()
        )
    )
    val stateData = _stateData.asStateFlow()

    fun setNotificationCountPlay(newValue: Int?){
        val newVal = stateData.value.copy(notificationCountPlay = newValue)
        _stateData.value = newVal
    }
    fun setNotificationCountGoals(newValue: Int?){
        val newVal = stateData.value.copy(notificationCountGoals = newValue)
        _stateData.value = newVal
    }
    fun setNotificationCountScores(newValue: Int?){
        val newVal = stateData.value.copy(notificationCountScores = newValue)
        _stateData.value = newVal
    }
    fun setAccountValueHidden(newValue: Boolean){
        val newVal = stateData.value.copy(accountValueHidden = newValue)
        _stateData.value = newVal
    }

    fun getNavElements(): List<NavigationDataModel> {
        return listOf(
            NavigationDataModel(
                title = context.getString(R.string.play),
                route = Routes.GAMES_SCREEN,
                icon = R.drawable.ic_play_app,
            ),
            NavigationDataModel(
                title = context.getString(R.string.rewards),
                route = Routes.GOALS_SCREEN,
                icon = R.drawable.ic_rewards_app,
            ),
            NavigationDataModel(
                title = context.getString(R.string.scores),
                route = Routes.SCORES_SCREEN,
                icon = R.drawable.ic_scores_app,
            ),
            NavigationDataModel(
                title = context.getString(R.string.account),
                route = Routes.ACCOUNT_SCREEN,
            )
        )
    }

    companion object {
        val instance: NavigationData by lazy { NavigationData() }
        const val TAG = "NavigationData"
    }
}
