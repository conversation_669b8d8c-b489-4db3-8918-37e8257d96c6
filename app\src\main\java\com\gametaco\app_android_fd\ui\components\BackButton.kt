package com.gametaco.app_android_fd.ui.components

import IcBack
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

@Composable
fun BackButton(
    modifier: Modifier,
    onBack:() -> Unit
) {
    Box(
        modifier = modifier
            .size(48.dp)
            .clickable(onClick = onBack),
        ) {
        Icon(
            imageVector = IcBack,
            tint = Color(0xFF005FC8),
            contentDescription = null,
            modifier = Modifier
                .size(20.dp)
                .align(Alignment.Center),
        )
    }
}