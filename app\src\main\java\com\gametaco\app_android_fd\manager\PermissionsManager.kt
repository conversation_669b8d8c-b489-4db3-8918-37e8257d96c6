package com.gametaco.app_android_fd.manager

import android.Manifest
import android.app.Activity
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.log.Logger
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class PermissionsManager(
    private val activityManager: ActivityManager,
) {

    companion object {
        val instance: PermissionsManager
            get() = resolve()
        const val TAG = "PermissionsManager"
    }

    private val activity: Activity
        get() = activityManager.activity

    init {
        EventBus.getDefault().register(this);
    }

    private val LOCATION_PERMISSION_REQ_CODE = 56848458
    private val NOTIFICATION_PERMISSION_REQ_CODE = 26998515

    private var locationPermissionCallback: ((Boolean) -> Unit)? = null

    fun checkLocationPermissions(showRequestDialog: Boolean, callback: (Boolean) -> Unit) {

        locationPermissionCallback = callback

        val hasFineLocationPermission = ContextCompat.checkSelfPermission(
            activity,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        val hasCoarseLocationPermission = ContextCompat.checkSelfPermission(
            activity,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        if (hasFineLocationPermission && hasCoarseLocationPermission) {
            Logger.d(TAG, "Location permissions already granted")

            // Permissions are already granted, call the callback immediately
            locationPermissionCallback?.invoke(true)
        } else {

            if(showRequestDialog) {
                // Request permissions
                requestLocationPermission()
            }
            else
            {
                locationPermissionCallback?.invoke(false)
            }
        }

    }

    private fun requestLocationPermission(){
        Logger.d(TAG, "requestLocationPermission")

        // Request both coarse and fine location permissions
        ActivityCompat.requestPermissions(
            activity,
            arrayOf(Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION),
            LOCATION_PERMISSION_REQ_CODE
        )
    }
    fun requestNotificationPermission(){
        Logger.i("requestNotificationPermission")
        ActivityCompat.requestPermissions(
            activity,
            arrayOf(Manifest.permission.POST_NOTIFICATIONS),
            NOTIFICATION_PERMISSION_REQ_CODE
        )
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRequestPermissionsResult(event: RequestPermissionsResultEvent){
        Logger.i("onRequestPermissionsResult: ${event}")
        if (event.requestCode == LOCATION_PERMISSION_REQ_CODE) {
            val allPermissionsGranted = event.grantResults.all { it == PackageManager.PERMISSION_GRANTED }

            locationPermissionCallback?.invoke(allPermissionsGranted)
        }
    }
}

data class RequestPermissionsResultEvent(
    var requestCode: Int,
    var permissions: Array<out String>,
    var grantResults: IntArray
)