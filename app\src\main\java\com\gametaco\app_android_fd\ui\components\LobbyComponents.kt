package com.gametaco.app_android_fd.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.gametaco.app_android_fd.data.entity.APITournament
import com.gametaco.app_android_fd.data.entity.APIWalletResponse
import com.gametaco.app_android_fd.data.entity.BannerEnum
import com.gametaco.app_android_fd.data.entity.GCGameData
import com.gametaco.app_android_fd.data.entity.GCRowItem
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.ui.modifiers.fadingEdge
import com.gametaco.app_android_fd.ui.theme.AppFont
import com.gametaco.app_android_fd.utils.toDollarString
import com.gametaco.utilities.STR
import resources.R

@Composable
fun GameTile(
    modifier: Modifier = Modifier,
    iconUrl: String,
    iconDescription: String? = null,
    clipShape: Shape = RoundedCornerShape(8.dp),
    showShadow: Boolean = true,
    onClick: () -> Unit
){
    Button(
        onClick = onClick,
        contentPadding = PaddingValues(0.dp),
        shape = clipShape,
        colors = ButtonDefaults.buttonColors(
            containerColor = Color.Transparent,
        ),
        modifier = Modifier
            .then(modifier)
            .wrapContentHeight()
            .let {
                if (showShadow){
                    it.shadow(
                        elevation = 8.dp,
                        ambientColor = Color.White,
                        spotColor = Color.Black,
                        shape = clipShape
                    )
                } else it
            }
    ) {
        AsyncImage(
            model = iconUrl,
            contentDescription = iconDescription,
            modifier = Modifier
                .fillMaxSize(),
            contentScale = ContentScale.FillBounds,
        )
    }
}

@Composable
fun TaggedTile(
    modifier: Modifier = Modifier,
    message: String? = null,
    tagOffsetX: Dp? = null,
    tagOffsetY: Dp? = null,
    content: @Composable BoxScope.()->Unit
){
    Box(
        modifier = modifier
    ) {
        content()

        if (message != null){
            val backgroundBlue = Color(0xFF0070EB)
            val borderBlue = Color(0xFF64AEFF)
            val backgroundOrange = Color(0xFFF87A1E)
            val borderOrange = Color(0xFFFFB77E)
            val backgroundRed = Color(0xFFC7002B)
            val borderRed = Color(0xFFE96775)
            val backgroundPurple = Color(0xFF7401B6)
            val borderPurple = Color(0xFFB165FD)

            var messageText = message
            var icon: Int? = null
            var backgroundColor = backgroundBlue
            var borderColor = borderBlue

            when (message){
                // Blue
                BannerEnum.REWARD -> { messageText = STR(R.string.reward_caps) }
                BannerEnum.BEAT_THE_SCORE -> { messageText = STR(R.string.beat_the_score_caps) }
                BannerEnum.WINNERS_CIRCLE -> { messageText = STR(R.string.winners_circle_caps) }
                BannerEnum.PROMOTION -> { messageText = STR(R.string.promotion_caps) }
                BannerEnum.SPECIAL_OFFER -> { messageText = STR(R.string.special_offer_caps) }
                BannerEnum.DAILY_SWING -> { messageText = STR(R.string.daily_swing_caps) }
                BannerEnum.DAILY_KICK -> { messageText = STR(R.string.daily_kick_caps) }
                BannerEnum.LIMITED_TIME -> { messageText = STR(R.string.limited_time_caps) }
                BannerEnum.LIMITED_TIME_REWARD -> { messageText = STR(R.string.limited_time_reward_caps) }
                BannerEnum.DAILY_PUTT -> { messageText = STR(R.string.daily_putt_caps) }
                BannerEnum.HAPPY_HOUR -> { messageText = STR(R.string.happy_hour_caps) }
                BannerEnum.FREE_CASH -> { messageText = STR(R.string.free_cash_caps) }
                BannerEnum.DAILY_SHOT -> { messageText = STR(R.string.daily_shot_caps) }
                BannerEnum.DAILY_POP -> { messageText = STR(R.string.daily_pop_caps) }

                // Orange
                BannerEnum.BIGGER_PRIZES -> {
                    messageText = STR(R.string.bigger_prizes_caps)
                    backgroundColor = backgroundOrange
                    borderColor = borderOrange
                }
                BannerEnum.TOURNAMENT -> {
                    messageText = STR(R.string.tournament_caps)
                    backgroundColor = backgroundOrange
                    borderColor = borderOrange
                }
                BannerEnum.MULTIPLAYER -> {
                    messageText = STR(R.string.daily_shot_caps)
//                    backgroundColor = backgroundOrange
//                    borderColor = borderOrange
                }
                BannerEnum.NEW_GAME -> {
                    messageText = STR(R.string.new_game_caps)
                    backgroundColor = backgroundOrange
                    borderColor = borderOrange
                }
                BannerEnum.NEW_GAME_MODE -> {
                    messageText = STR(R.string.new_game_mode_caps)
                    backgroundColor = backgroundOrange
                    borderColor = borderOrange
                }

                // Red
                BannerEnum.HOT_STREAK -> {
                    messageText = STR(R.string.hot_streak_caps)
                    icon = R.drawable.ic_fire_emoji
                    backgroundColor = backgroundRed
                    borderColor = borderRed
                }
            }

            TagComponent(
                offsetX = tagOffsetX,
                offsetY = tagOffsetY,
                backgroundColor = backgroundColor,
                borderColor = borderColor,
                height = 20.dp,
                contentPadding = PaddingValues(start = if(icon == null) {10.dp} else {7.dp} ,end = 10.dp,top = 0.dp, bottom = 6.dp)
            ){
                TextCentered(
                    text = messageText.uppercase(),
                    icon = icon,
                    fontFamily = AppFont.Shentox,
                    fontSize = 9.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimary,
                    modifier = Modifier
                        .padding(top = 2.dp)
                )
            }
        }
    }
}

@Composable
fun BonusCashBanner(value: String){
    Row (
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth()
    ) {
        Image(
            modifier = Modifier.size(32.dp),
            painter = painterResource(R.drawable.ic_cash_single),
            contentDescription = STR(R.string.bonus_cash_icon)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            AutosizeText(
                text = STR(R.string.bonus_cash_available_auto_applied, value.toDollarString()),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.secondary,
                maxLines = 1,
            )
            AutosizeText(
                text = STR(R.string.we_automatically_use_this_first_when_you_join_a_contest),
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.onSurface,
                maxLines = 1,
            )
        }
    }
}
@Composable
fun ReferFriendsBanner(){
    Row (
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth()
    ) {
        Image(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(4.dp))
                .shadow(4.dp,RoundedCornerShape(4.dp))
                .clickable {
                    FDManager.instance.showWebviewFD(page = null, sourceURL = "/account/referrals", title = "Earn Cash: Refer Friends")
                },
            contentScale = ContentScale.FillWidth,
            painter = painterResource(R.drawable.img_raf_banner),
            contentDescription = null
        )
    }
}
@Composable
fun FeaturedContest(
    contestType: String,
    gameName: String,
    tournament: APITournament,
    wallet:APIWalletResponse,
    gameBackground: String?,
    gameIcon: String?,
    onInfoClick:() ->Unit,
    gameId:String?
){
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 8.dp, bottom = 4.dp)
            .graphicsLayer { clip = true }
            .clip(RoundedCornerShape(4.dp))
    ){
        AsyncImage(model = gameBackground,
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
            modifier = Modifier.fillMaxSize()

        )
        Column (
            modifier = Modifier
                .fillMaxSize()
                .padding(6.dp, 6.dp, 6.dp, 6.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier
                    .padding(12.dp, 0.dp, 0.dp, 0.dp)
                    .fillMaxWidth()
                    .height(44.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                //Title
                Column (
                    modifier = Modifier.padding(0.dp, 8.dp, 0.dp, 0.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .height(16.dp)
                    ) {
                        Text(
                            text = contestType,
                            style = MaterialTheme.typography.titleSmall.copy(fontSize = 16.sp),
                            color = MaterialTheme.colorScheme.onPrimary,
                            modifier = Modifier.align(Alignment.Center).offset(y = -4.dp)
                        )
                    }
                    Spacer(modifier = Modifier.height(2.dp))
                    Box(modifier = Modifier
                        .height(16.dp)
                        .background(
                            MaterialTheme.colorScheme.surface,
                            RoundedCornerShape(2.dp)
                        )
                    ){
                        Text(
                            text = gameName.uppercase(),
                            style = MaterialTheme.typography.headlineSmall,
                            modifier = Modifier
                                .padding(4.dp, 0.dp)
                                .align(Alignment.CenterStart),
                            letterSpacing = 1.sp
                        )
                    }
                }
                AsyncImage(model = gameIcon,
                    contentDescription = null,
                    modifier = Modifier
                        .size(40.dp)
                        .border(1.dp, Color.Gray, RoundedCornerShape(4.dp))
                        .clip(RoundedCornerShape(4.dp))
                )
            }
            //Details Box
            Spacer(modifier = Modifier.height(4.dp))
            Box(
                modifier = Modifier.background(
                    MaterialTheme.colorScheme.surface,
                    RoundedCornerShape(4.dp)
                )
            ) {
                ContestListItem(
                    tournament = tournament,
                    wallet = wallet,
                    onInfoButton = onInfoClick,
                    titlesOnTop = false,
                    gameId = gameId,
                    lobbySelection = "Featured Tournament"
                )
            }
        }
    }
}

@Composable
fun LobbyContestInfo(
    title: String,
    value: String
){
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = title,
            color = MaterialTheme.colorScheme.outline,
            style = MaterialTheme.typography.labelMedium
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyLarge
        )
    }
}

@Composable
fun GameTileRowSpan(
    title: String,
    gamesList: List<GCGameData>,
    onClickTile: (GCGameData) -> Unit
){
    TileGroupTitle(title = title)
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp),
        horizontalArrangement = Arrangement.spacedBy(
            12.dp,
            Alignment.Start
        )
    ) {
        for (gameData in gamesList){
            GameTile(
                modifier = Modifier
                    .weight(1f)
                    .aspectRatio(1f)
                    .shadow(8.dp),
                iconUrl = gameData.tile_image_url
            ) {
                onClickTile(gameData)
            }
        }
    }
}

@Composable
fun GameTileRowScrollable(
    title: String,
    gamesList: List<Pair<GCGameData, GCRowItem>>,
    tileSize: Dp = 72.dp,
    horizontalPadding: Dp = 16.dp,
    onClickTile: (GCGameData) -> Unit
){
    TileGroupTitle(title = title)
    LazyRow(
        modifier = Modifier
            .height(tileSize),
        contentPadding = PaddingValues(start = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(
            horizontalPadding,
            Alignment.Start
        )
    ) {
        for (gameData in gamesList) {
            item {
                TaggedTile(
                    modifier = Modifier
                        .size(tileSize),
                    message = gameData.second.banner
                ) {
                    GameTile(
                        iconUrl = gameData.first.tile_image_url,
                        showShadow = false
                    ) {
                        onClickTile(gameData.first)
                    }
                }
            }
        }

        if (gamesList.isNotEmpty()){
            item{
                Spacer(modifier = Modifier.width(12.dp))
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun GameTileGrid(
    title: String,
    subtitle: String? = null,
    maxColumns: Int,
    gamesList: List<Pair<GCGameData, GCRowItem>>,
    onClickTile: (GCGameData) -> Unit
){
    TileGroupTitle(
        title = title,
        bottomPadding = if (subtitle != null) 0.dp else 6.dp
    )
    if (subtitle != null){
        AutosizeText(
            text = subtitle,
            style = MaterialTheme.typography.headlineLarge,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Normal,
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(top = 2.dp, bottom = 12.dp),
            maxLines = 1,
        )
    }
    BoxWithConstraints(
        modifier = Modifier
            .fillMaxWidth()
    ){
        val screenWidth = this.maxWidth
        val columnSpacing = 16.dp

        // Calculate the item width (1/2 of the screen width minus left and right padding)
        val itemSize = ((screenWidth - columnSpacing)) / maxColumns.toFloat()
        val imageSize = itemSize - columnSpacing

        FlowRow(
            maxItemsInEachRow = maxColumns,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = (columnSpacing / 2) -.5.dp)
                .align(Alignment.Center)
        ) {
            for (gameData in gamesList){
                Box(
                    modifier = Modifier
                        .size(itemSize),
                ) {
                    GameTile(
                        iconUrl = gameData.first.tile_image_url,
                        modifier = Modifier
                            .size(imageSize)
                            .align(Alignment.Center),
                    ) {
                        onClickTile(gameData.first)
                    }

                    TaggedTile(
                        modifier = Modifier
                            .align(Alignment.TopStart),
                        message = gameData.second.banner
                    ) {
                    }
                }
            }

            //Fill tile row if odd number
            val rem = gamesList.size % maxColumns
            if(rem != 0)
            {
                for(i in 0 ..< maxColumns - rem)
                {
                    Box(
                        modifier = Modifier
                            .width(itemSize)
                            .aspectRatio(1.08333f)
                    )
                }
            }
        }
    }
}

@Composable
fun TileGroupTitle(
    title: String,
    topPadding: Dp = 0.dp,
    bottomPadding: Dp = 12.dp
){
    Text(
        text = title,
        style = MaterialTheme.typography.titleSmall,
        color = MaterialTheme.colorScheme.secondary,
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .padding(top = topPadding, bottom = bottomPadding)
    )
}

@Composable
fun FeaturedTournamentRow(
    tournaments:List<Pair<APITournament, GCRowItem>>,
    gamesList: Map<String,GCGameData>,
    wallet:APIWalletResponse,
    tileHeight: Dp = 126.dp,
    tileWidth: Dp = 330.dp,
    onInfoClick: (tournament:APITournament, gameData: GCGameData?) -> Unit
){
    LazyRow(
        modifier = Modifier
            .height(tileHeight),
        contentPadding = PaddingValues(start = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(
            16.dp,
            Alignment.Start
        )
    ) {
        for (tournament in tournaments) {
            item {
                val gameData = gamesList[tournament.first.game_id]

                TaggedTile(
                    message = tournament.second.banner,
                    tagOffsetX = (-8).dp,
                    tagOffsetY = (-2).dp,
                    modifier = Modifier
                        .fillMaxHeight()
                        .width(tileWidth)
                ) {
                    FeaturedContest(
                        contestType = tournament.first.game_mode?.short_name ?: tournament.first.game_mode?.name ?: STR(R.string.standard),
                        gameName = tournament.first.game_display_name ?: STR(R.string.tournament),
                        tournament = tournament.first,
                        wallet = wallet,
                        gameBackground = gameData?.tournament_background_image_url,
                        gameIcon = gameData?.tile_image_url,
                        onInfoClick = {
                            onInfoClick(tournament.first, gameData)
                        },
                        gameId = gameData?.game_id
                    )
                }
            }
        }

        if (tournaments.isNotEmpty()){ // Add trailing padding to the end of the row
            item {
                Spacer(modifier = Modifier.width(12.dp))
            }
        }
    }
}

@Composable
fun FtueBanner(isGuest: Boolean = false){
    Box (
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.primary)
    ) {
        Image(
            painter = painterResource(R.drawable.img_ftue_banner),
            alignment = Alignment.TopCenter,
            contentScale = ContentScale.FillWidth,
            contentDescription = "FTUE Banner",
            alpha = 0.25f,
            modifier = Modifier
                .fillMaxSize()
                .fadingEdge()
        )
        Column (
            modifier = Modifier
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterVertically)
        ) {
            val textStyle = TextStyle(
                fontWeight = FontWeight.W700,
                fontStyle = FontStyle.Italic,
                fontSize = 36.sp,
                lineHeight = 39.6.sp,
                letterSpacing = 2.sp,
                shadow = Shadow(
                    color =  Color(0x40000000),
                    offset = Offset(0.0f, 8.0f),
                    blurRadius = 8.0f
                )
            )
            Spacer(modifier = Modifier.weight(1f))
            if (isGuest) {
                Text(
                    text = STR(R.string.play_a_free_guest_game_caps),
                    style = textStyle,
                    color = MaterialTheme.colorScheme.onPrimary,
                    textAlign = TextAlign.Center,
                )
            } else {
                Text(
                    text = STR(R.string.play_a_free_practice_game_caps),
                    style = textStyle,
                    color = MaterialTheme.colorScheme.onPrimary,
                    textAlign = TextAlign.Center
                )
                Text(
                    text = STR(R.string.and_learn_the_ropes),
                    style = MaterialTheme.typography.titleMedium.copy(fontSize = 18.sp),
                    color = MaterialTheme.colorScheme.onPrimary,
                    textAlign = TextAlign.Center,
                )
            }
            Spacer(modifier = Modifier.weight(1f))
        }
    }
}

@Composable
fun ErrorElement(errorMessage: String, onRefresh: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxHeight()
            .padding(horizontal = 48.dp),
        verticalArrangement = Arrangement.spacedBy(
            4.dp,
            Alignment.CenterVertically
        ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = STR(R.string.lobby_error),
            color = MaterialTheme.colorScheme.secondary,
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = errorMessage,
            color = MaterialTheme.colorScheme.secondary,
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center
        )
        Spacer(Modifier.height(16.dp))
        Text(
            text = STR(R.string.lobby_refresh_try_again),
            color = MaterialTheme.colorScheme.secondary,
            style = MaterialTheme.typography.bodyMedium
        )
        Spacer(Modifier.height(16.dp))
        CommonButton(title = STR(R.string.refresh)) {
            //gamesViewModel.loadData()
            onRefresh()
        }
    }
}

@Preview
@Composable
fun Preview(){

}