package com.gametaco.app_android_fd.ui.popups

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.gametaco.app_android_fd.ui.modifiers.clickableWithoutRipple
import com.gametaco.app_android_fd.ui.theme.AppFont
import com.gametaco.app_android_fd.viewmodel.GoalsViewModel

@Composable
fun ReplayPopup() {
    val show = GoalsViewModel.instance.showReplayPopup.collectAsState()
    if(show.value == false){
        return
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Color.Black.copy(alpha = 0.6f)
            )
            .clickableWithoutRipple {

            },
        contentAlignment = Alignment.Center
    ) {
        Box(modifier = Modifier
//            .navigationBarsPadding()
            .padding(20.dp)
            .fillMaxHeight(0.69f)
            .clip(RoundedCornerShape(16.dp))
            .shadow(10.dp, RoundedCornerShape(16.dp))
//            .border(1.dp, Color.Green)
        ){
            Image(
                painter = painterResource(id = resources.R.drawable.img_replay_popup),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Fit
            )
            TextButton(modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = 0.dp, end = 10.dp)
//                .border(1.dp, Color.Red)
               , onClick = {
                GoalsViewModel.instance.closeReplayPopup()
            }) {
                Text(
                    text = "Close",
                    fontSize = 16.sp,
                    color = Color.Transparent,
                    fontFamily = AppFont.ProximaNova,
                    modifier = Modifier.padding()
                )
            }
            TextButton(modifier = Modifier
                .padding(bottom = 45.dp, start = 16.dp,end = 16.dp)
                .fillMaxWidth()
                .height(60.dp)
                .align(Alignment.BottomCenter)
//                .border(1.dp, Color.Red)
                ,onClick = {
                GoalsViewModel.instance.closeReplayPopup()
            }) {
                Text(
                    text = "PLAY NOW",
                    fontSize = 16.sp,
                    color = Color.Transparent,
                    fontFamily = AppFont.ProximaNova,
                    modifier = Modifier.padding()
                )
            }
        }
    }
}
