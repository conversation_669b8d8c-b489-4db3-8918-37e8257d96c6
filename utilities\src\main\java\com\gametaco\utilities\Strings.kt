package com.gametaco.utilities

import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.res.stringResource

@Composable
@ReadOnlyComposable
fun STR(@StringRes id: Int) : String {
    return stringResource(id)
}

@Composable
@ReadOnlyComposable
fun STR(@StringRes id: Int, vararg formatArgs: Any): String {
    return stringResource(id, *formatArgs)
}
