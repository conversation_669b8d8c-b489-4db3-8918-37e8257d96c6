package com.gametaco.app_android_fd.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

//val Purple40 = Color(0xFF6650a4)
//val PurpleGrey40 = Color(0xFF625b71)
//val Pink40 = Color(0xFF7D5260)
val theme_light_primary = Color(0xFF0070EB)
val theme_light_inverse_primary = Color(0xFFEAC300)
val theme_light_secondary = Color(0xFF05285A)
val theme_light_secondary_container = Color(0xFF004EA3)
val theme_light_tertiary = Color(0xFF127F00)
val theme_light_tertiary_container = Color(0xFFE9F8EF)
val theme_light_background = Color(0xFFEAF0F6)
val theme_light_surface = Color.White
val theme_light_outline = Color(0xFFB0B7BF)
val theme_light_outline_variant = Color(0xFFD0D7DC)
val theme_light_on_primary = Color.White
val theme_light_on_surface = Color(0xFF131314)
val theme_light_inverse_on_surface = Color(0xFF696B6D)

val theme_light_outline_variant2 = Color(0xFFEAF0F6)
