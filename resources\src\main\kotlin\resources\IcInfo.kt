
import androidx.compose.foundation.Image
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp


@Preview
@Composable
private fun VectorPreview() {
	Image(Info, null)
}

private var _IcInfo: ImageVector? = null

public val Info: ImageVector
	get() {
		if (_IcInfo != null) {
			return _IcInfo!!
		}
		_IcInfo = ImageVector.Builder(
			name = "InfoL",
			defaultWidth = 48.dp,
			defaultHeight = 48.dp,
			viewportWidth = 48f,
			viewportHeight = 48f
		).apply {
			path(
				fill = SolidColor(Color(0xFFFFFFFF)),
				fillAlpha = 1.0f,
				stroke = null,
				strokeAlpha = 1.0f,
				strokeLineWidth = 1.0f,
				strokeLineCap = StrokeCap.Butt,
				strokeLineJoin = StrokeJoin.Miter,
				strokeLineMiter = 1.0f,
				pathFillType = PathFillType.EvenOdd
			) {
				moveTo(1f, 24f)
				curveTo(1f, 11.2986f, 11.2986f, 1f, 24f, 1f)
				curveTo(36.7014f, 1f, 47f, 11.2986f, 47f, 24f)
				curveTo(47f, 36.7014f, 36.7014f, 47f, 24f, 47f)
				curveTo(11.2986f, 47f, 1f, 36.7014f, 1f, 24f)
				close()
				moveTo(3.705882f, 24f)
				curveTo(3.7059f, 35.1888f, 12.8112f, 44.2941f, 24f, 44.2941f)
				curveTo(35.1888f, 44.2941f, 44.2941f, 35.1888f, 44.2941f, 24f)
				curveTo(44.2941f, 12.8112f, 35.1888f, 3.7059f, 24f, 3.7059f)
				curveTo(12.8112f, 3.7059f, 3.7059f, 12.8112f, 3.7059f, 24f)
				close()
				moveTo(24f, 17.235294f)
				curveTo(23.2532f, 17.2353f, 22.6471f, 17.8414f, 22.6471f, 18.5882f)
				lineTo(22.647058f, 37.529411f)
				curveTo(22.6471f, 38.2762f, 23.2532f, 38.8824f, 24f, 38.8824f)
				curveTo(24.7468f, 38.8824f, 25.3529f, 38.2762f, 25.3529f, 37.5294f)
				lineTo(25.352942f, 18.588236f)
				curveTo(25.3529f, 17.8414f, 24.7468f, 17.2353f, 24f, 17.2353f)
				close()
				moveTo(22.647058f, 10.470589f)
				curveTo(22.6471f, 9.7238f, 23.2532f, 9.1176f, 24f, 9.1176f)
				curveTo(24.7468f, 9.1176f, 25.3529f, 9.7238f, 25.3529f, 10.4706f)
				lineTo(25.352942f, 13.176472f)
				curveTo(25.3529f, 13.9233f, 24.7468f, 14.5294f, 24f, 14.5294f)
				curveTo(23.2532f, 14.5294f, 22.6471f, 13.9233f, 22.6471f, 13.1765f)
				lineTo(22.647058f, 10.470589f)
				close()
			}
		}.build()
		return _IcInfo!!
	}

