package com.gametaco.app_android_fd.ui.components

import <PERSON><PERSON><PERSON><PERSON>
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.gametaco.app_android_fd.ui.modifiers.clickableWithoutRipple
import com.gametaco.app_android_fd.ui.modifiers.onAppear
import com.gametaco.app_android_fd.ui.theme.OverlayBackground
import com.gametaco.utilities.STR
import resources.R
import kotlin.math.roundToInt

@Composable
fun BottomSheetModal(
    title: String,
    titleStyle:TextStyle = MaterialTheme.typography.titleMedium,
    showBackButton: Boolean = false,
    backButtonSize: Dp = 18.dp,
    showDivider: Boolean = true,
    expanded: Boolean,
    showBlocker: Boolean = true,
    verticalScrollContent: Boolean = true,
    closeAction: () -> Unit,
    enterTransition: EnterTransition = slideInVertically(initialOffsetY = { fullHeight ->  fullHeight}),
    exitTransition: ExitTransition = slideOutVertically(targetOffsetY = { fullHeight ->  fullHeight}),
    contentBackgroundColor: Color = MaterialTheme.colorScheme.surface,
    leftTopContent: (@Composable () -> Unit)? = null,
    footer: (@Composable () -> Unit)? = null,
    content: @Composable () -> Unit
) {
    var offsetY by remember { mutableStateOf(0f) }

    Box (modifier = Modifier.fillMaxSize()){
        if(expanded && showBlocker){
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(OverlayBackground)
                    .clickable {}
                    .onAppear { offsetY = 0f }
                    .pointerInput(Unit) {
                        detectVerticalDragGestures(
                            onVerticalDrag = { change, dragAmount ->
                                offsetY += dragAmount
                                if (offsetY < 0) {
                                    offsetY = 0f
                                }
                                change.consume()
                            },
                            onDragEnd = {
                                if (offsetY > 500) {
                                    closeAction()
                                } else {
                                    offsetY = 0f
                                }
                            }
                        )
                    },
            ){
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp)
                        .align(Alignment.TopCenter)
                        .clickable { closeAction() }
                )
            }
        }
        AnimatedVisibility(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .offset { IntOffset(0, offsetY.roundToInt()) },
            visible = expanded,
            enter = enterTransition,
            exit = exitTransition,
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 60.dp)
                    .align(Alignment.BottomCenter)
                    .background(
                        MaterialTheme.colorScheme.surface,
                        RoundedCornerShape(
                            topStart = 18.dp,
                            topEnd = 18.dp
                        )
                    ),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Top,
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(54.dp),
                    contentAlignment = Alignment.Center
                ) {
                    AutosizeText(
                        text = title,
                        style = titleStyle,
                        fontWeight = FontWeight.Bold,
                        maxLines = 1,
                        color = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier
                            .padding(horizontal = if(showBackButton) {32.dp} else {64.dp})
                            .align(Alignment.Center)
                    )

                    if (showBackButton) {
                        Box (
                            modifier = Modifier
                                .fillMaxHeight()
                                .width(backButtonSize * 3)
                                .clickableWithoutRipple { closeAction() }
                                .align(Alignment.CenterStart)
                                .padding(start = 18.dp, end = 6.dp)
                        ) {
                            Icon(
                                imageVector = IcBack,
                                tint = MaterialTheme.colorScheme.primary,
                                contentDescription = null,
                                modifier = Modifier
                                    .size(backButtonSize)
                                    .align(Alignment.CenterStart)
                            )
                        }
                    } else {
                        if(leftTopContent != null){
                            Box (
                                modifier = Modifier
                                    .fillMaxHeight()
//                                    .width(backButtonSize * 3)
//                                    .clickableWithoutRipple { closeAction() }
                                    .align(Alignment.CenterStart)
                                    .padding(start = 18.dp, end = 6.dp)
                            ){
                                leftTopContent()
                            }
                        }
                        Box (
                            modifier = Modifier
                                .fillMaxHeight()
                                .clickableWithoutRipple { closeAction() }
                                .align(Alignment.CenterEnd)
                                .padding(start = 6.dp, end = 18.dp)
                        ) {
                            Text(
                                text = STR(R.string.close),
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier
                                    .align(Alignment.CenterEnd)
                            )
                        }
                    }
                }
                if (showDivider) {
                    Divider(
                        modifier = Modifier.fillMaxWidth(),
                        thickness = 2.dp,
                        color = MaterialTheme.colorScheme.outlineVariant
                    )
                }
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(contentBackgroundColor)
                        .weight(weight = 1f, fill = false)
                        .then(
                            if (verticalScrollContent) {
                                Modifier.verticalScroll(rememberScrollState())
                            } else {
                                Modifier
                            }
                        )
                ) {
                    content()
                }
                Box {
                    if (footer != null) {
                        footer()
                    }
                }
            }
        }
    }
}
