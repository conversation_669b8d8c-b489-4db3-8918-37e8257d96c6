package com.gametaco.app_android_fd.manager

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import com.gametaco.app_android_fd.models.ModalPopupData

class ModalPopupManager {

    private val _modalState = mutableStateOf<ModalPopupData?>(null)
    val modalState: State<ModalPopupData?> = _modalState

    fun showModal(modalData : ModalPopupData) {
        _modalState.value = modalData
    }

    fun dismissModal() {
        _modalState.value?.onDismiss?.invoke()
        _modalState.value = null
    }

    fun isShowing() : Boolean {
        return modalState.value != null
    }
}





