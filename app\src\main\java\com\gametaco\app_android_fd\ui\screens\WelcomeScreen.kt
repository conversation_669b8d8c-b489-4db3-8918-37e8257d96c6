package com.gametaco.app_android_fd.ui.screens

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.ui.components.Background
import com.gametaco.app_android_fd.ui.components.WelcomeCarousel
import com.gametaco.app_android_fd.ui.components.WelcomePage
import com.gametaco.app_android_fd.ui.theme.BlueWelcomeBackground
import com.gametaco.app_android_fd.viewmodel.WelcomeViewModel
import com.gametaco.utilities.STR
import resources.R

@Composable
fun WelcomeScreen(welcomeViewModel: WelcomeViewModel)
{

    val experimentsReady by ExperimentManager.instance.experimentsReady.collectAsState() //experiments either loaded or failed

    if(!experimentsReady) {
        LoadingScreen()
        return
    }

    val italicTitle:TextStyle = MaterialTheme.typography.titleMedium.copy(
        fontStyle = FontStyle.Italic,
        fontSize = 32.sp,
        lineHeight = 40.sp
    )
    val subtitleStyle:TextStyle = MaterialTheme.typography.titleMedium.copy(
        fontWeight = FontWeight.Bold,
        fontSize = 20.sp,
        lineHeight = 25.sp
    )
    val welcomeCarouselPages: List<@Composable (Offset?) -> Unit> = listOf(
        { safeZoneBottomEndPosition ->
            WelcomePage(
                backgroundImage = R.drawable.img_welcome_1,
                safeZoneBottomEndPosition = safeZoneBottomEndPosition,
                icon = R.drawable.ic_fanduel_full,
                title = STR(R.string.your_favorite_games_all_in_one_app),
                titleStyle = italicTitle.copy(fontSize = 24.sp, lineHeight = 30.sp)
            )
        },
        { safeZoneBottomEndPosition ->
            WelcomePage(
                backgroundImage = R.drawable.img_welcome_2,
                safeZoneBottomEndPosition = safeZoneBottomEndPosition,
                title = STR(R.string.play_for_fun_compete_for_cash),
                subTitle = STR(R.string.play_against_other_people_for_real_cash),
                titleStyle = italicTitle,
                subtitleStyle = subtitleStyle
            )
        },
        { safeZoneBottomEndPosition ->
            WelcomePage(
                backgroundImage = R.drawable.img_welcome_3,
                safeZoneBottomEndPosition = safeZoneBottomEndPosition,
                title = STR(R.string.skill_based_matchmaking),
                subTitle = STR(R.string.play_head_to_head_games_with_players_of_a_similar_skill_level),
                titleStyle = italicTitle,
                subtitleStyle = subtitleStyle
            )
        },
    )
    Background(BlueWelcomeBackground)
    WelcomeCarousel(welcomeCarouselPages, welcomeViewModel)
}


@Preview(widthDp = 600, heightDp = 800)
@Preview(widthDp = 360, heightDp = 740)
@Composable
fun WelcomeScreenScreen(){
    PreviewRoot {
        WelcomeScreen(WelcomeViewModel())
    }
}
