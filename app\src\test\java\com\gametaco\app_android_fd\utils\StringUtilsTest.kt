package com.gametaco.app_android_fd.utils

import kotlin.test.Test
import kotlin.test.assertEquals


class StringUtilsTest {

    @Test fun dollarWithComma() {
        val items = listOf(
            "1.01" to "$1.01",
            "1000" to "$1,000",
            "1000.01" to "$1,000",
            "1000000.01" to "$1,000,000",
            1.01 to "$1.01",
            1000.0 to "$1,000",
            1000.01 to "$1,000",
            1000000.01 to "$1,000,000",
        )

        items.forEach { (input, expected) ->
            val result = when (input) {
                is String -> input.toDollarWithCommaString()
                is Double -> input.toDollarWithCommaString()
                else -> throw IllegalArgumentException("Unsupported type: ${input::class.java}")
            }
            assertEquals(expected, result)
        }
    }

    @Test fun numberWithComma() {
        val items = listOf(
            1 to "1",
            1000 to "1,000",
            1000000 to "1,000,000",
            "1.01" to "1.01",
            "1000" to "1,000",
            "1000.01" to "1,000",
            "1000000.01" to "1,000,000",
            1.01 to "1.01",
            1000 to "1,000",
            1000.01 to "1,000",
            1000000 to "1,000,000",
        )

        items.forEach { (input, expected) ->
            val result = when (input) {
                is Int -> input.toNumberWithCommaString()
                is String -> input.toNumberWithCommaString()
                is Double -> input.toNumberWithCommaString()
                else -> throw IllegalArgumentException("Unsupported type: ${input::class.java}")
            }
            assertEquals(expected, result)
        }
    }

}