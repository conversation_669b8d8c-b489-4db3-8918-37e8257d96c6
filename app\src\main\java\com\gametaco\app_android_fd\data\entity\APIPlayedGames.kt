package com.gametaco.app_android_fd.data.entity

data class APIPlayedGamesResponse(
    val count: Int,
    val results: List<APIPlayedGame>
)

data class APIPlayedGame(
    val id: String,
    val created_at: String?,
    val tournament_instance_id: String,
    val started_at: String,
    val ended_at: String?,
    val game_id: String?,
    val game_display_name: String?,
    val tournament_instance_opened_at: String,
    val tournament_instance_closed_at: String?,
    val entry_fee: String,
    val game_icon: String?,
    val maximum_entries: Int?,
    val is_winner: Boolean,
    val is_tie: Boolean?,
    val status: String,
    val tournament_instance_status: String,
    val tournament_start_information: TournamentStartInformation?,
    val tournament_brand: String,
    val winnings: Winnings?,
    val tournament_description: String?,
    val game_mode_name: String?,
    val game_mode_description: String?,
    val game_mode_icon: String?
){
}

data class APIPlayedGameWinnings(
    val place: Int,
    val prize_amount: String
)



data class APIPlayedGameIDResponse(
    val id:String?,
    val tournament_id:String?,
    var game_id:String?,
    val user: APIPlayedGameIDUser,
    val tournament_instance_id: String,
    val tournament_instance_opened_at:String?,
    val tournament_instance_closed_at:String?,
    val started_at: String?,
    val ended_at: String?,
    val entry_fee: String,
    val maximum_entries: Int?,
    val is_winner: Boolean,
    val is_tie: Boolean?,
    val score: Int?,
    val current_entries_count: Int,
    val game_icon: String?,
    val game_display_name: String,
    val results: List<APIPlayedGameIDResult>,
    val tournament_instance_status: String,
    val refund_reason:String?,
    val reference_id:String?,
    val prize_amount: String?,
    val game_mode_name: String?,
    val game_mode_description: String?,
    val game_mode_icon: String?,
)

data class APIPlayedGameIDUser(
    val username:String,
    val avatar_url:String?
)

data class APIPlayedGameIDResult(
    val username:String,
    val score: Int?,
    val prize_amount: String?,
    val avatar_url: String?,
)

data class APIRecentlyPlayedGamesResponse(
    val results:List<String>
)