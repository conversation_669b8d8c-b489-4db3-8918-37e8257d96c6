package com.gametaco.app_android_fd.manager

import androidx.compose.runtime.Composable
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.API400ErrorCode
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.ui.popups.AlertDialogTechnicalProblemsFD
import com.gametaco.app_android_fd.ui.screens.GeoAlertModel
import com.gametaco.app_android_fd.ui.screens.GeoAlertType
import com.gametaco.utilities.ResourceState
import com.gametaco.utilities.STR
import org.greenrobot.eventbus.EventBus
import resources.R

class ErrorManager(
    private val alertDialogManager: AlertDialogManager
){

    companion object {
        const val TAG = "ErrorManager"
        val instance: ErrorManager
            get() = resolve()
    }

    fun <T> handleResourceStateError(resourceState : ResourceState<T>) : Boolean {

        if(!resourceState.isError())
            return false

        val errorState = resourceState as ResourceState.Error

        when(errorState.code) {
            400 -> {
                val error400 = WorldWinnerAPI.instance.stringTo400Error(errorState.error)

                val fieldErrors = error400?.field_errors
                if (!fieldErrors.isNullOrEmpty()) {
                    var errorMsg = ""
                    for ((key, valueList) in fieldErrors) {
//                        println("Key: $key")
                        valueList.forEach { value ->
                            errorMsg += "$key : $value\n"
                        }
                    }
                    alertDialogManager.showDialog("Error", errorMsg, "Confirm", {})
                } else {

                    if (error400?.general_error?.code ?: API400ErrorCode.UNKNOWN_ERROR == API400ErrorCode.UNKNOWN_FANDUEL_ERROR_OCCURRED ||
                        error400?.general_error?.code ?: API400ErrorCode.UNKNOWN_ERROR == API400ErrorCode.UNKNOWN_ERROR
                    ) {
                        alertDialogManager.showDialogCustom(composeFunction = @Composable {
                            AlertDialogTechnicalProblemsFD(title = "Sorry!",
                                message = "We are experiencing technical problems and apologize for any inconvenience. Please try again later. If you have any questions, please contact Player Services.",
                                aButtonText = "Ok",
                                onAPressed = {
                                },
                                bButtonText = "Get Help",
                                onBPressed = {
                                    FDManager.instance.showWebviewFD(FDWebviewPage.Support)
                                })
                        })
                    } else {

                        var messageString = error400?.general_error?.message
                        if (messageString.isNullOrEmpty()) {
                            messageString = error400?.general_error?.code?.errorMessage
                        }

                        alertDialogManager.showDialog(
                            "Error",
                            messageString ?: "An unknown error occurred",
                            "Confirm",
                            {})

                    }
                    return true
                }
            }
            401 -> { return true } //401 handled in UnauthorizedInterceptor
            500 -> {
                if (TournamentManager.instance.getTournamentEntryState() == null) {
                    alertDialogManager.showDialog("Error", "An error occurred on our end, please check back later", "Confirm", {} )
                }
                return true
            }
            404, 503 -> {
                    alertDialogManager.showDialogCustom(composeFunction = @Composable {
                        AlertDialogTechnicalProblemsFD(title = STR(R.string.connection_issue),
                            message = STR(R.string.connection_issue_detail),
                            aButtonText = "Ok",
                            onAPressed = {
                            },
                            bButtonText = "Get Help",
                            onBPressed = {
                                FDManager.instance.showWebviewFD(FDWebviewPage.Support)
                            })
                    })
                return true
            }
            0 -> {
                AlertDialogManager.instance.showDialog("Alert!", "Something went wrong on our end. Please try again or check back later.", "Confirm", {} )
                return true
            }
            else -> {
                return false
            }
        }

        return false
    }




}