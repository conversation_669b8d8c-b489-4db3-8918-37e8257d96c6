package com.gametaco.app_android_fd.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.gametaco.app_android_fd.ui.theme.AppFont
import com.gametaco.utilities.STR
import resources.R

@Composable
fun WelcomePage(
    backgroundImage: Int,
    safeZoneBottomEndPosition: Offset?,
    icon: Int? = null,
    title: String,
    titleStyle: TextStyle,
    subTitle: String? = null,
    subtitleStyle: TextStyle = MaterialTheme.typography.titleSmall,
)
{
    Box(modifier = Modifier.fillMaxSize()) {
        Image(
            painter = painterResource(backgroundImage),
            alignment = Alignment.TopCenter,
            contentScale = ContentScale.FillWidth,
            contentDescription = STR(R.string.content_description_check),
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(.8f)
        )

        if (safeZoneBottomEndPosition != null) {
            val density = LocalDensity.current
            val contentWidth = with(density) { safeZoneBottomEndPosition.x.toDp() }
            val contentHeight = with(density) { safeZoneBottomEndPosition.y.toDp() }

            WelcomePageContent(
                modifier = Modifier
                    .width(contentWidth)
                    .height(contentHeight)
                    .align(Alignment.TopCenter),
                icon = icon,
                title = title,
                titleStyle = titleStyle,
                subTitle = subTitle,
                subtitleStyle = subtitleStyle
            )
        }
    }

}

@Composable
fun WelcomePageContent(
    modifier: Modifier = Modifier,
    icon: Int? = null,
    title: String,
    titleStyle: TextStyle,
    subTitle: String? = null,
    subtitleStyle: TextStyle = MaterialTheme.typography.titleSmall,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(
            space = 0.dp,
            alignment = Alignment.CenterVertically
        ),
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier,
    )
    {
        Spacer(modifier = Modifier
            .weight(1f)
            .fillMaxWidth())

        if(icon != null) {
            Image(
                painter = painterResource(id = icon),
                alignment = Alignment.BottomCenter,
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .width(240.dp)
                    .aspectRatio(1.16f)

            )
        }
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .fillMaxWidth()
                .height(84.dp)
        )
        {
            Text(
                text = title,
                color = MaterialTheme.colorScheme.onPrimary,
                style = titleStyle,
                textAlign = TextAlign.Center,
                fontStyle = FontStyle.Normal,
                fontFamily = AppFont.FanduelDisplay,
                fontWeight = FontWeight.Black,
                overflow = TextOverflow.Visible,
                modifier = Modifier
                    .fillMaxSize()
            )
        }
        if (subTitle != null) {
            Spacer(modifier = Modifier.height(12.dp))
            Text(
                text = subTitle,
                style = subtitleStyle,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onPrimary,
                fontFamily = AppFont.ProximaNova,
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .padding(16.dp)
                    .align(Alignment.CenterHorizontally)
            )
            // The mockups have significant space beneath the subtitle
            Spacer(modifier = Modifier.height(36.dp))
        }
    }

}

@Composable
fun LobbyPage(
    backgroundImage: Int,
    buttonText: String,
    onClick: () -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.primary)
    )

    Image(
        painter = painterResource(backgroundImage),
        alignment = Alignment.TopCenter,
        contentScale = ContentScale.FillHeight,
        contentDescription = STR(R.string.content_description_check),
        modifier = Modifier
            .fillMaxSize()
            .padding(bottom = 42.dp)
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
    ){
        Button(
            modifier = Modifier
                .padding(start = 12.dp, end = 12.dp, bottom = 36.dp)
                .height(40.dp)
                .fillMaxWidth()
                .align(Alignment.BottomCenter),
            contentPadding = PaddingValues(6.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.inversePrimary,
                contentColor = MaterialTheme.colorScheme.secondary
            ),
            shape = RectangleShape,
            onClick = onClick
        ) {
            Text(
                text = buttonText,
                fontWeight = FontWeight.Bold
            )
        }
    }
}