package com.gametaco.app_android_fd.ui.components

//import androidx.compose.foundation.layout.FlowColumnScopeInstance.weight
//import androidx.compose.foundation.layout.FlowRowScopeInstance.weight
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Divider
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.TextUnitType
import androidx.compose.ui.unit.dp
import toDp

@Composable
fun ContentBox(
    modifier: Modifier = Modifier,
    titlePaddingValues: PaddingValues = PaddingValues(16.dp, 14.dp),
    bodyPaddingValues: PaddingValues = PaddingValues(
        start = 16.dp,
        top = 14.dp,
        end = 16.dp,
        bottom = 16.dp
    ),
    bodyBlockedIfGuest: Boolean = false,
    guestBlockTitle:String = "",
    guestBlockDesc:String = "",
    guestBlockLabel:String = "",
    guestBlockClick:(()->Unit)? = null,
    cornerRoundness: Dp = 4.dp,
    boarderSize:Dp = 1.dp,
    background:Color = MaterialTheme.colorScheme.surface,
    titleComposable: @Composable (() -> Unit)? = null,
    bodyComposable: @Composable (() -> Unit)? = null,

) {
    Box(modifier = Modifier
        .fillMaxWidth()
        .background(background, shape = RoundedCornerShape(cornerRoundness))
        .clip(shape = RoundedCornerShape(cornerRoundness))
        .then(
            if(boarderSize > 0.dp){
                Modifier.border(boarderSize, MaterialTheme.colorScheme.outline, shape = RoundedCornerShape(cornerRoundness))
            }else{
                Modifier
            }
        ).then(modifier)
    ) {
        Column {
            if(titleComposable != null){
                Box (
                    modifier = Modifier
                        .padding(titlePaddingValues)
                ) {
                    titleComposable()
                }
            }

            if (bodyComposable == null) {
                return
            }
            if(titleComposable != null){
                Divider(thickness = 1.dp, color = MaterialTheme.colorScheme.outline)
            }
            var size by remember {
                mutableStateOf(IntSize(0,0))
            }
            Box(modifier = Modifier.onSizeChanged {
                size = it
            })
            {
                Box (
                    modifier = Modifier
                        .padding(bodyPaddingValues)
                ) {
                    bodyComposable()
                }
                if(bodyBlockedIfGuest)
                {
                    Box(Modifier.height(size.toDp().height))
                    {
                        GuestBlockComponent(
                            title = guestBlockTitle,
                            desc = guestBlockDesc,
                            label = guestBlockLabel,
                            onClick = guestBlockClick
                        )
                    }
                }
            }

        }
    }
}

@Composable
fun ContentTitle(
    icon: Int? = null,
    iconDescription: String? = null,
    iconColor: Color? = null,
    value: String? = null,
    style: TextStyle = MaterialTheme.typography.bodyMedium,
    title: String
) {
    Row (
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth()
    ) {
        if (icon != null) {
            Image(
                modifier = Modifier.height(18.dp),
                contentScale = ContentScale.FillHeight,
                painter = painterResource(icon),
                contentDescription = iconDescription,
                colorFilter = ColorFilter.tint(
                    iconColor ?: MaterialTheme.colorScheme.secondary
                )
            )
            Spacer(modifier = Modifier.width(8.dp))
        }
        Text(
            text = title,
            style = style,
            color = MaterialTheme.colorScheme.secondary
        )
        if (value != null) {
            AutosizeText(
                text = value,
                style = style,
                textAlign = TextAlign.End,
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.tertiary,
                maxLines = 1
            )
        }
    }
}

@Composable
fun ContentListBody(
    listElements: List<@Composable ()->Unit>,
    precedingSpacerHeight: Dp = 6.dp,
    elementSpacingHeight: Dp = 12.dp,
    showDivider: Boolean = true,
    dividerPadding: PaddingValues = PaddingValues(start = 16.dp),
    dividerColor: Color = MaterialTheme.colorScheme.outline
)
{
    Column (
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        listElements.forEachIndexed {
                index,
                listElement ->
            if (index > 0) {
                Spacer(modifier = Modifier.height(elementSpacingHeight))
                if(showDivider)
                {
                    HorizontalDivider(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(dividerPadding)
                            .background(MaterialTheme.colorScheme.surface),
                        thickness = 1.dp,
                        color = dividerColor
                    )
                }
                Spacer(modifier = Modifier.height(elementSpacingHeight))
            }
            else
            {
                Spacer(modifier = Modifier.height(precedingSpacerHeight))
            }

            listElement()
        }
    }
}
@Composable
fun Background(backgroundBrush: Brush, paddingAmount: Dp = 0.dp)
{
    Box(modifier = Modifier
        .fillMaxSize()
        .background(
            brush = backgroundBrush
        )
        .padding(paddingAmount)
    )
    {

    }
}

@Composable
fun ContentMessageBody(message: String) {
    Text(
        text = message,
        fontSize = TextUnit(14f, TextUnitType.Sp),
        lineHeight = TextUnit(18f, TextUnitType.Sp)
    )
}

fun getValueAsCurrency(value: Float): String {
    return "\$${"%.2f".format(value)}"
}

@Composable
fun NoRippleButton(
    onClick:() -> Unit,
    content: @Composable () -> Unit
) {
    Box(
        modifier = Modifier
            .background(Color.Transparent)
            .clickable(
                interactionSource = remember{ MutableInteractionSource()},
                indication = null
            )
            {
                onClick()
            }
    )
    {
        content()
    }
}

