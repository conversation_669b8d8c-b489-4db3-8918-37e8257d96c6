package com.gametaco.app_android_fd.ui.navigation

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.runtime.Composable
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.ui.screens.AccountHubScreen
import com.gametaco.app_android_fd.ui.screens.GameplayScreen
import com.gametaco.app_android_fd.ui.screens.GoalsScreen
import com.gametaco.app_android_fd.ui.screens.HomeScreen
import com.gametaco.app_android_fd.ui.screens.LobbyScreen
import com.gametaco.app_android_fd.ui.screens.LoginScreen
import com.gametaco.app_android_fd.ui.screens.MaintenanceScreen
import com.gametaco.app_android_fd.ui.screens.PlayScreen
import com.gametaco.app_android_fd.ui.screens.ScoresScreen
import com.gametaco.app_android_fd.ui.screens.WebViewScreen
import com.gametaco.app_android_fd.ui.screens.WelcomeScreen
import com.gametaco.app_android_fd.viewmodel.GameplayViewModel
import com.gametaco.app_android_fd.viewmodel.GamesViewModel
import com.gametaco.app_android_fd.viewmodel.GoalsViewModel
import com.gametaco.app_android_fd.viewmodel.LoginViewModel
import com.gametaco.app_android_fd.viewmodel.PlayViewModel
import com.gametaco.app_android_fd.viewmodel.ScoresViewModel
import com.gametaco.app_android_fd.viewmodel.WebViewViewModel
import com.gametaco.app_android_fd.viewmodel.WelcomeViewModel


val noEnterTransition : AnimatedContentTransitionScope<NavBackStackEntry>.() -> EnterTransition = {
    fadeIn(
        animationSpec = tween(durationMillis = 30),
        initialAlpha = 0.99f
    )
}

val noExitTransition : AnimatedContentTransitionScope<NavBackStackEntry>.() -> ExitTransition = {
    fadeOut(
        animationSpec = tween(durationMillis = 30),
        targetAlpha = 0.99f
    )
}


@Composable
fun AppNavigationGraph(navHostController: NavHostController){

    NavHost(navController = navHostController,
            startDestination = Routes.LOGIN_SCREEN,
            enterTransition =  noEnterTransition,
            exitTransition =  noExitTransition,
    ) {

        composable(Routes.WELCOME_SCREEN) {
            WelcomeScreen(WelcomeViewModel.instance)
        }

        composable(Routes.HOME_SCREEN) {
            HomeScreen()
        }

        composable(Routes.GAMES_SCREEN){
            LobbyScreen(GamesViewModel.instance, PlayViewModel.instance)
        }

        composable(Routes.GOALS_SCREEN) {
            GoalsScreen(GoalsViewModel.instance)
        }

        composable(Routes.SCORES_SCREEN) {
            ScoresScreen(ScoresViewModel.instance)
        }

        composable(Routes.CONTEST_SCREEN){
            PlayScreen(PlayViewModel.instance)
        }

        composable(Routes.ACCOUNT_SCREEN){
            AccountHubScreen(navHostController = navHostController)
        }

        composable(Routes.WEBVIEW_SCREEN){
            WebViewScreen(WebViewViewModel.instance)
        }

        composable(Routes.LOGIN_SCREEN){
            LoginScreen(LoginViewModel.instance)
        }

        composable(Routes.GAMEPLAY_SCREEN){
            GameplayScreen(GameplayViewModel.instance)
        }

        composable(Routes.MAINTENANCE_SCREEN){
            MaintenanceScreen()
        }
    }
}
