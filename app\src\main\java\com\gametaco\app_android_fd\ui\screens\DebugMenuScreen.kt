package com.gametaco.app_android_fd.ui.screens
import android.app.Notification
import android.app.NotificationManager
import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.Adb
import androidx.compose.material.icons.filled.ArrowDownward
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.ImageAspectRatio
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.app.NotificationCompat
import androidx.navigation.NavController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.braze.Constants
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.data.ENV
import com.gametaco.app_android_fd.data.EnvHost
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.manager.ActivityManager
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.BrazeManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.OnSessionChangedEvent
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.ui.components.BottomSheetModal
import com.gametaco.app_android_fd.ui.components.DebugActionButton
import com.gametaco.app_android_fd.ui.components.DropdownComponent
import com.gametaco.app_android_fd.ui.components.Error400Selector
import com.gametaco.app_android_fd.ui.components.ToggleSwitch
import com.gametaco.app_android_fd.utils.log.Logger
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus


enum class DebugMenuNav{
    Environment,
    Setting,
    Log,
    Account
}

@Composable
fun DebugMenuScreen (show: MutableState<Boolean>){
    val navController = rememberNavController()
    var currentRoute by remember { mutableStateOf(DebugMenuNav.Environment.name) }

    navController.addOnDestinationChangedListener { _, destination, _ ->
        currentRoute = destination.route ?: DebugMenuNav.Environment.name
    }

    BottomSheetModal(
        title = "☕️☕️☕️ Debug Menu ☕️☕️☕️",
        showBackButton = false,
        showDivider = true,
        expanded = show.value,
        closeAction = {
            show.value = false
        },
        footer = {
            Surface(
                modifier = Modifier
                    .navigationBarsPadding()
                    .shadow(4.dp)
                    .padding(top = 8.dp)
                    .background(Color.Transparent)
                    .fillMaxWidth()
            ){
                Row(horizontalArrangement = Arrangement.SpaceAround, modifier = Modifier.fillMaxWidth()) {
                    TabItem(DebugMenuNav.Environment.name,navController,Icons.Default.Adb,DebugMenuNav.Environment.name,currentRoute)
                    TabItem(DebugMenuNav.Setting.name,navController,Icons.Default.Settings,DebugMenuNav.Setting.name,currentRoute)
                    TabItem(DebugMenuNav.Log.name,navController,Icons.Default.ImageAspectRatio,DebugMenuNav.Log.name,currentRoute)
                    TabItem(DebugMenuNav.Account.name,navController,Icons.Default.AccountCircle,DebugMenuNav.Account.name,currentRoute)
                }
            }
        },
        contentBackgroundColor = MaterialTheme.colorScheme.background,
        verticalScrollContent = false,
    ){
        NavHost(navController = navController, startDestination = DebugMenuNav.Environment.name) {
            composable(DebugMenuNav.Environment.name) { EnvironmentView(navController) }
            composable(DebugMenuNav.Setting.name) { SettingView(navController) }
            composable(DebugMenuNav.Log.name) { LogView(navController) }
            composable(DebugMenuNav.Account.name) { AccountView(navController) }
        }
    }
}

@Composable
private fun TabItem(route:String,navController:NavController,imageVector: ImageVector,label:String,currentRoute:String){
    Column(modifier = Modifier.clickable {
        navController.navigate(route)
    }, horizontalAlignment = Alignment.CenterHorizontally) {
        val tint = if(currentRoute == route) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.secondary
        Icon(imageVector = imageVector, contentDescription = label,tint = tint, modifier = Modifier.size(30.dp))
        Text(text = label, fontSize = 14.sp, fontWeight = FontWeight.Bold, color = tint)
    }
}
@Composable
private fun EnvironmentView(navController:NavController){
    Column {
        var currentENV by remember { mutableStateOf(AppEnv.current) }
        val sessionExpiry by FDManager.instance.sessionDataExpiryFlow.collectAsState()

        DropdownComponent(
            label = "Environment",
            defaultOption = PreferencesManager.instance.getAppENV(),
            options = ENV.entries.map { it.name },
        ){
            PreferencesManager.instance.setAppENV(it)
            currentENV = AppEnv.current
            WorldWinnerAPI.instance.refreshApiService()
        }
        DropdownComponent(
            label = "Environment Host",
            defaultOption = PreferencesManager.instance.getAppEnvHost() ?: "",
            options = EnvHost.entries.map { it.name },
        ){
            PreferencesManager.instance.setAppEnvHost(it)
            currentENV = AppEnv.current
            WorldWinnerAPI.instance.refreshApiService()
        }
        TextFieldWithCopyButton(currentENV.api_app_id,"App ID")
        TextFieldWithCopyButton(currentENV.app_version,"App Version")
        TextFieldWithCopyButton(currentENV.api_endpoint,"API Endpoint")
        TextFieldWithCopyButton(PreferencesManager.instance.getDeviceID() ?: "null","Device ID")
        TextFieldWithCopyButton(sessionExpiry ?: "loading...","Session Expiry")
    }
}@Composable
private fun TextFieldWithCopyButton(text:String,label: String){
    TextField(
        value = text,
        onValueChange = {},
        readOnly = true,
        label = { Text(label) },
        maxLines = 2,
        modifier = Modifier
            .fillMaxWidth(),
        trailingIcon = {
            val clipboardManager: ClipboardManager = LocalClipboardManager.current
            Image(imageVector = Icons.Default.ContentCopy, contentDescription = null, modifier = Modifier.clickable {
                clipboardManager.setText(AnnotatedString(text))
            })
        },
    )
}
var debugNotificationId : Int = 1
@Composable
private fun SettingView(navController:NavController){
    Column {
        ToggleSwitch(
            title = "Show FTUE",
            initialValue = AuthenticationManager.instance.showLobbyFtue,
            valueSetFunction = { AuthenticationManager.instance.toggleLobbyFtue() },
            valueGetFunction = { AuthenticationManager.instance.showLobbyFtue },
        )
        ToggleSwitch(
            title = "Show Rewards FTUE",
            initialValue = AuthenticationManager.instance.showRewardsFtueDebug,
            valueSetFunction = { AuthenticationManager.instance.toggleRewardsFtue() },
            valueGetFunction = { AuthenticationManager.instance.showRewardsFtueDebug },
        )
        ToggleSwitch(
            title = "Braze Test Device",
            initialValue = BrazeManager.instance.getTestDevice(),
            valueSetFunction = { BrazeManager.instance.setTestDevice(!BrazeManager.instance.getTestDevice()) },
            valueGetFunction = { BrazeManager.instance.getTestDevice() },
        )
        Error400Selector(
            title = "Select 400 Error Code",
            initialValue = TournamentManager.instance.tournamentJoinErrorDebugOverride,
            onValueChange = { errorCode ->
                TournamentManager.instance.tournamentJoinErrorDebugOverride = errorCode
            }
        )
        DebugActionButton(
            title = "Test notification",
            action = {

                val context : Context = ActivityManager.instance.activity
                val notificationManager : NotificationManager = (context.getSystemService(Context.NOTIFICATION_SERVICE)) as NotificationManager;

                val notification: Notification = NotificationCompat.Builder(ActivityManager.instance.activity)
                    .setContentTitle("New Message")
                    .setContentText("You've received new messages.")
                    .setSmallIcon(resources.R.drawable.ic_launcher)
                    .setNumber(6)
                    .setChannelId("com_appboy_default_notification_channel")
                    .build()


                notificationManager.notify(Constants.BRAZE_PUSH_NOTIFICATION_TAG, debugNotificationId++, notification);

            }
        )
        DebugActionButton(
            title = "Clear Push Badges",
            action = {
                BrazeManager.instance.clearNotifications();
            }
        )
    }
}

// Data class for a processed log line.
data class LogEntry(val line: String, val effectiveLevel: Int?)

/**
 * Extracts the log level from a line if available.
 *
 * This regex matches the format "YYYY-MM-DD HH:MM:SS [LEVEL] message", as used in
 * [DiskLoggingTree].
 */
fun extractLogLevel(line: String): Int? {
    val regex = Regex("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2} \\[(\\d+)\\]")
    return regex.find(line)?.groupValues?.get(1)?.toIntOrNull()
}

// Processes raw log lines into LogEntry objects.
// Only includes logs that pass the search filter and minimum level filter.
fun processLogsWithLevels(
    logs: List<String>,
    minLevel: Int,
    filterQuery: String
): List<LogEntry> {
    // First, extract log levels for all lines.
    val entries = logs.map { line ->
        line to extractLogLevel(line)
    }
    // Then filter by the search query.
    val filtered = entries.filter { (line, _) ->
        filterQuery.isBlank() || line.contains(filterQuery, ignoreCase = true)
    }

    // Finally, assign log levels to continuation lines based on previous filtered entries.
    var lastLevel: Int? = null
    return filtered.mapNotNull { (line, level) ->
        if (level != null) lastLevel = level
        if (lastLevel != null && lastLevel!! <= minLevel)
            LogEntry(line, lastLevel)
        else
            null
    }
}

// Maps a log level to a color.
fun getColorForLogLevel(level: Int?): Color {
    return when (level) {
        0 -> Color(0xffcd0000) // assert
        1 -> Color(0xffcd0000) // error
        2 -> Color(0xFF645508) // warning
        3 -> Color(0xff5aa869) // info
        4 -> Color(0xff379fd6) // debug
        5 -> Color(0xFFA52A2A) // verbose
        else -> Color.Gray
    }
}

@Composable
private fun LogView(navController: NavController) {
    var filterQuery by remember { mutableStateOf("") }
    var minLogLevel by remember { mutableStateOf(4) }
    val logs by Logger.allLogs.collectAsState()
    val coroutineScope = rememberCoroutineScope()

    LaunchedEffect(Unit) {
        coroutineScope.launch {
            Logger.instance.loadAllLogs()
        }
    }

    val processedLogs = processLogsWithLevels(logs, minLogLevel, filterQuery)
    val listState = rememberLazyListState()

    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp)
                .height(64.dp),
        ) {
            TextField(
                value = filterQuery,
                label = { Text("Search:") },
                maxLines = 1,
                onValueChange = { filterQuery = it },
                modifier = Modifier.weight(1f)
            )

            Image(
                imageVector = Icons.Default.Close,
                contentDescription = "Clear",
                modifier = Modifier
                    .padding(start = 8.dp)
                    .fillMaxHeight()
                    .clickable { filterQuery = "" }
            )

            Image(
                imageVector = Icons.Default.ArrowDownward,
                contentDescription = "Go to Bottom",
                modifier = Modifier
                    .padding(start = 8.dp)
                    .fillMaxHeight()
                    .clickable {
                        coroutineScope.launch {
                            listState.animateScrollToItem(processedLogs.size - 1)
                        }
                    }
            )

            Image(
                imageVector = Icons.Default.Share,
                contentDescription = "Share Logs",
                modifier = Modifier
                    .padding(start = 8.dp)
                    .fillMaxHeight()
                    .clickable {
                        val logFile = Logger.instance.getCurrentLogFile()
                        if (logFile != null) {
                            Logger.instance.shareLogFile(logFile)
                        }
                    }
            )

            Image(
                imageVector = Icons.Default.Delete,
                contentDescription = "Delete",
                modifier = Modifier
                    .padding(start = 24.dp)
                    .fillMaxHeight()
                    .clickable { Logger.clearAll() }
            )
        }

        DropdownComponent(
            label = "Min Log Level",
            defaultOption = "Debug (4)",
            options = listOf("Assert (0)", "Error (1)", "Warning (2)", "Info (3)", "Debug (4)", "Verbose (5)"),
            onSelect = { option ->
                // Extract the number between parentheses.
                minLogLevel = option.substringAfter("(").substringBefore(")").toInt()
            }
        )

        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
                .padding(8.dp)
        ) {
            items(processedLogs) { logEntry ->
                Text(
                    text = buildHighlightText(logEntry.line, filterQuery),
                    color = getColorForLogLevel(logEntry.effectiveLevel),
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * Returns an AnnotatedString for the given line, bolding any occurrence
 * of the search term (case-insensitive).
 */
fun buildHighlightText(line: String, search: String): AnnotatedString {
    if (search.isBlank()) return AnnotatedString(line)
    return buildAnnotatedString {
        val lowerCaseLine = line.lowercase()
        val lowerCaseSearch = search.lowercase()
        var startIndex = 0
        while (true) {
            val index = lowerCaseLine.indexOf(lowerCaseSearch, startIndex)
            if (index == -1) {
                append(line.substring(startIndex))
                break
            }
            append(line.substring(startIndex, index))
            withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) {
                append(line.substring(index, index + search.length))
            }
            startIndex = index + search.length
        }
    }
}

@Composable
private fun AccountView(navController:NavController){
    Column {
        TextFieldWithCopyButton(AuthenticationManager.instance.apiMe?.username ?: "null","Account Name")
        TextFieldWithCopyButton(AuthenticationManager.instance.apiMe?.id ?: "null","Fanduel ID")
        TextFieldWithCopyButton(AuthenticationManager.instance.apiMe?.braze_external_id ?: "null","Braze ID")
        TextFieldWithCopyButton(PreferencesManager.instance.getWWAuthToken() ?: "null","WW Auth Token")
        TextFieldWithCopyButton(PreferencesManager.instance.getFanduelSessionDataToken() ?: "null","Fanduel SessionData Token")

        Button(onClick = {
            EventBus.getDefault().post(OnSessionChangedEvent(false, "DebugMenuScreen/Logout"))
        },enabled = AuthenticationManager.instance.apiMe != null,
            modifier = Modifier.fillMaxWidth()) {
            Text(text = "Log out")
        }
    }
}

@Composable
@Preview
fun DebugMenuScreenPreview(){
    DebugMenuScreen(mutableStateOf(true))
}