package com.gametaco.app_android_fd.models

import androidx.compose.runtime.Immutable
import com.gametaco.app_android_fd.data.entity.APIActiveGoal
import com.gametaco.app_android_fd.data.entity.APICollectedGoal
import com.gametaco.app_android_fd.data.entity.APICompletableGoal
import com.gametaco.app_android_fd.data.entity.APIGoalType
import com.gametaco.app_android_fd.data.entity.APIGoalsCollectableGoal

@Immutable
data class GoalDataModel(
    val completableGoals: List<APICompletableGoal>,
    val collectableGoals: List<APIGoalsCollectableGoal>,
    val collectedGoals: List<APICollectedGoal>
){
    val allGoals: List<APIActiveGoal> by lazy {
        completableGoals + collectableGoals + collectedGoals
    }

    val activeGoals: List<APIActiveGoal> by lazy {
        collectableGoals.filter { it.goal.type != APIGoalType.cluster.rawValue } + completableGoals
    }
    fun getCollectableClusterGoalByTaskId(taskId:String?):APIGoalsCollectableGoal?{
        if(taskId != null){
            val clusterGoals = collectableGoals.filter { it.goalType == APIGoalType.cluster }
            for (goal in clusterGoals){
                if(goal.goal_task_id == taskId){
                    return goal
                }
            }
        }
        return null
    }
}

