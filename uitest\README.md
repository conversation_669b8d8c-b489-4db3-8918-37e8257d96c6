**Pre-requisites (one time):**

1. **Appium & Drivers**
  - Install Appium 2.x globally via NPM:
    ```bash
    npm install -g appium
    ```
  - Install the Android driver (e.g. uiautomator2) and the Chromium driver:
    ```bash
    appium driver install uiautomator2
    appium driver install chromium
    ```

2. **Update Android Components**  
   Ensure both Chrome and the Android System WebView apps on the device are up to date (tested with Chrome 134):
  - Update Chrome via the [Google Play Store](https://play.google.com/store/apps/details?id=com.android.chrome)
  - Update System WebView via the [Google Play Store](https://play.google.com/store/apps/details?id=com.google.android.webview)

3. **Chromedriver Setup**
  - Download the Chromedriver binary that exactly matches your device’s Chrome/WebView version from:  
    [Chrome for Testing](https://googlechromelabs.github.io/chrome-for-testing/)
  - Set the environment variable `APPIUM_CHROME_DRIVER` to point to the downloaded Chromedriver executable.  
    For example:  
    `/Users/<USER>/drivers/chrome/chromedriver-mac-arm64-134.0.6998.90/chromedriver`
  - Ensure `ANDROID_HOME` is set to your Android SDK location.
  

**Pre-requisites (each time):**
1. An Android device should be running and connected to the machine.
   • An Android emulator works. 
2. The version of Fanduel Faceoff you want tested should be installed on the device. Alternatively, you can use the `-apk=` flag to install the app from a local APK file.


***To run the tests from Android Studio***:
1. Open the project in Android Studio.
2. Make sure an Android device is connected and visible in the "Running Devices" tab.
3. Navigate to [FanduelUiTest.kt](src/test/kotlin/com/worldwinner/fanduel/uitest/FanduelUiTest.kt)
4. Click the Green Play button next to `class FanduelUiTest {` and select `Run 'FanduelUiTest'` from the dropdown menu.

***To run the tests from the command line using the new script:***

1. Ensure the script has execute permissions. If not, run:
```bash
chmod +x run_ui_tests.sh
```
2. Run the script:
`./run_ui_tests.sh`
To specify a custom APK, pass it as an argument:
`./run_ui_tests.sh -apk=/path/to/apk.apk`
If no APK is specified, the tests will use the device’s currently installed Fanduel Faceoff APK.

**Notes**:
* You do not have to start Appium manually. The tests will start Appium automatically.
* The APK path can be overridden either via the Gradle property (apkPath) when launching from Android Studio or via the command line script.