#!/bin/zsh

export PATH="$PATH:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0"

# Keystore information
SOURCE_APK_PATH="$1"
BUILD_CONFIG="$2"

# Extract versionCode and versionName from the signed APK
VERSION_NAME=$(aapt dump badging $SOURCE_APK_PATH | grep "versionName" | sed -e "s/.*versionName='//" -e "s/' .*//")
VERSION_CODE=$(aapt dump badging $SOURCE_APK_PATH | grep "versionCode" | sed -e "s/.*versionCode='//" -e "s/' .*//")
echo "Version Name: $VERSION_NAME, Version Code: $VERSION_CODE"

# Construct the APK name
OUT_APK_NAME="FDLauncher-${BUILD_CONFIG}-${VERSION_CODE}-${VERSION_NAME}.apk"
echo $OUT_APK_NAME

aws s3 cp "$SOURCE_APK_PATH" s3://faceoff-android-dev-release/${OUT_APK_NAME}

WEBHOOK_MSG="Android ${BUILD_CONFIG} build ${VERSION_CODE} is available on S3"
curl -H 'Content-Type: application/json' -d "{\"text\": \"$WEBHOOK_MSG\"}" "https://kromestudios.webhook.office.com/webhookb2/05a88d58-8ef6-40ee-af9d-9d9f6a0574ba@a75bb70f-d2b8-4001-ba57-9c936408ef0e/IncomingWebhook/b92beb3fb0c348c7bf0f61ad7f982bc2/218820b0-a710-4340-9b20-1e5ebb9766d1"