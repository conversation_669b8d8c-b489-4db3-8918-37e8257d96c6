package com.gametaco.app_android_fd.manager

import android.app.Activity
import android.app.Application.ActivityLifecycleCallbacks
import android.content.Context
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.gametaco.app_android_fd.MainActivity
import com.gametaco.app_android_fd.MainApplication
import com.gametaco.app_android_fd.UnityWrapperInterface
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.CoroutineScopes
import com.salesforce.android.chat.ui.internal.chatfeed.ChatFeedActivity
import com.salesforce.android.chat.ui.internal.prechat.PreChatActivity
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.stateIn

class ActivityManager(
    private val context: Context,
    private val coroutineScopes: CoroutineScopes,
) {
    companion object {
        val instance: ActivityManager
            get() = resolve()
    }

    private var _activity: AppCompatActivity? = null
    val activity: AppCompatActivity
        get() = _activity ?: throw IllegalStateException("Activity is not registered")
    val unityWrapper: UnityWrapperInterface
        get() = activity as UnityWrapperInterface
    val uiManager: UIManager
        get() = activity as UIManager

    private val _activityStack = MutableStateFlow<List<Activity>>(emptyList())
    val activityStack: StateFlow<List<Activity>>
        get() = _activityStack
    val currentActivity: StateFlow<Activity?> = activityStack
        .mapNotNull { it.lastOrNull() }
        .distinctUntilChanged()
        .stateIn(
            scope = coroutineScopes.main,
            started = SharingStarted.Eagerly,
            initialValue = null,
        )

    val mainActivityIsForeground: Boolean
        get() = _mainActivityIsForeground
    private var _mainActivityIsForeground = false

    fun registerMainActivity(activity: AppCompatActivity) {
        this._activity = activity
    }

    fun unregisterMainActivity(activity: AppCompatActivity) {
        if (this._activity == activity) {
            this._activity = null
        }
    }

    fun initialize(application: MainApplication) {
        application.registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            }

            override fun onActivityDestroyed(activity: Activity) {
                updateStack { it.filterNot { it === activity } }

                if(activity is MainActivity) {
                    _mainActivityIsForeground = false
                }
            }
            override fun onActivityStarted(activity: Activity) {
                updateStack { it.filterNot { it === activity } + activity }

                if(activity is MainActivity) {
                    _mainActivityIsForeground = true
                }
            }
            override fun onActivityResumed(activity: Activity) {
                updateStack { it.filterNot { it === activity } + activity }

                if(activity is MainActivity) {
                    _mainActivityIsForeground = true
                }
            }
            override fun onActivityPaused(activity: Activity) {
                if (activity is MainActivity) {
                    _mainActivityIsForeground = false
                }
            }
            override fun onActivityStopped(activity: Activity) {
                updateStack { it.filterNot { it === activity } }

                if (activity is MainActivity) {
                    _mainActivityIsForeground = false
                }
            }
            override fun onActivityPreDestroyed(activity: Activity) {
                if (activity is MainActivity) {
                    _mainActivityIsForeground = false
                    ExperimentManager.instance.OnShutdown()
                }
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            }

            private fun updateStack(block: (List<Activity>) -> List<Activity>) {
                val newStack = block(_activityStack.value)
                val deduped = newStack
                    .asReversed()
                    .distinctBy { it }
                    .asReversed()
                _activityStack.value = deduped
            }
        })
    }

    fun isCurrentActivityUmRelated(): Boolean {
        val activity = currentActivity.value
        if (activity == null) {
            return false
        }

        val activityName = activity.javaClass.name

        // These activities are package private, so we have to fallback to checking the class name
        return activityName == "com.fanduel.core.libs.modalpresenter.ModalActivity"
                || activityName == "com.fanduel.core.libs.modalpresenter.AlertActivity"
                || activity is PreChatActivity
                || activity is ChatFeedActivity
    }
}

