//
//  AppsFlyerManager.kt
//
//  Copyright © 2024 Game Taco, Inc.
//
import android.content.Intent
import android.net.Uri
import com.appsflyer.AppsFlyerConversionListener
import com.appsflyer.AppsFlyerLib
import com.appsflyer.attribution.AppsFlyerRequestListener
import com.appsflyer.deeplink.DeepLink
import com.appsflyer.deeplink.DeepLinkListener
import com.appsflyer.deeplink.DeepLinkResult
import com.gametaco.app_android_fd.BuildConfig
import com.gametaco.app_android_fd.MainApplication.Companion.context
import com.gametaco.app_android_fd.data.AppConstants
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.manager.DeepLinkManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.utils.log.Logger

class AppsFlyerManager {
    companion object {
        val instance: AppsFlyerManager
            get() = resolve()

        const val TAG = "AppsFlyerManager"
        const val AF_STATUS = "af_status"
        const val AF_PUSH_LINK = "af_push_link"
        const val DEEP_LINK_VALUE = "deep_link_value"
        const val IS_FIRST_LAUNCH = "is_first_launch"
    }

    var conversionData: Map<String, Any>? = null

    // This flag signals between the UDL and GCD callbacks that this deep_link was
    // already processed, and the callback functionality for deep linking can be skipped.
    // When GCD or UDL finds this flag true it MUST set it to false before skipping.
    var deferredDeepLinkProcessed = false

    private val apiKey = AppConstants.APPSFLYER_API_KEY
    private val appId = AppConstants.APPSFLYER_APP_ID
    private val appsFlyerLib: AppsFlyerLib = AppsFlyerLib.getInstance()

    fun initialise() {
        appsFlyerLib.setDebugLog(true)
        appsFlyerLib.setMinTimeBetweenSessions(0)
        appsFlyerLib.setAppId(appId)
        appsFlyerLib.setDebugLog(BuildConfig.DEBUG)
        initDeepLinking()
        appsFlyerLib.init(apiKey, conversionDataListener, context)
        appsFlyerLib.start(context, apiKey, object :
            AppsFlyerRequestListener {
            override fun onSuccess() {
                Logger.d(TAG, "Launch sent successfully")
            }
            override fun onError(errorCode: Int, errorDesc: String) {
                Logger.d(TAG, "Launch failed to be sent:\nError code: $errorCode\nError description: $errorDesc")
            }
        })
    }

    fun stop() {
        Logger.d(TAG, "Stopping AppsFlyer")
        appsFlyerLib.stop(true, context)
    }

    private fun initDeepLinking() {
        //appsFlyerLib.setAppInviteOneLink("fdfaceoff.onelink.me")
        //appsFlyerLib.setResolveDeepLinkURLs("", "")
        appsFlyerLib.addPushNotificationDeepLinkPath(AF_PUSH_LINK);
        appsFlyerLib.subscribeForDeepLink(object : DeepLinkListener {
            override fun onDeepLinking(deepLinkResult: DeepLinkResult) {
                when (deepLinkResult.status) {
                    DeepLinkResult.Status.FOUND -> {
                        Logger.d(TAG, "Deep link found")
                    }
                    DeepLinkResult.Status.NOT_FOUND -> {
                        Logger.d(TAG, "Deep link not found")
                        return
                    }
                    else -> {
                        // dlStatus == DeepLinkResult.Status.ERROR
                        val dlError = deepLinkResult.error
                        Logger.d(TAG, "There was an error getting Deep Link data: $dlError")
                        return
                    }
                }
                val deepLinkObj: DeepLink = deepLinkResult.deepLink
                try {
                    Logger.d(TAG, "The DeepLink data is: $deepLinkObj")
                } catch (e: Exception) {
                    Logger.d(TAG, "DeepLink data came back null")
                    return
                }

                // Handle deferred deep link
                if (deepLinkObj.isDeferred == true) {
                    Logger.d(TAG, "This is a deferred deep link")
                    if (deferredDeepLinkProcessed) {
                        Logger.d(TAG,"Deferred deep link was already processed by GCD. This iteration can be skipped.")
                        deferredDeepLinkProcessed = false
                        return
                    }
                } else {
                    Logger.d(TAG, "This is a direct deep link")
                }
                Logger.d(TAG, deepLinkObj.toString())
                try {
                    var deepLinkValue = deepLinkObj.deepLinkValue
                    if (deepLinkValue.isNullOrEmpty()) {
                        Logger.d(TAG, "deepLinkValue is null, trying string value")
                        deepLinkValue = deepLinkObj.getStringValue(DEEP_LINK_VALUE)
                        if (deepLinkValue.isNullOrEmpty()) {
                            Logger.d(TAG, "Could not find Deep Link Value!")
                            return
                        }
                        Logger.d(TAG, "$DEEP_LINK_VALUE is $deepLinkValue [This is an old link]")
                    }
                    Logger.d(TAG, "The DeepLink will route to: $deepLinkValue")
                    // This marks to GCD that UDL already processed this deep link.
                    // It is marked to both DL and DDL, but GCD is relevant only for DDL
                    deferredDeepLinkProcessed = true
                    goToDeeplink(deepLinkValue, deepLinkObj)
                } catch (e: Exception) {
                    Logger.d(TAG, "onDeepLinking: There's been an error: $e")
                    return
                }
            }
        })
    }

    private fun goToDeeplink(deepLinkValue: String?, dlData: DeepLink?) {
        if (deepLinkValue == null) {
            Logger.d(TAG, "goToDeeplink: DeepLink value is null")
            return
        }

        var intent : Intent = Intent(Intent.ACTION_VIEW).apply {
            data = Uri.parse(deepLinkValue)
            addCategory(Intent.CATEGORY_DEFAULT)
            addCategory(Intent.CATEGORY_BROWSABLE)
        }

        DeepLinkManager.instance.handleIntent(intent)
    }

//    private val conversionListener: AppsFlyerConversionListener = object : AppsFlyerConversionListener {
//        override fun onConversionDataSuccess(data: MutableMap<String, Any>?) {
//            data?.let {
//                val status: Any? = data[AF_STATUS]
//                Logger.d(TAG, "::$status")
//                Logger.d(TAG, "Conversion Data: $data")
//                if (status.toString() == "Non-organic") {
//                    if (data[IS_FIRST_LAUNCH] == true) {
//                        Logger.d(TAG, "First time launching")
//                        //Deferred deep link in case of a legacy link
//                        if (deferredDeepLinkProcessed) {
//                            Logger.d(TAG, "Deferred deep link was already processed by UDL. The DDL processing in GCD can be skipped.")
//                            deferredDeepLinkProcessed = false
//                        } else {
//                            deferredDeepLinkProcessed = true
//                            val deepLinkValue: String = data[DEEP_LINK_VALUE].toString()
//                            val deepLink: DeepLink? = mapToDeepLinkObject(data)
//                            goToDeeplink(deepLinkValue, deepLink)
//                        }
//                    } else {
//                        Logger.d(TAG, "Conversion: Not First Launch")
//                    }
//                } else {
//                    Logger.d(TAG, "Conversion: This is an organic install.")
//                }
//
//                conversionData = data
//            } ?: run {
//                Logger.d(TAG, "Conversion Failed")
//            }
//        }
//
//        override fun onConversionDataFail(errorMessage: String?) {
//            if (errorMessage != null) {
//                Logger.d(TAG, "onConversionDataFail: $errorMessage")
//            }
//        }
//
//        override fun onAppOpenAttribution(p0: MutableMap<String, String>?) {
//            Logger.d(TAG, "onAppOpenAttribution: This is fake call.")
//        }
//
//        override fun onAttributionFailure(errorMessage: String?) {
//            Logger.d(TAG, "onAttributionFailure: $errorMessage")
//        }
//    }
//
//    private fun mapToDeepLinkObject(conversionDataMap: Map<String, Any>): DeepLink? {
//        try {
//            val stringMap: MutableMap<String, String> = HashMap()
//            for ((key, value) in conversionDataMap) {
//                stringMap[key] = value.toString()
//            }
//            return DeepLink.values(stringMap)
//        } catch (e: JSONException) {
//            Logger.d(TAG, "Error when converting map to DeepLink object: $e")
//        }
//        return null
//    }
//
//    private fun gotoUri(context: Context, uriAction: UriAction) {
//        val uri = uriAction.uri
//        Logger.d(TAG, "gotoUri: $uri")
//        DeepLinkManager.instance.handleUri(uri)
//    }

    fun setUserId(userId: Int?) {
        appsFlyerLib.setCustomerUserId(userId?.toString() ?: "")
    }

    fun logEvent(event: AnalyticsEvent) {
        if (event.analyticsEvent.isNotEmpty()) {
            Logger.d(TAG, "logEvent: ${event.analyticsEvent}, ${event.properties}")
            appsFlyerLib.logEvent(context, event.analyticsEvent, event.properties)
        }
    }

    var conversionDataListener: AppsFlyerConversionListener = object : AppsFlyerConversionListener {
        override fun onConversionDataSuccess(map: Map<String, Any>) {}

        override fun onConversionDataFail(s: String) {
        }

        override fun onAppOpenAttribution(conversionData: Map<String, String>) {
        }

        override fun onAttributionFailure(errorMessage: String) {
        }
    }
}
