package com.gametaco.app_android_fd.ui.popups

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.gametaco.app_android_fd.LocalAlertDialogManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.FDWebviewPage
import com.gametaco.app_android_fd.models.DialogData
import com.gametaco.app_android_fd.ui.components.Background
import com.gametaco.app_android_fd.ui.components.CommonButton
import com.gametaco.app_android_fd.ui.screens.PreviewRoot
import com.gametaco.app_android_fd.ui.theme.AppandroidwwTheme
import com.gametaco.app_android_fd.ui.theme.BlueMinimalBackground
import com.gametaco.app_android_fd.ui.theme.Typography
import com.gametaco.app_android_fd.ui.theme.theme_light_primary
import com.gametaco.app_android_fd.ui.theme.theme_light_secondary
import com.gametaco.app_android_fd.ui.theme.theme_light_tertiary
import com.gametaco.app_android_fd.ui.utils.getNavigationBarHeight
import com.gametaco.utilities.STR
import resources.R

@Composable
fun AlertDialogPopup(dialogData: DialogData?) {
    if (dialogData == null) return
    val alertDialogManager = LocalAlertDialogManager.current

    if(dialogData.composeFunction != null) {
        dialogData.composeFunction?.invoke()
    } else {
        Dialog(onDismissRequest = {
// Commented out as dialogs are not to dismiss unless buttons are clicked
//            alertDialogManager.dismissDialog()
        }) {
            Surface(
                shape = MaterialTheme.shapes.medium, // This is optional, for rounded corners
                modifier = Modifier.widthIn(max = 300.dp)
            ) {
                Column(
                    modifier = Modifier.padding(all = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = dialogData.title?:"",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(20.dp))
                    Text(
                        text = dialogData.message?:"",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(20.dp))

                    CommonButton(
                        title = dialogData.confirmButtonText?:"",
                        fillColor = theme_light_tertiary,
                        modifier = Modifier.fillMaxWidth()
                    )
                    {
                        dialogData.onConfirm?.invoke()
                        alertDialogManager.dismissDialog()
                    }

                    if (dialogData.onCancel != null) {
                        Spacer(modifier = Modifier.height(20.dp))
                        CommonButton(
                            title = dialogData.cancelButtonText ?: STR(R.string.cancel),
                            fillColor = theme_light_secondary,
                            modifier = Modifier.fillMaxWidth()
                        )
                        {
                            dialogData.onCancel?.invoke()
                            alertDialogManager.dismissDialog()
                        }
                    }
                }
            }
        }
    }
}



@Composable
fun AlertDialogButtonsSideBySide(title: String? = null,
                                 message: String? = null,
                                 aButtonText: String? = null,
                                 onAPressed: (() -> Unit)? = null,
                                 bButtonText: String? = null,
                                 onBPressed: (() -> Unit)? = null
                                 ) {

    val alertDialogManager = LocalAlertDialogManager.current

    Dialog(onDismissRequest = {
// Commented out as dialogs are not to dismiss unless buttons are clicked
//        alertDialogManager.dismissDialog()
    } ) {
        Surface(
            shape = MaterialTheme.shapes.medium, // This is optional, for rounded corners
            modifier = Modifier.widthIn(max = 300.dp)
        ) {
            Column(
                modifier = Modifier.padding(all = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = title?:"",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = message?:"",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(20.dp))

                Row(
                    horizontalArrangement = Arrangement.Absolute.spacedBy(
                        space = 12.dp,
                        alignment = Alignment.CenterHorizontally
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                ) {
                    CommonButton(title = aButtonText?:"", fillColor = theme_light_primary, modifier = Modifier.weight(1f))
                    {
                        onAPressed?.invoke()
                        alertDialogManager.dismissDialog()
                    }

                    Spacer(modifier = Modifier.width(20.dp))
                    CommonButton(title = bButtonText?:"", fillColor = theme_light_tertiary, modifier = Modifier.weight(1f))
                    {
                        onBPressed?.invoke()
                        alertDialogManager.dismissDialog()
                    }
                }
            }
        }
    }
}


@Composable
fun AlertDialogTechnicalProblemsFD(title: String? = null,
                                 message: String? = null,
                                 aButtonText: String? = null,
                                 onAPressed: (() -> Unit)? = null,
                                 bButtonText: String? = null,
                                 onBPressed: (() -> Unit)? = null
) {

    val alertDialogManager = LocalAlertDialogManager.current


    Dialog(onDismissRequest = {
    } ) {
        Surface(
            shape = MaterialTheme.shapes.small, // This is optional, for rounded corners
            modifier = Modifier.widthIn(max = 300.dp)
        ) {
            Column(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = title?:"",
                    style = MaterialTheme.typography.titleMedium.copy(fontSize = 18.sp, fontWeight = FontWeight.W700),
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = message?:"",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp, fontWeight = FontWeight.W400),
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(20.dp))

                    CommonButton(title = aButtonText?:"", fillColor = theme_light_tertiary, modifier = Modifier.fillMaxWidth())
                    {
                        onAPressed?.invoke()
                        alertDialogManager.dismissDialog()
                    }

                    Spacer(modifier = Modifier.height(20.dp))


                val bButtonTextAnnotated: AnnotatedString = buildAnnotatedString {
                    withStyle(
                        style = Typography.bodyLarge.toSpanStyle()
                            .copy(color = Color.Red)
                    )
                    {
                        append(bButtonText?:"")
                    }
                }

                ClickableText(text = bButtonTextAnnotated, onClick =
                {
                    onBPressed?.invoke()
                    alertDialogManager.dismissDialog()
                })

            }
        }
    }
}



@Composable
fun AlertDialogTextCancel(title: String? = null,
                                   message: String? = null,
                                   aButtonText: String? = null,
                                   onAPressed: (() -> Unit)? = null,
                                   bButtonText: String? = null,
                                   onBPressed: (() -> Unit)? = null
) {

    val alertDialogManager = LocalAlertDialogManager.current


    Dialog(onDismissRequest = {
    } ) {
        Surface(
            shape = MaterialTheme.shapes.small, // This is optional, for rounded corners
            modifier = Modifier.widthIn(max = 300.dp)
        ) {
            Column(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = title?:"",
                    style = MaterialTheme.typography.titleMedium.copy(fontSize = 18.sp, fontWeight = FontWeight.W700),
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = message?:"",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp, fontWeight = FontWeight.W400),
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(20.dp))

                CommonButton(title = aButtonText?:"", fillColor = theme_light_tertiary, modifier = Modifier.fillMaxWidth())
                {
                    onAPressed?.invoke()
                    alertDialogManager.dismissDialog()
                }

                Spacer(modifier = Modifier.height(20.dp))


                val bButtonTextAnnotated: AnnotatedString = buildAnnotatedString {
                    withStyle(
                        style = Typography.bodyLarge.toSpanStyle()
                            .copy(color = Color.Red)
                    )
                    {
                        append(bButtonText?:"")
                    }
                }

                ClickableText(text = bButtonTextAnnotated, onClick =
                {
                    onBPressed?.invoke()
                    alertDialogManager.dismissDialog()
                })

            }
        }
    }
}


@Composable
fun AlertDialogRestrictedStatus(   onAPressed: (() -> Unit)? = null,
                                   onBPressed: (() -> Unit)? = null
) {

    val alertDialogManager = LocalAlertDialogManager.current
    Background(BlueMinimalBackground)

    Column (Modifier.pointerInput(Unit) { /* Prevent click through / drag through to page behind */ }) {
        Column (
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(
                space = 16.dp,
                alignment = Alignment.Top
            ),
            modifier = Modifier
                .padding(PaddingValues(24.dp, 48.dp, 24.dp, 16.dp))
                .weight(1f, false)
                .fillMaxSize()
        ){

            Text(
                text = STR(R.string.restricted_heading),
                style = MaterialTheme.typography.titleMedium.copy(fontSize = 22.sp),
                color = Color.White,
                textAlign = TextAlign.Center,
            )

            Image(
                painter = painterResource(R.drawable.ic_geo_warning),
                alignment = Alignment.TopCenter,
                contentScale = ContentScale.FillWidth,
                contentDescription = null,
                modifier = Modifier.size(200.dp, 136.dp)
            )

            Text(
                text = STR(R.string.restricted_description),
                style = MaterialTheme.typography.bodySmall.copy(fontSize = 12.sp),
                color = Color.White,
                textAlign = TextAlign.Left,
            )


            Text(
                text = STR(R.string.restricted_faqs),
                style = MaterialTheme.typography.titleMedium.copy(fontSize = 16.sp),
                color = Color.White,
                textAlign = TextAlign.Left,
                modifier = Modifier
                    .fillMaxWidth()
            )

            LazyColumn(
                modifier = Modifier
                    .fillMaxSize(),
                    verticalArrangement = Arrangement.spacedBy(
                        space = 8.dp,
                        alignment = Alignment.Top)
            ) {

                item {


                    Box(
                        modifier = Modifier
                            .clip(RoundedCornerShape(8.dp))
                            .background(color = Color(0xFF005FC8))
                            .fillMaxWidth()
                            .height(170.dp)
                    ) {

                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(
                                space = 8.dp,
                                alignment = Alignment.Top
                            ),
                            modifier = Modifier
                                .padding(PaddingValues(8.dp, 8.dp))
                                .fillMaxSize()
                        ) {

                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Absolute.spacedBy(
                                    space = 12.dp,
                                    alignment = Alignment.CenterHorizontally
                                ),
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .wrapContentHeight()
                            ) {

                                Image(
                                    painter = painterResource(R.drawable.ic_info),
                                    contentDescription = null,
                                    colorFilter = ColorFilter.tint(Color.White),
                                    modifier = Modifier.size(20.dp)
                                )

                                Text(
                                    text = STR(R.string.restricted_what_happened),
                                    style = MaterialTheme.typography.titleMedium.copy(fontSize = 12.sp),
                                    color = Color.White,
                                    textAlign = TextAlign.Left,
                                    modifier = Modifier
                                        .fillMaxWidth()

                                )
                            }

                            Text(
                                text = STR(R.string.restricted_what_happened_desc),
                                style = MaterialTheme.typography.bodySmall.copy(fontSize = 12.sp),
                                color = Color.White,
                                textAlign = TextAlign.Left,
                                modifier = Modifier
                                    .fillMaxWidth()
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Box(
                        modifier = Modifier
                            .clip(RoundedCornerShape(8.dp))
                            .background(color = Color(0xFF005FC8))
                            .fillMaxWidth()
                            .height(170.dp)
                    ) {

                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(
                                space = 8.dp,
                                alignment = Alignment.Top
                            ),
                            modifier = Modifier
                                .padding(PaddingValues(8.dp, 8.dp))
                                .fillMaxSize()
                        ) {

                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Absolute.spacedBy(
                                    space = 12.dp,
                                    alignment = Alignment.CenterHorizontally
                                ),
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .wrapContentHeight()
                            ) {

                                Image(
                                    painter = painterResource(R.drawable.ic_info),
                                    contentDescription = null,
                                    colorFilter = ColorFilter.tint(Color.White),
                                    modifier = Modifier.size(20.dp)
                                )

                                Text(
                                    text = STR(R.string.restricted_how_to_fix),
                                    style = MaterialTheme.typography.titleMedium.copy(fontSize = 12.sp),
                                    color = Color.White,
                                    textAlign = TextAlign.Left,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                )
                            }

                            val secondMessageString = buildAnnotatedString {
                                append(
                                    STR(R.string.restricted_how_to_fix_desc_0)

                                )
                                withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) {
                                    append(" " + STR(R.string.restricted_how_to_fix_desc_1) + " ")
                                }
                                append(STR(R.string.restricted_how_to_fix_desc_2))
                                withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) {
                                    append(" " + STR(R.string.restricted_how_to_fix_desc_3) + " ")
                                }
                                append(STR(R.string.restricted_how_to_fix_desc_4))
                            }

                            Text(
                                text = secondMessageString,
                                style = MaterialTheme.typography.bodySmall.copy(fontSize = 12.sp),
                                color = Color.White,
                                textAlign = TextAlign.Left,
                                modifier = Modifier
                                    .fillMaxWidth()
                            )
                        }
                    }
                }
            }


        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(108.dp)
                .background(color = Color(0xFF005FC8))
        )
        {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(start = 48.dp, top = 24.dp, end = 48.dp, bottom = 8.dp),

                verticalArrangement = Arrangement.Absolute.spacedBy(8.dp, Alignment.CenterVertically),
                horizontalAlignment = Alignment.CenterHorizontally,
            )
            {
                CommonButton(title = STR(R.string.add_funds), fillColor = Color(0xFF128000), hasShadow = true) {
                    alertDialogManager.dismissDialog()
                    onAPressed?.invoke()
                }
                Box(
                    modifier = Modifier.fillMaxSize()
                )
                {
                    Text(
                        text = STR(R.string.dismiss),
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.White,
                        modifier = Modifier
                            .clickable {
                                alertDialogManager.dismissDialog()
                                onBPressed?.invoke()
                            }
                            .align(Alignment.Center)
                            .padding(0.dp)
                    )
                }
            }
        }
        Box(
            modifier = Modifier
                .height(getNavigationBarHeight())
                .fillMaxWidth()
                .background(color = Color(0xFF005FC8)),
        ) {}
    }

}



@Composable
@Preview
fun AlertDialogPreview(){

    PreviewRoot {
        AppandroidwwTheme {
            AlertDialogRestrictedStatus(onAPressed = {
                },
                onBPressed = {
                    FDManager.instance.showWebviewFD(FDWebviewPage.Support)
                })
        }
    }

}