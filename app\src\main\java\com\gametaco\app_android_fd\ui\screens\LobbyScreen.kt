package com.gametaco.app_android_fd.ui.screens

import PullRefreshIndicator
import TurboPopup
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.times
import com.gametaco.app_android_fd.LocalNavManager
import com.gametaco.app_android_fd.LocalUIManager
import com.gametaco.app_android_fd.data.AppConstants
import com.gametaco.app_android_fd.data.entity.APIGamesResponse
import com.gametaco.app_android_fd.data.entity.APIGamesResponseGame
import com.gametaco.app_android_fd.data.entity.APITournament
import com.gametaco.app_android_fd.data.entity.GCGameData
import com.gametaco.app_android_fd.data.entity.GCRowItem
import com.gametaco.app_android_fd.data.entity.GameCatalogResponse
import com.gametaco.app_android_fd.data.entity.RowStyles
import com.gametaco.app_android_fd.data.entity.RowTypes
import com.gametaco.app_android_fd.data.navigation.NavigationData
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.BrazeEventName
import com.gametaco.app_android_fd.manager.BrazeManager
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.SystemBarState
import com.gametaco.app_android_fd.manager.WalletManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.ui.components.BonusCashBanner
import com.gametaco.app_android_fd.ui.components.BrazeCarousel
import com.gametaco.app_android_fd.ui.components.ContentBox
import com.gametaco.app_android_fd.ui.components.ContestDetail
import com.gametaco.app_android_fd.ui.components.ErrorElement
import com.gametaco.app_android_fd.ui.components.FeaturedContest
import com.gametaco.app_android_fd.ui.components.FeaturedTournamentRow
import com.gametaco.app_android_fd.ui.components.FtueBanner
import com.gametaco.app_android_fd.ui.components.GamePreview
import com.gametaco.app_android_fd.ui.components.GameTileGrid
import com.gametaco.app_android_fd.ui.components.GameTileRowScrollable
import com.gametaco.app_android_fd.ui.components.NavBar
import com.gametaco.app_android_fd.ui.components.TaggedTile
import com.gametaco.app_android_fd.ui.components.TileGroupTitle
import com.gametaco.app_android_fd.ui.components.TopBarLogo
import com.gametaco.app_android_fd.ui.theme.theme_light_background
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.viewmodel.GamesViewModel
import com.gametaco.app_android_fd.viewmodel.LoginViewModel
import com.gametaco.app_android_fd.viewmodel.PlayViewModel
import com.gametaco.utilities.ResourceState
import com.gametaco.utilities.STR
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import pullRefresh
import rememberPullRefreshState
import resources.R

const val TAG_LOBBY_SCREEN = "LobbyScreen"

@Composable
fun LobbyScreen(
    gamesViewModel: GamesViewModel,
    playViewModel: PlayViewModel
) {
    val TAG = TAG_LOBBY_SCREEN
    val uiManager = LocalUIManager.current
    val coroutineScope = rememberCoroutineScope()
    val listState = rememberLazyListState()

    LaunchedEffect(Unit) {
        uiManager.setSystemBarState(SystemBarState.TransparentLightStatusBarIcons)
        gamesViewModel.loadData()
        BrazeManager.instance.logEvent(BrazeEventName.Returned_To_Lobby.value)

        gamesViewModel.uiEvent.collect { event ->
            when (event) {
                is GamesViewModel.UiEvent.ScrollToTop -> {
                    coroutineScope.launch {
                        listState.scrollToItem(0)
                    }
                }
            }
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            //set loading on ResourceStates so that the previous state of the lobby isn't seen for a frame when navigating to it
//            gamesViewModel.setLoading()
        }
    }

    val navManager = LocalNavManager.current
    val gamesCatalogResponse by gamesViewModel.gameCatalog.collectAsState()
    val gamesDataResponse by gamesViewModel.gamesData.collectAsState()
    val recentlyPlayedGames by gamesViewModel.recentlyPlayedGames.collectAsState()
    val featuredTournaments by gamesViewModel.featuredTournaments.collectAsState()
    val dataReady by gamesViewModel.dataReady.collectAsState()

    val combinedState by combine(
        gamesViewModel.gameCatalog,
        gamesViewModel.gamesData,
    ) { catalog, data ->
        Pair(catalog, data)
    }.collectAsState(initial = Pair(gamesCatalogResponse, gamesDataResponse,))

    var selectedGameCatalogData: GCGameData? by remember { mutableStateOf(null) }
    var selectedGameData: APIGamesResponseGame? by remember { mutableStateOf(null) }
    val infoModalExpanded = remember { mutableStateOf(false) }
    val gameModalExpanded = remember { mutableStateOf(false) }
    val turboViewed = remember { mutableStateOf(PreferencesManager.instance.getIsTurboViewed()) }
    val experimentsReady by ExperimentManager.instance.experimentsReady.collectAsState()

    var isTurboActive = true
//    if(experimentsReady){
//        isTurboActive = ExperimentManager.instance.turbosMode?.isAvailable() == false || ExperimentManager.instance.turbosMode?.isActive() == true
////        println("isTurboActive:"+isTurboActive)
//    }

    //off by default for everyone
    var isTurboPopupActive = false
//    if(experimentsReady && !AuthenticationManager.instance.isGuest){ //only on for non guests and flagged users
//        isTurboPopupActive = ExperimentManager.instance.turbosMode?.isAvailable() == true && ExperimentManager.instance.turbosMode?.isActive() == true
////        println("isTurboPopupActive:"+isTurboPopupActive)
//    }



    var currentTournamentData : APITournament? by remember { mutableStateOf(null) }
    val wallet by WalletManager.instance.wallet.collectAsState()
    val cards by BrazeManager.instance.cards.collectAsState()
    val isGuest = AuthenticationManager.instance.isGuest
    val isFtue = AuthenticationManager.instance.showLobbyFtue
    val ftueGamesTitle = if(isGuest) STR(R.string.choose_a_game) else STR(R.string.browse_games)
    val ftueGamesSubtitle = if (isGuest) STR(R.string.try_a_free_practice_game_guest) else
        STR(R.string.try_a_free_practice_game_before_facing_another_player)



    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    // 4 tiles, 16dp padding on left, 16dp between items, 16 dp padding on right
    val gameTileItemWidth = (screenWidth - 16.dp - (3 * 16.dp) - 16.dp) / 4
    val featuredBannerWidth = screenWidth - 2 * 16.dp

    val refreshScope = rememberCoroutineScope()
    var refreshing by remember { mutableStateOf(false) }

    fun refresh() = refreshScope.launch {
        refreshing = true
        gamesViewModel.loadData()
        refreshing = false
    }
    val state = rememberPullRefreshState(refreshing, ::refresh)

    Scaffold (
        topBar = { TopBarLogo() },
        bottomBar = {
            if(!isFtue ||
                combinedState.first is ResourceState.Error ||
                combinedState.second is ResourceState.Error
            ){
                NavBar(NavigationData.instance, navManager)
            }
        },
    ){ paddingValues ->
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .pullRefresh(state = state)
                .background(color = theme_light_background)
                .padding(paddingValues)
        ){
            Box(modifier = Modifier
                .pullRefresh(state = state)
                .fillMaxSize()
                .background(color = theme_light_background)) {
                when {
                    combinedState.first is ResourceState.Loading || combinedState.second is ResourceState.Loading -> {
                        Logger.d(TAG, "Inside Loading")
                        LoadingScreen()
                    }

                    (combinedState.first is ResourceState.Success && combinedState.second is ResourceState.Success) ||
                    (combinedState.first is ResourceState.Error || combinedState.second is ResourceState.Error) -> {

                        var catalogResponse: GameCatalogResponse? = null
                        var gameDataResponse: APIGamesResponse? = null

                        if (combinedState.first is ResourceState.Success && combinedState.second is ResourceState.Success){
                            Logger.d(TAG, "Inside Success")

                            catalogResponse = (combinedState.first as ResourceState.Success).data
                            gameDataResponse = (combinedState.second as ResourceState.Success).data
                        } else {
                            Logger.d(TAG, "Inside Failure")
                        }

                        if (catalogResponse == null || gameDataResponse == null){
                            Logger.d(TAG, "Lobby catalog failed to load")

                            val errorString: String
                            if (combinedState.first is ResourceState.Error) {
                                val errorResponse = combinedState.first as ResourceState.Error
                                errorString = errorResponse.error
                            } else {
                                val errorResponse = combinedState.second as ResourceState.Error
                                errorString = errorResponse.error
                            }

                            if(!gamesViewModel.setCachedGames()){
                                ErrorElement(errorString) {
                                    gamesViewModel.loadData()
                                }
                            }
                        }else if(!dataReady){
                            Logger.d(TAG, "Data is not ready yet!")
                        }
                        else {
                            val gamesDict = catalogResponse.data.games
                            val rows = catalogResponse.rows.sortedBy { row -> row.display_order }

                            BoxWithConstraints {
                                var tabletPadding = PaddingValues()
                                val boxWithConstraintsScope = this
                                val maxWidth = boxWithConstraintsScope.maxWidth
                                if (maxWidth > AppConstants.VIEW_MAX_WIDTH.dp) {
                                    tabletPadding =
                                        PaddingValues(
                                            horizontal = (maxWidth - AppConstants.VIEW_MAX_WIDTH.dp) / 2f
                                        )
                                }

                                LazyColumn(
                                    state = listState,
                                    modifier = Modifier
                                        .padding(tabletPadding)
                                        .background(color = theme_light_background),
                                    userScrollEnabled = true,
                                    verticalArrangement = Arrangement.spacedBy(
                                        10.dp,
                                        Alignment.Top
                                    ),
                                ) {
                                    item {
                                        val height = if (isFtue) {
                                            if (isGuest) {
                                                144.dp
                                            } else {
                                                144.dp
                                            }
                                        } else if (isGuest) {
                                            222.dp
                                        } else {
                                            240.dp
                                        }
                                        Box(
                                            modifier = Modifier
                                                .height(height)
                                        ) {
                                            if (isFtue) {
                                                FtueBanner(isGuest)
                                            } else if (isGuest) {
                                                Image(
                                                    painter = painterResource(R.drawable.img_banner_guest),
                                                    alignment = Alignment.TopCenter,
                                                    contentScale = ContentScale.FillHeight,
                                                    contentDescription = STR(R.string.content_description_check),
                                                    modifier = Modifier
                                                        .fillMaxSize()
                                                        .clickable { LoginViewModel.instance.fanduelSignupFromGuestMode() }
                                                )
                                            } else {
                                                BrazeCarousel()
                                            }
                                        }
                                    }

                                    if (!isFtue && isGuest) {
                                        item {
                                            Box(
                                                modifier = Modifier
                                                    .padding(horizontal = 16.dp, vertical = 12.dp)
                                                    .clickable {
                                                        LoginViewModel.instance.fanduelSignupFromGuestMode()
                                                    }
                                            )
                                            {
                                                ContentBox(
                                                    modifier = Modifier.fillMaxWidth(),
                                                    bodyPaddingValues = PaddingValues(),
                                                ) {
                                                    Row(
                                                        modifier = Modifier
                                                            .fillMaxWidth()
                                                            .height(50.dp)
                                                            .clip(RoundedCornerShape(4.dp))
                                                            .graphicsLayer { clip = true },
                                                        horizontalArrangement = Arrangement.SpaceBetween,
                                                        verticalAlignment = Alignment.CenterVertically
                                                    ) {
                                                        Image(
                                                            painter = painterResource(id = R.drawable.ic_cash),
                                                            contentDescription = null,
                                                            contentScale = ContentScale.FillHeight,
                                                            modifier = Modifier
                                                                .wrapContentHeight(unbounded = true)
                                                                .height(92.dp)
                                                                .offset(x = (-28).dp)
                                                        )

                                                        Column(
                                                            modifier = Modifier
                                                                .fillMaxHeight()
                                                                .padding(horizontal = 12.dp),
                                                            verticalArrangement = Arrangement.spacedBy(
                                                                4.dp,
                                                                Alignment.CenterVertically
                                                            ),
                                                            horizontalAlignment = Alignment.Start
                                                        ) {
                                                            Text(
                                                                text = STR(R.string.rewards_are_waiting_for_you),
                                                                style = MaterialTheme.typography.headlineMedium
                                                            )
                                                            Text(
                                                                text = STR(R.string.join_now_to_claim_bonus_cash),
                                                                color = MaterialTheme.colorScheme.secondary,
                                                                style = MaterialTheme.typography.bodyMedium
                                                            )
                                                        }
                                                        Spacer(modifier = Modifier)
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    fun onGameTileClicked(
                                        gameData: GCGameData,
                                        lobbySelection: String?
                                    ) {
                                        selectedGameCatalogData = gameData
                                        selectedGameData =
                                            gamesViewModel.getGameDataById(gameData.game_id)

                                        playViewModel.setGameData(
                                            gameData = selectedGameCatalogData,
                                            game = selectedGameData
                                        )
                                        if (isFtue) {
                                            gameModalExpanded.value = true
                                        } else {
                                            navManager.navigate(Routes.CONTEST_SCREEN)

                                            AnalyticsManager.instance.logEvent(
                                                AnalyticsEvent(
                                                    analyticsEvent = AnalyticsEventName.Game_Selected.value,
                                                    properties = mapOf(
                                                        "Game Name" to (selectedGameData?.name ?: "Unknown"),
                                                        "Lobby Selection" to (lobbySelection ?: "Unknown"),
                                                    )
                                                )
                                            )
                                        }
                                    }

                                    for ((index, row) in rows.withIndex()) {
                                        // Skip non-game-grid rows if in Ftue or is guest
                                        if ((
                                                    (isFtue || isGuest) &&
                                                            (row.row_type != RowTypes.GAME ||
                                                                    row.style != RowStyles.GRID) ||
                                                            (row.for_guest_mode != isGuest) //guest mode filter
                                                    )
                                        ) {
                                            continue
                                        }

                                        // Show before non-river elements or after the first river
                                        if (
                                            index == 0 && (row.style != RowStyles.RIVER || row.row_type != RowTypes.GAME) ||
                                            index == 1 && rows[0].style == RowStyles.RIVER && rows[0].row_type == RowTypes.GAME
                                        ) {
                                            if (!isFtue && !isGuest && wallet.bonus_balance.toDouble() > 0) {
                                                item {
                                                    Spacer(Modifier.height(5.dp))
                                                    Box(modifier = Modifier.padding(horizontal = 16.dp)) {
                                                        ContentBox(
                                                            titleComposable = {
                                                                BonusCashBanner(value = wallet.bonus_balance)
                                                            }
                                                        )
                                                    }
                                                }
                                            }

                                            if (!isFtue && !isGuest && wallet.bonus_balance.toDouble() == 0.0) {
//https://kromestudios.atlassian.net/browse/KF-1303, fd wants to remove this
//                                                item {
//                                                    Box(modifier = Modifier.padding(horizontal = 16.dp)) {
//                                                        ReferFriendsBanner()
//                                                    }
//                                                }
                                                
                                                //comment for now wait for further info
//                                    val card = cards.find {
//                                        it.extras["section"] == BrazeContentCardType.raf.name
//                                    }
//                                    if (card != null) {
//                                        item {
//                                            BrazeBanner(
//                                                card = card,
//                                                modifier = Modifier
//                                                    .padding(horizontal = 16.dp, vertical = 12.dp)
//                                            )
//                                        }
//                                    }
                                            }
                                        }

                                        val RECENTLY_PLAYED = "Recently Played"//todo: currently this section is hardcoded (followed iOS)

                                        when (row.row_type) {
                                            RowTypes.GAME -> {
                                                val gamesList: MutableList<Pair<GCGameData, GCRowItem>> =
                                                    mutableListOf()
                                                for (rowItem in row.items) {
                                                    if (gamesDict.containsKey(rowItem.game_meta_datum_id)) {
                                                        gamesDict[rowItem.game_meta_datum_id]?.let {
                                                            if (gamesViewModel.getGameDataById(it.game_id) != null) {
                                                                if (row.title == RECENTLY_PLAYED) {
                                                                    if (recentlyPlayedGames.contains(
                                                                            it.game_id
                                                                        )
                                                                    ) {
                                                                        gamesList.add(
                                                                            Pair(
                                                                                it,
                                                                                rowItem
                                                                            )
                                                                        )
                                                                    }
                                                                } else {
                                                                    gamesList.add(Pair(it, rowItem))
                                                                }
                                                            }
                                                        }
                                                    }
                                                }

                                                if (gamesList.isEmpty()) {
                                                    continue
                                                }
                                                if(row.title == RECENTLY_PLAYED){
                                                    gamesList.sortBy {
                                                        recentlyPlayedGames.indexOf(it.first.game_id)
                                                    }
                                                }
                                                when (row.style) {
                                                    RowStyles.RIVER -> {
                                                        item {
                                                            GameTileRowScrollable(
                                                                title = row.title,
                                                                gamesList = gamesList.subList(
                                                                    0,
                                                                    minOf(
                                                                        gamesList.size,
                                                                        (row.max_items_to_show
                                                                            ?: gamesList.size)
                                                                    )
                                                                ),
                                                                horizontalPadding = if (row.title == RECENTLY_PLAYED || (row.items.count() <= 4)) 16.dp else 12.dp,
                                                                tileSize = gameTileItemWidth,
                                                                onClickTile = { gameData: GCGameData ->
                                                                    onGameTileClicked(
                                                                        gameData = gameData,
                                                                        lobbySelection = row.title
                                                                    )
                                                                }
                                                            )
                                                        }
                                                    }

                                                    RowStyles.GRID -> {
                                                        item {
                                                            GameTileGrid(
                                                                title = if (isFtue) ftueGamesTitle else row.title,
                                                                subtitle = if (isFtue) ftueGamesSubtitle else null,
                                                                maxColumns = 3,
                                                                gamesList = gamesList,
                                                                onClickTile = { gameData: GCGameData ->
                                                                    onGameTileClicked(
                                                                        gameData = gameData,
                                                                        lobbySelection = row.title
                                                                    )
                                                                }
                                                            )
                                                        }
                                                    }

                                                    else -> {

                                                    }
                                                }
                                            }

                                            RowTypes.TOURNAMENT -> {

                                                val availableTournaments: MutableList<Pair<APITournament, GCRowItem>> =
                                                    mutableListOf()
                                                val availableGames: MutableMap<String, GCGameData> =
                                                    mutableMapOf()


                                                row.items.forEach {
                                                    val tournament =
                                                        featuredTournaments[it.tournament_id]


                                                    if (tournament != null) {
                                                        var available = tournament.is_turbo != true || (tournament.is_turbo == true && isTurboActive)

                                                        //temp fix for https://worldwinner.atlassian.net/browse/FX-2818, as is_turbo is not added to the featured tournaments
                                                        if(tournament.is_turbo == null && tournament.name?.lowercase()?.contains("turbo") == true && !isTurboActive){
                                                            available = false
                                                        }

                                                        if(available){
                                                            availableTournaments.add(
                                                                Pair(
                                                                    tournament,
                                                                    it
                                                                )
                                                            )

                                                            for (pair in catalogResponse.data.games) {
                                                                if (pair.value.game_id == tournament.game_id) {
                                                                    availableGames[tournament.game_id] =
                                                                        pair.value
                                                                }
                                                            }
                                                        }
                                                    }
                                                }

                                                if (availableTournaments.size > 1) {
                                                    item{
                                                        if(row.show_title == true){
                                                            TileGroupTitle(
                                                                title = row.title,
                                                                topPadding = 1.dp,
                                                                bottomPadding = 1.dp
                                                            )
                                                        }
                                                        Spacer(Modifier.height(4.dp))
                                                        FeaturedTournamentRow(
                                                            tournaments = availableTournaments,
                                                            gamesList = availableGames,
                                                            tileWidth = featuredBannerWidth * .9f,
                                                            wallet = wallet
                                                        ) { tournament, gameData ->
                                                            currentTournamentData = tournament
                                                            infoModalExpanded.value = true
                                                            selectedGameData =
                                                                gamesViewModel.getGameDataById(
                                                                    gameData?.game_id ?: ""
                                                                )
                                                        }
                                                    }
                                                } else if (availableTournaments.isNotEmpty()) {
                                                    val tournament = availableTournaments[0]
                                                    val gameData =
                                                        availableGames[tournament.first.game_id]
                                                    item{
                                                        if(row.show_title == true){
                                                            TileGroupTitle(
                                                                title = row.title,
                                                                topPadding = 1.dp,
                                                                bottomPadding = 1.dp
                                                            )
                                                        }

                                                        TaggedTile(
                                                            message = tournament.second.banner,
                                                            tagOffsetX = (-8).dp,
                                                            modifier = Modifier
                                                                .height(116.dp)
                                                                .fillMaxWidth()
                                                                .padding(horizontal = 16.dp)
                                                        ) {
                                                            FeaturedContest(
                                                                contestType = tournament.first.game_mode?.short_name
                                                                    ?: tournament.first.game_mode?.name
                                                                    ?: STR(R.string.standard),
                                                                gameName = tournament.first.game_display_name
                                                                    ?: STR(R.string.tournament),
                                                                tournament = tournament.first,
                                                                wallet = wallet,
                                                                gameBackground = gameData?.tournament_background_image_url,
                                                                gameIcon = gameData?.tile_image_url,
                                                                onInfoClick = {
                                                                    currentTournamentData =
                                                                        tournament.first
                                                                    infoModalExpanded.value = true
                                                                    selectedGameData =
                                                                        gamesViewModel.getGameDataById(
                                                                            gameData?.game_id ?: ""
                                                                        )
                                                                },
                                                                gameId = gameData?.game_id
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    item {
                                        Spacer(modifier = Modifier)
                                    }
                                }
                            }
                        }
                    }
                }
                PullRefreshIndicator(
                    modifier = Modifier.align(alignment = Alignment.TopCenter),
                    refreshing = refreshing,
                    state = state,
                )
            }
        }
    }

    ContestDetail(infoModalExpanded, currentTournamentData, wallet, isGuest, gameId = selectedGameData?.id)

    GamePreview(gameModalExpanded, selectedGameData, isFtue, isGuest)
    if(isTurboPopupActive && !turboViewed.value){
        TurboPopup(onClose = {
            PreferencesManager.instance.setIsTurboViewed()
            ExperimentManager.instance.turbosMode?.trackExposure()
            turboViewed.value = true
        }) {
            navManager.navigate(Routes.GAMES_SCREEN)
        }
    }
}

@Preview
@Composable
fun LobbyScreenPreview(){
    PreviewRoot {
        LobbyScreen(gamesViewModel = GamesViewModel.instance, playViewModel = PlayViewModel.instance)
    }
}
