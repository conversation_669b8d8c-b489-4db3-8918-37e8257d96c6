package com.gametaco.app_android_fd.ui.popups

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.gametaco.app_android_fd.LocalModalPopupManager
import com.gametaco.app_android_fd.models.ModalPopupData
import com.gametaco.app_android_fd.ui.components.CommonButton
import com.gametaco.app_android_fd.ui.components.LinkedText
import com.gametaco.app_android_fd.ui.screens.HomeScreen
import com.gametaco.app_android_fd.ui.theme.AppandroidwwTheme
import com.gametaco.utilities.STR
import resources.R

@Composable
fun ModalPopup(modalData: ModalPopupData?) {
    modalData?.let {
        modalData?.composeFunction?.invoke(modalData)
    }
}

@Preview(widthDp = 360, heightDp = 740)
@Composable
fun ModalPopupPreview() {
    AppandroidwwTheme {
        HomeScreen()
        /*ModalPopup(
            titleText = "Connection issue",
            descriptionText = "We're sorry but you've lost your connection to the server.\n\nPlease check your internet connection and try again.",
            confirmText = "Ok",
        )*/
        /*ModalPopup(
            titleText = "Unable to submit score",
            descriptionText = "Please check your internet connection and select Retry. If you quit, your score may not be recorded.",
            closeText = "Quit",
            confirmText = "Retry",
            onConfirm = { },
        )*/
//        ModalPopup(
//            titleText = "Entry limit reached",
//            descriptionText = "You've hit the limit of unmatched contests for this prize pool. Play a different contest until one of your open contests in this prize pool gets matched.",
//            confirmText = "Choose a different contest",
//            onConfirm = { },
//        )
    }
}

@Composable
fun MaxPlaysPopUpLeaderboard(modalData: ModalPopupData)
{
    val modalPopupManager = LocalModalPopupManager.current
    PopupBox(
        showPopup = true,
        onDismissRequest = { modalPopupManager.dismissModal() },
        title = STR(R.string.join_to_play_unlimited_games),
    )
    {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp, Alignment.CenterVertically),
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
        )
        {
            Text(
                STR(R.string.you_ve_hit_the_limit_of_free_games_for_guest_users),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyMedium,
            )
            Text(
                STR(R.string.register_for_free_to_play_unlimited_games),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyMedium,
            )
            CommonButton(title = STR(R.string.join_now_to_keep_playing)) {
                modalPopupManager.dismissModal()
                modalData.onConfirm.invoke()
            }
            LinkedText(title = STR(R.string.return_to_games)) {
                modalPopupManager.dismissModal()
                modalData.onCancel?.invoke()
            }
        }
    }
}

@Composable
fun MaxPlaysPopUpJoinCTA(modalData: ModalPopupData)
{
    val modalPopupManager = LocalModalPopupManager.current
    PopupBox(
        showPopup = true,
        onDismissRequest = { modalPopupManager.dismissModal() },
        title = STR(R.string.unlock_games),
    )
    {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp, Alignment.CenterVertically),
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
        )
        {
            Text(
                STR(R.string.want_to_play_for_cash),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyMedium,
            )
            Text(
                STR(R.string.create_or_login_to_keep_playing),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyMedium,
            )
            CommonButton(title = STR(R.string.join_now)) {
                modalPopupManager.dismissModal()
                modalData.onConfirm.invoke()
            }
            LinkedText(title = STR(R.string.no_thankyou)) {
                modalPopupManager.dismissModal()
                modalData.onCancel?.invoke()
            }
        }
    }
}

@Composable
fun CompeteForCashPopUp(modalData: ModalPopupData)
{
    val modalPopupManager = LocalModalPopupManager.current
    PopupBox(
        showPopup = true,
        onDismissRequest = { modalPopupManager.dismissModal() },
        title = STR(R.string.play_for_fun_compete_for_cash),
    )
    {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp, Alignment.CenterVertically),
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
        )
        {
            Text(
                STR(R.string.as_a_guest_you_only_get_a_preview),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyMedium,
            )
            Text(
                STR(R.string.want_to_play_for_cash_unlock_all_games_and_earn_rewards),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyMedium,
            )
            CommonButton(title = STR(R.string.join_now))
            {
                modalPopupManager.dismissModal()
                modalData?.onConfirm?.invoke()
            }
            LinkedText(title = STR(R.string.see_my_score_and_keep_playing_as_a_guest))
            {
                modalPopupManager.dismissModal()
                modalData?.onCancel?.invoke()
            }
        }
    }
}
