package com.gametaco.app_android_fd.models

import androidx.compose.runtime.Immutable
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntry
import com.gametaco.utilities.ResourceState

@Immutable
data class LeaderboardDataModel(
    val isLoading: Boolean,
    val tournamentInstanceEntryId: String?,
    val data: APITournamentInstanceEntry?,
    val goals: ResourceState<GoalDataModel>,
    val connectedGoals: List<String>,
    val placeholderOpponentCount: Int,
    val showFtue: <PERSON>olean,
    val onCloseFtue: (String?) -> Unit,
    val onClose: () -> Unit,
    val onPlayAgain: () -> Unit,
    val isAfterGame: Boolean,
) {
    companion object {
        val Empty = LeaderboardDataModel(
            isLoading = false,
            tournamentInstanceEntryId = null,
            data = null,
            goals = ResourceState.Success(GoalDataModel(
                listOf(), listOf(), listOf()
            )),
            connectedGoals = listOf(),
            placeholderOpponentCount = 0,
            showFtue = false,
            onCloseFtue = { },
            onClose = { },
            onPlayAgain = { },
            isAfterGame = false,
        )
    }
}