package com.gametaco.app_android_fd.ui.components

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import com.gametaco.app_android_fd.ui.modifiers.onDisappear
import resources.R

@Composable
fun CustomAnimationView(
    animation:Int,
    isPlaying: Boolean = true,
    restartOnPlay: Boolean = true,
    reverseOnRepeat: Boolean = false,
    speed: Float = 1f,
    iterations: Int = 1,
    modifier: Modifier = Modifier,
    animationDidFinish: (() -> Unit)? = null) {
    val composition by rememberLottieComposition(LottieCompositionSpec.RawRes(animation))
    var finished by remember { mutableStateOf(false) }
    val progress by animateLottieCompositionAsState(
        composition,
        isPlaying = isPlaying,
        restartOnPlay = restartOnPlay,
        reverseOnRepeat = reverseOnRepeat,
        speed = speed,
        iterations = iterations,
    )

    LottieAnimation(
        composition = composition,
        progress = { progress },
        modifier = modifier.graphicsLayer {
            scaleX = 1.01f  // Slightly scale up the animation, this is required to handle cases where the content is right on the edge of the border and compose decides the content should start the next pixel over
            scaleY = 1.01f
        }.onDisappear {
            finished = false
        },
        contentScale = ContentScale.Crop
    )
    if(progress >= 1.0 && !finished){
        finished = true
        animationDidFinish?.invoke()
    }
}

@Composable
@Preview
fun CustomAnimationViewPreview(){
    CustomAnimationView(R.raw.anim_lb_refunded)
}