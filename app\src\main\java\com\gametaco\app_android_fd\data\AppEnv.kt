package com.gametaco.app_android_fd.data

import com.fanduel.coremodules.config.contract.AppDomain
import com.fanduel.coremodules.config.contract.Country
import com.fanduel.coremodules.config.contract.Environment
import com.gametaco.app_android_fd.BuildConfig
import com.gametaco.app_android_fd.manager.PreferencesManager

enum class ENV{
    DEV,
    QA,
    PROD
}

val ENV.isProd: Boolean
    get() = this == ENV.PROD
val ENV.isNonProd: Boolean
    get() = !isProd

// Will only be set in non-production builds
enum class EnvHost {
    GT,
    WW,
}

data class EnvConfig(
    val env: ENV,
    val envHost: EnvHost?,
    val api_endpoint:String,
    val api_app_id:String,
    val app_version:String,
    val braze_api_key:String,
    val braze_api_endpoint:String,
    val sift_account_id:String,
    val sift_beacon_key:String,
    val amplitude_api_key:String,
    val amplitude_experiments_key:String,
    val amplitude_app_id:String,
    val fanduel_account_url:String,
    val firebase_cloud_messaging_sender_id:String,
    val fanduel_environment:Environment,
    val fanduel_appdomain : AppDomain,
    val maintenanceURL:String,
    val fanduel_salesforce_orgId : String,
    val fanduel_salesforce_deploymentId : String,
    val fanduel_salesforce_buttonId : String,
    val fanduel_salesforce_liveAgentPod : String,
) {
    val apiEndpointHostname: String
        get() = api_endpoint.removePrefix("https://").removePrefix("http://").substringBefore("/")
}

object EnvConfigs{
    fun EnvConfigDev(
        envHost: EnvHost,
        fanduel_account_url: String,
        fanduel_environment: Environment,
    ) = EnvConfig(
        env = ENV.DEV,
        envHost = envHost,
        api_endpoint = AppConstants.API_BASE_URL_DEV,
        api_app_id = AppConstants.API_APP_ID,
        app_version = AppConstants.API_APP_VERSION,
        braze_api_endpoint = AppConstants.BRAZE_API_ENDPOINT_DEV,
        braze_api_key = AppConstants.BRAZE_API_KEY_DEV,
        sift_account_id = AppConstants.SIFT_ACCOUNT_ID_DEV,
        sift_beacon_key = AppConstants.SIFT_BEACON_KEY_DEV,
        amplitude_api_key = AppConstants.AMPLITUDE_API_KEY_DEV,
        amplitude_app_id = AppConstants.AMPLITUDE_APP_ID_DEV,
        amplitude_experiments_key = AppConstants.AMPLITUDE_EXPERIMENTS_KEY_DEV,
        fanduel_account_url = fanduel_account_url,
        firebase_cloud_messaging_sender_id = AppConstants.FIREBASE_CLOUD_MESSAGING_SENDER_ID,
        fanduel_environment = fanduel_environment,
        fanduel_appdomain = AppDomain.SkilledGames("us"),
        maintenanceURL = "https://qa1-maint.wwqa-fd.com/api/maintenance",
        fanduel_salesforce_orgId = "00D8G000000sbQL",
        fanduel_salesforce_deploymentId = "5725Y000000QSWF",
        fanduel_salesforce_buttonId = "5735Y000000QSsW",
        fanduel_salesforce_liveAgentPod = "d.la1-c1cs-ia6.salesforceliveagent.com",
    )

    val DEV_WW:EnvConfig = EnvConfigDev(
        envHost = EnvHost.WW,
        fanduel_account_url = "",
        fanduel_environment = Environment.DevStack("wwstaging"),
    )
    val DEV_GT:EnvConfig = EnvConfigDev(
        envHost = EnvHost.GT,
        fanduel_account_url = AppConstants.FANDUEL_BASE_URL_DEV_GT,
        fanduel_environment = Environment.DevStack("gtstaging"),
    )

    fun EnvConfigQa(
        envHost: EnvHost,
        fanduel_account_url: String,
        fanduel_environment: Environment,
    ) = EnvConfig(
        env = ENV.QA,
        envHost = envHost,
        api_endpoint = AppConstants.API_BASE_URL_QA,
        api_app_id = AppConstants.API_APP_ID,
        app_version = AppConstants.API_APP_VERSION,
        braze_api_endpoint = AppConstants.BRAZE_API_ENDPOINT_DEV,
        braze_api_key = AppConstants.BRAZE_API_KEY_DEV,
        sift_account_id = AppConstants.SIFT_ACCOUNT_ID_DEV,
        sift_beacon_key = AppConstants.SIFT_BEACON_KEY_DEV,
        amplitude_api_key = AppConstants.AMPLITUDE_API_KEY_DEV,
        amplitude_app_id = AppConstants.AMPLITUDE_APP_ID_DEV,
        amplitude_experiments_key = AppConstants.AMPLITUDE_EXPERIMENTS_KEY_DEV,
        fanduel_account_url = fanduel_account_url,
        firebase_cloud_messaging_sender_id = AppConstants.FIREBASE_CLOUD_MESSAGING_SENDER_ID,
        fanduel_environment = fanduel_environment,
        fanduel_appdomain = AppDomain.SkilledGames("us"),
        maintenanceURL = "https://qa1-maint.wwqa-fd.com/api/maintenance",
        fanduel_salesforce_orgId = "00D8G000000sbQL",
        fanduel_salesforce_deploymentId = "5725Y000000QSWF",
        fanduel_salesforce_buttonId = "5735Y000000QSsW",
        fanduel_salesforce_liveAgentPod = "d.la1-c1cs-ia6.salesforceliveagent.com",
    )

    val QA_WW:EnvConfig = EnvConfigQa(
        envHost = EnvHost.WW,
        fanduel_account_url = "",
        fanduel_environment = Environment.DevStack("wwstaging"),
    )
    val QA_GT:EnvConfig = EnvConfigQa(
        envHost = EnvHost.GT,
        fanduel_account_url = AppConstants.FANDUEL_BASE_URL_DEV_WW,
        fanduel_environment = Environment.DevStack("gtstaging"),
    )

    val PROD:EnvConfig = EnvConfig(
        env = ENV.PROD,
        envHost = null,
        api_endpoint = AppConstants.API_BASE_URL_PROD,
        api_app_id = AppConstants.API_APP_ID,
        app_version = AppConstants.API_APP_VERSION,
        braze_api_endpoint = AppConstants.BRAZE_API_ENDPOINT_PROD,
        braze_api_key = AppConstants.BRAZE_API_KEY_PROD,
        sift_account_id = AppConstants.SIFT_ACCOUNT_ID_PROD,
        sift_beacon_key = AppConstants.SIFT_BEACON_KEY_PROD,
        amplitude_api_key = AppConstants.AMPLITUDE_API_KEY_PROD,
        amplitude_app_id = AppConstants.AMPLITUDE_APP_ID_PROD,
        amplitude_experiments_key = AppConstants.AMPLITUDE_EXPERIMENTS_KEY_PROD,
        fanduel_account_url = AppConstants.FANDUEL_BASE_URL_PROD,
        firebase_cloud_messaging_sender_id = AppConstants.FIREBASE_CLOUD_MESSAGING_SENDER_ID,
        fanduel_environment = Environment.Prod(Country.US),
        fanduel_appdomain = AppDomain.SkilledGames("us"),
        maintenanceURL = "https://maint.ww-fd.com/api/maintenance",
        fanduel_salesforce_orgId = "00D5Y000002UY4T",
        fanduel_salesforce_deploymentId = "5725Y000000QSWF",
        fanduel_salesforce_buttonId = "5735Y000000QSsW",
        fanduel_salesforce_liveAgentPod = "d.la3-c2-ia5.salesforceliveagent.com",
    )
}
class AppEnv {
    companion object {
        val current:EnvConfig
            get() {
                val isProd = BuildConfig.WW_ENVIRONMENT == "prod"

                var env = PreferencesManager.instance.getAppENV()
                //don't allow changing env on prod builds
                if(isProd) {
                    env = ENV.PROD.name
                }

                var envHost = PreferencesManager.instance.getAppEnvHost()
                if(isProd) {
                    envHost = null
                }

                return when(env){
                    ENV.QA.name -> {
                        when (envHost) {
                            null, EnvHost.WW.name -> EnvConfigs.QA_WW
                            EnvHost.GT.name -> EnvConfigs.QA_GT
                            else -> error("Invalid envHost: $envHost")
                        }
                    }
                    ENV.DEV.name -> {
                        when (envHost) {
                            null, EnvHost.GT.name -> EnvConfigs.DEV_GT
                            EnvHost.WW.name -> EnvConfigs.DEV_WW
                            else -> error("Invalid envHost: $envHost")
                        }
                    }
                    ENV.PROD.name -> EnvConfigs.PROD
                    else -> {
                        EnvConfigs.QA_WW
                    }
                }
            }
    }
}
