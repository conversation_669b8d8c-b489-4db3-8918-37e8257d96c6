package com.gametaco.app_android_fd.utils

import java.text.SimpleDateFormat
import java.util.Date
import java.util.TimeZone

fun Date.format(pattern:String = "MMMM dd, yyyy"):String{
    val sdf = SimpleDateFormat(pattern)
    return sdf.format(this.getTime())
}

fun Date.formatEST(pattern:String = "MMMM dd, yyyy"):String{
    val sdf = SimpleDateFormat(pattern)
    sdf.timeZone = TimeZone.getTimeZone("America/New_York")
    return sdf.format(this.getTime())
}
fun Date.formatLocal(pattern:String = "MMMM dd, yyyy"):String{
    val sdf = SimpleDateFormat(pattern)
    sdf.timeZone = TimeZone.getDefault()
    return sdf.format(this.getTime())
}
fun Date.getLeftTimeString():String{
    val t = (this.time - Date().time) / 1000
    if(t > 0){
        if(t < 60){
            return "<1M"
        }else if(t < 3600){
            return "${(t/60).toInt()}M"
        }else if(t < 3600 * 24){
            return "${(t/(3600)).toInt()}H"
        }else{
            return "${(t/(3600 * 24)).toInt()}D"
        }
    }else{
        return "EXPIRED"
    }
}
