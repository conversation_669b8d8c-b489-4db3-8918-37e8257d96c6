package com.gametaco.app_android_fd.ui.popups

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.gametaco.app_android_fd.ui.modifiers.clickableWithoutRipple
import com.gametaco.app_android_fd.ui.theme.AppFont
import com.gametaco.app_android_fd.viewmodel.GoalsViewModel

@Composable
fun DailyRewardEndView() {
    val show = GoalsViewModel.instance.showDailyRewardEndPopup.collectAsState()
    if(show.value == false){
        return
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Color.Black.copy(alpha = 0.6f)
            )
            .clickableWithoutRipple {

            },
        contentAlignment = Alignment.TopStart
    ) {
        Box(modifier = Modifier
            .navigationBarsPadding()
            .padding(24.dp, 100.dp)
            .clip(RoundedCornerShape(16.dp))
            .shadow(10.dp, RoundedCornerShape(16.dp))
        ){
            Image(
                painter = painterResource(id = resources.R.drawable.img_daily_reward_end_popup),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Fit
            )
            TextButton(modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = 42.dp, end = 6.dp), onClick = {
                GoalsViewModel.instance.closeDailyRewardEndPopup()
            }) {
                Text(
                    text = "Close",
                    fontSize = 16.sp,
                    color = Color.Transparent,
                    fontFamily = AppFont.ProximaNova,
                    modifier = Modifier.padding()
                )
            }
        }
    }
}
