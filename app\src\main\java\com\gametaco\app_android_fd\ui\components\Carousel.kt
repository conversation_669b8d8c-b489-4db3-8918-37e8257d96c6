package com.gametaco.app_android_fd.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.gametaco.app_android_fd.LocalNavManager
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.ui.theme.theme_light_tertiary
import com.gametaco.app_android_fd.ui.utils.getNavigationBarHeight
import com.gametaco.app_android_fd.viewmodel.WelcomeViewModel
import com.gametaco.utilities.STR
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import resources.R


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun WelcomeCarousel(
    pages: List<@Composable (Offset?) -> Unit>,
    welcomeViewModel: WelcomeViewModel
) {
    val navManager = LocalNavManager.current
    val hasPriorLogin = AuthenticationManager.instance.hasPriorLogin
    val experimentsReady by ExperimentManager.instance.experimentsReady.collectAsState()


    var footerTopRight: Offset? by remember { mutableStateOf(null) }

    Box(modifier = Modifier.fillMaxSize())
    {
        val pagerState = rememberPagerState(
            initialPage = 0,
            initialPageOffsetFraction = 0f
        ) {
            pages.size
        }
        HorizontalPager(

            state = pagerState
        ) { index ->
            Box(modifier = Modifier.fillMaxSize())
            {
                pages[index](footerTopRight)
            }
        }

        Column(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .padding(32.dp, getNavigationBarHeight())
                .onGloballyPositioned { coordinates ->
                    footerTopRight = coordinates.boundsInWindow().topRight
                },
            verticalArrangement = Arrangement.Absolute.spacedBy(8.dp, Alignment.Bottom),
            horizontalAlignment = Alignment.CenterHorizontally,
            ) {
            Row(
                horizontalArrangement = Arrangement.Absolute.spacedBy(
                    space = 12.dp,
                    alignment = Alignment.CenterHorizontally
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
            ) {
                for (i in 0 until pagerState.pageCount) {
                    val dotColor = if (pagerState.currentPage == i) {
                        Color.White
                    } else {
                        Color.White.copy(alpha = .45f)
                    }
                    Canvas(modifier = Modifier
                        .size(12.dp, 12.dp)
                        .align(Alignment.CenterVertically),
                        onDraw = {
                            drawCircle(dotColor)
                        })
                }
            }

            Spacer(modifier = Modifier.size(16.dp)) //0.dp Spacers add another 8.dp with Column Spacing

            Box(
                modifier = Modifier
                    .clip(RoundedCornerShape(4.dp))
                    .background(Color.White)
                    .padding(16.dp)
                    .wrapContentHeight()
            ) {
                Column(
                    modifier = Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .align(Alignment.BottomCenter),
                    verticalArrangement = Arrangement.Absolute.spacedBy(8.dp, Alignment.Bottom),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    CommonButton(
                        title = STR(R.string.log_in_with_your_account),
                        fillColor = theme_light_tertiary,
                        titleStyle = MaterialTheme.typography.bodyLarge
                    ) {
                        welcomeViewModel.loginPressed(navManager)
                    }

                    CommonButton(
                        title = STR(R.string.create_an_account),
                        titleStyle = MaterialTheme.typography.bodyLarge
                    ) {
                        welcomeViewModel.createAccountPressed(navManager)
                    }

                    val hideGuestModeButton = true
//                    if(experimentsReady)
//                    {
//                        hideGuestModeButton = ExperimentManager.instance.hideGuestModeButton?.isAvailable() == true && ExperimentManager.instance.hideGuestModeButton?.isActive() == true
//                    }

                    if (!hideGuestModeButton && !hasPriorLogin) {
                        Spacer(modifier = Modifier.size(0.dp)) //16Total with Column Spacing
                        LinkedText(
                            title = STR(R.string.new_here_try_a_game_as_a_guest),
                            style = MaterialTheme.typography.bodySmall.copy(color = MaterialTheme.colorScheme.primary),
                            onClick = {
                                welcomeViewModel.guestPressed(navManager)
                            },
                        )
                    } else {
                        AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Welcome_Viewed.value))
                    }
                }
            }
            Box {
//                val disclaimerText = if(hasPriorLogin) {
//                    STR(R.string.llc_all_rights_reserved)
//                } else {
//                    STR(R.string.llc_all_rights_reserved_no_prior_login)
//                }
                val disclaimerText = STR(R.string.llc_all_rights_reserved)
                Text(
                    text = disclaimerText,
                    style = MaterialTheme.typography.labelMedium,
                    textAlign = TextAlign.Center,
                    color = Color.White,
                    modifier = Modifier
                        .padding(top = 8.dp)
                )
            }
            Spacer(modifier = Modifier.size(0.dp))
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun LobbyCarousel(
    pages: List<@Composable ()->Unit>,
) {
    val pagerState = rememberPagerState(
        initialPage = 0,
        initialPageOffsetFraction = 0f
    ) {
        pages.size
    }
    var lastInteractionTime by remember { mutableStateOf(System.currentTimeMillis()) }
    // This block will execute when the pager starts or stops auto scrolling, either by user
    // interaction or by the auto-scroll logic.
    LaunchedEffect(pagerState.isScrollInProgress) {
        // Reset last interaction time when scrolling starts or ends. This ensures the auto-scroll
        // login below will not trigger immediately after a user interaction.
        lastInteractionTime = System.currentTimeMillis()
    }

    // Auto-scroll with resume delay
    LaunchedEffect(Unit) {
        while (isActive) {
            val now = System.currentTimeMillis()
            val timeSinceLastInteraction = now - lastInteractionTime

            if (!pagerState.isScrollInProgress && timeSinceLastInteraction > 3000) {
                val nextPage = (pagerState.currentPage + 1) % pages.size
                pagerState.animateScrollToPage(nextPage)
                lastInteractionTime = System.currentTimeMillis() // reset after scroll
            }

            delay(1000) // check every 1 second
        }
    }
    Box(modifier = Modifier.fillMaxSize())
    {

        HorizontalPager(
            state = pagerState
        ) { index ->
            Box(modifier = Modifier.fillMaxSize())
            {
                pages[index]()
            }
        }

        Column(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .padding(16.dp),
            verticalArrangement = Arrangement.Absolute.spacedBy(8.dp, Alignment.Bottom),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(
                horizontalArrangement = Arrangement.Absolute.spacedBy(
                    space = 12.dp,
                    alignment = Alignment.CenterHorizontally
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
            )
            {
                if (pagerState.pageCount > 1) {
                    for (i in 0 until pagerState.pageCount) {
                        val dotColor = if (pagerState.currentPage == i) Color.White else Color.Gray
                        Canvas(modifier = Modifier
                            .size(8.dp)
                            .align(Alignment.CenterVertically),
                            onDraw = {
                                drawCircle(dotColor)
                            })
                    }
                }
            }
        }
    }
}