package com.worldwinner.fanduel.uitest

import com.worldwinner.fanduel.uitest.appium.AppiumManager
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import kotlin.test.assertEquals

open class FanduelUiTestBase {

    protected val appiumManager = AppiumManager()
    protected val uiTestManager = UiTestManagerImpl(appiumManager)

    protected fun uiTest(
        body: suspend TestScope.() -> UiTestResult,
    ) {
        return runTest {
            appiumManager.run(uiTestManager) {
                val result = body()
                assertEquals(UiTestResult.Pass, result, "Test failed: ${result.message}")
            }
        }
    }
}