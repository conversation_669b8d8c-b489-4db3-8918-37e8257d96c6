package com.gametaco.app_android_fd.ui.popups

import android.app.Activity
import android.content.Context
import com.fanduel.core.libs.modalpresenter.contract.ActionItem
import com.fanduel.core.libs.modalpresenter.contract.IModal
import com.fanduel.core.libs.modalpresenter.contract.IModalPresenter
import com.fanduel.core.libs.modalpresenter.contract.ModalPresenterConfig
import com.fanduel.coremodules.config.contract.AppDomain
import com.fanduel.coremodules.config.contract.ICoreConfig
import com.fanduel.coremodules.ioc.ICoreIoC
import com.fanduel.coremodules.webview.AuthMode
import com.fanduel.coremodules.webview.CoreWebView
import com.fanduel.coremodules.webview.config.CoreWebViewConfig
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

internal interface IFDWebviewPresenter {
    fun present(
        source: String,
        authMode: AuthMode,
        titleString: String,
        actionButtonString: String? = null,
        appDomain: AppDomain? = null,
        onComplete: (Boolean) -> Unit = {}
    )
}

internal fun interface ICoreWebViewFactory {
    fun create(context: Context): CoreWebView
}

internal class CoreWebViewFactory : ICoreWebViewFactory {
    override fun create(context: Context): CoreWebView = CoreWebView(context)
}

internal class FDWebviewPresenter(
    private val coreIoC: ICoreIoC,
    private val coreWebViewFactory: ICoreWebViewFactory,
    private val coroutineScope: CoroutineScope,
) : IFDWebviewPresenter {
    private val coreConfig: ICoreConfig
        get() = requireNotNull(coreIoC.resolve(ICoreConfig::class.java)) {
            "Please ensure that ICoreConfig is registered on CoreIoC"
        }
    private val modalPresenter: IModalPresenter
        get() = requireNotNull(coreIoC.resolve(IModalPresenter::class.java)) {
            "Please ensure that IModalPresenter is registered on CoreIoC"
        }

    private val _modalActivity = MutableStateFlow<Activity?>(null)
    val modalActivity: StateFlow<Activity?>
        get() = _modalActivity

    override fun present(
        source: String,
        authMode: AuthMode,
        titleString: String,
        actionButtonString: String?,
        appDomain: AppDomain?,
        onComplete: (Boolean) -> Unit
    ) {
        val context = requireNotNull(coreConfig.context) {
            "Please ensure that context is set on ICoreConfig"
        }
        coroutineScope.launch {
            var modal: WeakReference<IModal>? = null
            val onFlowCompleted: (Boolean) -> Unit = {
                onComplete(it)
                modal?.get()?.dismiss()
                _modalActivity.value = null
            }
            modal = WeakReference(
                modalPresenter.presentModal(
                    config = ModalPresenterConfig(
                        titleText = titleString,
                        trailingActionItem = when (actionButtonString){
                            null -> ActionItem.CloseIcon { modal?.get()?.dismiss() }
                            else -> ActionItem.Text(actionButtonString, onClick = { modal?.get()?.dismiss() })
                        },
                        appDomain = appDomain ?: coreConfig.config?.appDomain,
                    ),
                    content = { contentFactory(it, source, authMode, appDomain, onFlowCompleted) }
                ).await()
            )
            modal.get()?.onDismiss = { onComplete(false) }
        }
    }

    private fun contentFactory(
        context: Context,
        source: String,
        authMode: AuthMode,
        appDomain: AppDomain?,
        onComplete: (Boolean) -> Unit
    ): CoreWebView {
        return coreWebViewFactory.create(context).also {
            _modalActivity.value = context as? Activity
            it.source = source
            it.config = CoreWebViewConfig(
                cookies = mapOf("fd-app-new-support-ui " to "true"),
                appDomain = appDomain ?: coreConfig.config?.appDomain,
                authMode = authMode,
                showHeader = false,
                showFooter = false,
//                capabilities = setOf(
//                    Capability(
//                        topic = FLOW_COMPLETE_TOPIC,
//                        version = 1,
//                        methods = setOf(FLOW_COMPLETE_METHOD)
//                    )
//                ),
//                onMessage = { topic, method, _, _ ->
//                    if (topic == FLOW_COMPLETE_TOPIC && method == FLOW_COMPLETE_METHOD) {
//                        onComplete(true)
//                    }
//                }
            )
        }
    }
}
