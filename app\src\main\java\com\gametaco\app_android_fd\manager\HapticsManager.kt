package com.gametaco.app_android_fd.manager

import android.content.Context
import android.os.VibrationEffect
import android.os.Vibrator
import com.gametaco.app_android_fd.di.resolve

enum class HapticFeedbackType(val type: String) {
    LIGHT("LightImpact"),
    MEDIUM("MediumImpact"),
    HEAVY("HeavyImpact"),
    RIGID("RigidImpact"),
    SOFT("SoftImpact"),
    SELECTION("Selection"),
    SUCCESS("Success"),
    WARNING("Warning"),
    ERROR("Error"),
    FAILURE("Failure");

    override fun toString(): String {
        return type
    }

    companion object {
        fun fromType(type: String): HapticFeedbackType? {
            return values().find { it.type == type }
        }
    }
}

class HapticsManager(
    private val context: Context,
    private val preferencesManager: PreferencesManager,
) {

    companion object {
        val instance: HapticsManager
            get() = resolve()
    }

    fun playEffect(feedbackType:HapticFeedbackType?) {
        if(feedbackType == null || !preferencesManager.getIsHapticsEnabled())
            return

        val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator

        var effect : VibrationEffect? = null
        var amplitude = VibrationEffect.DEFAULT_AMPLITUDE
        var durationMs : Long = 20

        when (feedbackType) {
            HapticFeedbackType.LIGHT -> {
                durationMs = 20
                amplitude = 40
            }
            HapticFeedbackType.MEDIUM -> {
                durationMs = 40
                amplitude = 120
            }
            HapticFeedbackType.HEAVY -> {
                durationMs = 80
                amplitude = 255
            }
            HapticFeedbackType.RIGID -> {
                durationMs = 20
                amplitude = 255
            }
            HapticFeedbackType.SOFT -> {
                durationMs = 80
                amplitude = 40
            }
            HapticFeedbackType.SELECTION -> {
                durationMs = 20
                amplitude = 40
            }
            HapticFeedbackType.SUCCESS -> {
                durationMs = 20
                amplitude = 40
            }
            HapticFeedbackType.WARNING -> {
                durationMs = 80
                amplitude = 255
            }
            HapticFeedbackType.ERROR -> {
                durationMs = 80
                amplitude = 255
            }
            HapticFeedbackType.FAILURE -> {
                durationMs = 40
                amplitude = 120
            }
        }

        effect = VibrationEffect.createOneShot(durationMs, amplitude)

        if(effect != null) {
            vibrator.vibrate(effect)
        }
    }


}