package com.worldwinner.fanduel.uitest

import io.appium.java_client.android.AndroidDriver

interface UiTestManager {

    fun setAndroidDriver(driver: AndroidDriver)

    suspend fun runToBootApp(): UiTestResult
    suspend fun runToGrantFirstRunLocationPermission(): UiTestResult

    // Login flows
    suspend fun runToSignIn_success(): UiTestResult
    suspend fun runToSignIn_failure_invalid_email(): UiTestResult
    suspend fun runToSignIn_failure_incorrect_password(): UiTestResult
    suspend fun runToSignIn_failure_invalid_email_incorrect_password(): UiTestResult

    suspend fun runToTurboPopup(): UiTestResult

    // Navigation flows
    suspend fun runToLobbyTab(): UiTestResult
// text validation on login page
    suspend fun runTextValidationOnLoginPage(): UiTestResult
    // Rewards Page
    suspend fun runToRewardsTab(): UiTestResult
    suspend fun checkSectionsOnRewardsPage(): UiTestResult
    suspend fun runToScoresTab(): UiTestResult

    // Account Flows
    suspend fun  checkAccountPageSections(): UiTestResult
    suspend fun runToAccountTab(): UiTestResult

    // Game list verification flows
    suspend fun runToLobby_verifyRecentlyPlayed(): UiTestResult
    suspend fun runToLobby_verifyPopularGames(): UiTestResult
    suspend fun runToLobby_verifyAllGames(): UiTestResult
    suspend fun runToLobby_verifyGameCount(): UiTestResult
    
    // Close dialog flow
    suspend fun runToCloseDialog_whenVisible(): UiTestResult

    // Logout flows
    suspend fun runToAccount_logout_confirmationDialog_displays(): UiTestResult
    suspend fun runToAccount_logout_confirmationDialog_canBeCancelled(): UiTestResult
//    suspend fun runToAccount_logout_confirmationDialog_canBeConfirmed(): UiTestResult
    suspend fun runToAccount_logout_verifyLoggedOut(): UiTestResult
}