package com.gametaco.app_android_fd.manager

import com.gametaco.app_android_fd.utils.log.Logger


class MockUnity : UnityProviding {
    override fun loadGame(encodedString: String) {
        Logger.d("🥸 MockUnity loadGame")
    }

    override fun startGame(o: String) {
        Logger.d("🥸 MockUnity startGame: $o")
    }

    override fun pauseGame(isPaused: Boolean) {
        Logger.d("🥸 MockUnity pauseGame $isPaused")
    }

    override fun resetUnity() {
        Logger.d("🥸 MockUnity reset")
    }

    override fun returnToApp() {
        Logger.d("🥸 MockUnity return to app")
    }

    override fun getReplayFilePath(tournamentInstanceEntryId : String) : String? {
        Logger.d("🥸 MockUnity getReplayFilePath")
        return null
    }

    override fun prepareForReplay(tournamentInstanceEntryID: String?) {
        Logger.d("🥸 MockUnity prepareForReplay")
    }

    override fun uploadReplay(tournamentInstanceEntryID: String?, replayFilePath: String?, isUserViewable: Boolean) {
        Logger.d("🥸 MockUnity uploadReplay")
    }

    override fun showUnityFromAction(actionType: String) {
        Logger.d("🥸 showUnityFromAction")
    }

    override fun getDailyRewardFutureTime(): String? {
        Logger.d("🥸 getDailyRewardFutureTime")
        return null
    }

    override fun setTournamentManagerDelegate(delegate: TournamentManagerDelegate) {
        Logger.d("🥸 setTournamentManagerDelegate")
    }

    override fun hideUnity(appNavigationInfo: String?, error: String?) {
        Logger.d("🥸 hideUnity")
    }

    override fun isShowingUnity(): Boolean {
        return false
    }

}