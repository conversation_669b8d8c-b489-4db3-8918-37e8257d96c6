package com.gametaco.app_android_fd.data.entity


data class APITournamentInstanceEntry(
    val id: String,
    val tournament_instance_id: String,
    val started_at: String?,
    val ended_at: String?,
    val entry_fee: String,
    val maximum_slots: Int?,
    val is_winner: <PERSON><PERSON>an,
    val is_tie: Boolean,
    val tournament_id: String?,
    val tournament_instance_status: String,
    val tournament_instance_closed_at: String?,
    val score: Int?,
    val current_slots_count: Int,
    val game_icon: String?,
    val game_id: String,
    val results: List<APITournamentInstanceEntryResult>,
    val is_joinable: <PERSON><PERSON>an,
    val can_reenter: Boolean,
    val prize_amount: String?,
    val game_display_name: String,
    val game_mode_id:String?,
    val game_mode_name: String?,
    val game_mode_description: String?,
    val game_mode_icon: String?,
    val refund_reason: String?,
    val reference_id:String?,
    val expanded_results:Map<String,APITournamentInstanceEntryExpandedResult>?,
    val gradient_top_color:String?,
    val gradient_bottom_color:String?,
    val maximum_entries_per_player:Int?,
    val has_additional_ticket: Boolean = false,
    val is_for_ticket_holders_only: Boolean = false,
    var ticket_info:APITournamentEntryResultTicketInfo? = null,
){
    val entryFee: EntryFee by lazy { EntryFee(entry_fee) }
    val isEntryFeeFree:Boolean
        get() = entryFee.isFree
    val isFreeEntry: Boolean
        get() = isEntryFeeFree || has_additional_ticket

    fun hasBeenRefunded():Boolean{
        return tournament_instance_status !=  APITournamentInstanceStatus.Open.rawValue
                && (tournament_instance_status == APITournamentInstanceStatus.Refunded.rawValue
                || refund_reason != null)
    }
}

val APITournamentInstanceEntry?.entryFeeLabel: String
    get() = entryFeeLabel(isFree = this == null || isFreeEntry, value = this?.entryFee?.string ?: "0.00")

data class APITournamentInstanceEntryExpandedResult(
    val keyStats:List<APITournamentInstanceEntryExpandedResultStatItem>,
    val subScores:List<APITournamentInstanceEntryExpandedResultScoreItem>,
)

data class APITournamentInstanceEntryExpandedResultStatItem(
    val name:String,
    val value:String
)
data class APITournamentInstanceEntryExpandedResultScoreItem(
    val name:String,
    val value:Int,
    val description:String?
)

data class APITournamentInstanceEntryResult(
    val user: APITournamentUser?,
    val score: Int?,
    val prize_amount: String?,
    val order:Int?,
    val status:APITournamentUserStatus,
    val replay_url:String?
){
    fun getUserName():String?{
        return user?.username
    }
}

data class APITournamentUser(
    val id:String,
    val username:String,
    val avatar_url:String?
)
enum class APITournamentUserStatus(value:String){
    NOT_STARTED("NOT_STARTED"),
    COMPLETED("COMPLETED"),
    IN_PROGRESS("IN_PROGRESS"),
    OPEN("OPEN")
}

enum class APIRefundReason(val rawValue:String){
    onlyPlayer("ONLY_PLAYER"),
    autoBooted("AUTO_BOOTED"),
    playerServices("PLAYER_SERVICES"),
}

data class APITournamentPlayerProfile(
    val username:String,
    val avatar_url:String,
    val member_since:String,
    val game_mode_stats:APITournamentPlayerProfileStat?
)

data class APITournamentPlayerProfileStat(
    val average_score:Double,
    val total_games_played:Int,
    val highest_score:Int,
    val highest_score_achieved_at:String?
)

