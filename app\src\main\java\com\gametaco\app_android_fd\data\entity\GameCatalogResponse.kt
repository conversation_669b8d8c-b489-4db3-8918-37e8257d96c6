package com.gametaco.app_android_fd.data.entity

data class GameCatalogResponse(
    val data: GCData,
    val rows: List<GCRow>,
)


data class GCData(
    val games: Map<String, GCGameData>,
)


data class GCGameData(
    val id : String,
    val game_id : String,
    val loading_background_image_url : String,
    val tournament_background_image_url : String,
    val hero_image_url : String,
    val tile_image_url : String,
    val gradient_top_color : String,
    val gradient_bottom_color : String,
    val panel_color : String?
)

data class GCVideoData(
    val id : String,
    val title : String,
    val url : String,
    val thumbnail_url : String
)

data class GCRow(
    val title : String,
    val style : String,
    val row_type : String,
    val display_order : Int,
    val items : List<GCRowItem>,
    val for_guest_mode : Boolean,
    val max_items_to_show : Int?,
    val show_title : Boolean?
)

data class GCRowItem(
    val tournament_id : String?,
    val game_meta_datum_id : String?,
    val display_order : Int,
    val banner : String?
)

object RowTypes {
    const val GAME = "GAME"
    const val TOURNAMENT = "TOURNAMENT"
}

object RowStyles {
    const val RIVER = "RIVER"
    const val GRID = "GRID"
}

object BannerEnum {
    const val REWARD = "REWARD"
    const val BEAT_THE_SCORE = "BEAT_THE_SCORE"
    const val WINNERS_CIRCLE = "WINNERS_CIRCLE"
    const val PROMOTION = "PROMOTION"
    const val SPECIAL_OFFER = "SPECIAL_OFFER"
    const val DAILY_SWING = "DAILY_SWING"
    const val DAILY_KICK = "DAILY_KICK"
    const val DAILY_SHOT = "DAILY_SHOT"
    const val DAILY_POP = "DAILY_POP"
    const val LIMITED_TIME = "LIMITED_TIME"
    const val LIMITED_TIME_REWARD = "LIMITED_TIME_REWARD"
    const val DAILY_PUTT = "DAILY_PUTT"
    const val HAPPY_HOUR = "HAPPY_HOUR"
    const val FREE_CASH = "FREE_CASH"
    const val BIGGER_PRIZES = "BIGGER_PRIZES"
    const val TOURNAMENT = "TOURNAMENT"
    const val MULTIPLAYER = "MULTIPLAYER"
    const val NEW_GAME = "NEW_GAME"
    const val NEW_GAME_MODE = "NEW_GAME_MODE"
    const val HOT_STREAK = "HOT_STREAK"
}
