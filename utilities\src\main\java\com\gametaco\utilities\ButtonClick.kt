package com.gametaco.utilities

import android.os.SystemClock

private const val clickWaitTimeSeconds = 1 // (Default: 1 second)
private var lastClickTime = 0L

fun buttonClickAllowed() : Boolean {
    var deltaTime = SystemClock.elapsedRealtimeNanos() - lastClickTime
    return (deltaTime > clickWaitTimeSeconds * 1000000000L)
}

fun resetButtonClick() {
    lastClickTime = SystemClock.elapsedRealtimeNanos()
}
