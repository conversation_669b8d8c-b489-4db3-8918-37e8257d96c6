package com.gametaco.app_android_fd.ui.modifiers

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer

/**
 * Animated fade view from 0.5 to 1 with
 * @param enable is fading?
 */
@Composable
fun Modifier.fade(enable: Boolean): Modifier {

    val alpha by animateFloatAsState(if (enable) 0.5f else 1.0f)
    return this then Modifier.graphicsLayer { this.alpha = alpha }
}