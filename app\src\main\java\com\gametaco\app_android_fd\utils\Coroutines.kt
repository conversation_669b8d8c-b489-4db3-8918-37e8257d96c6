package com.gametaco.app_android_fd.utils

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.launch

data class CoroutineScopes(
    val main: CoroutineScope = CoroutineScope(Dispatchers.Main),
    val io: CoroutineScope = CoroutineScope(Dispatchers.IO),
)


fun <T> Flow<T>.collectInScope(scope: CoroutineScope, collector: FlowCollector<T>): Job =
    scope.launch {
        collect(collector)
    }
