#!/bin/zsh

export PATH="$PATH:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0"

# Keystore information
SOURCE_APK_PATH="$1"
KEYSTORE_PATH="./debug.keystore"
KEYSTORE_ALIAS="$2"
KEYSTORE_PASSWORD="$3"
BUILD_CONFIG="$4"

SIGNED_APK_PATH="signed-aligned-${BUILD_CONFIG}.apk"
ALIGNED_APK_PATH="aligned-${BUILD_CONFIG}.apk"

rm -f $SIGNED_APK_PATH $ALIGNED_APK_PATH

if [ ! -f "$SOURCE_APK_PATH" ]; then
  echo "ERROR: Source APK not found at $SOURCE_APK_PATH"
  exit 1
fi

# Align the APK
zipalign -p 4 $SOURCE_APK_PATH $ALIGNED_APK_PATH

# Sign the APK using apksigner
apksigner sign --min-sdk-version=18 --ks $KEYSTORE_PATH --ks-key-alias $KEYSTORE_ALIAS --ks-pass pass:$KEYSTORE_PASSWORD --out $SIGNED_APK_PATH $ALIGNED_APK_PATH

# Verify the APK
apksigner verify $SIGNED_APK_PATH

# Extract versionCode and versionName from the signed APK
VERSION_NAME=$(aapt dump badging $SIGNED_APK_PATH | grep "versionName" | sed -e "s/.*versionName='//" -e "s/' .*//")
VERSION_CODE=$(aapt dump badging $SIGNED_APK_PATH | grep "versionCode" | sed -e "s/.*versionCode='//" -e "s/' .*//")
echo "Version Name: $VERSION_NAME, Version Code: $VERSION_CODE"

# Construct the APK name
OUT_APK_NAME="FDLauncher-${BUILD_CONFIG}-${VERSION_CODE}-${VERSION_NAME}.apk"
echo $OUT_APK_NAME

aws s3 cp "$SIGNED_APK_PATH" s3://faceoff-android-dev-release/${OUT_APK_NAME}

WEBHOOK_MSG="Android ${BUILD_CONFIG} build ${VERSION_CODE} is available on S3"
curl -H 'Content-Type: application/json' -d "{\"text\": \"$WEBHOOK_MSG\"}" "https://kromestudios.webhook.office.com/webhookb2/05a88d58-8ef6-40ee-af9d-9d9f6a0574ba@a75bb70f-d2b8-4001-ba57-9c936408ef0e/IncomingWebhook/b92beb3fb0c348c7bf0f61ad7f982bc2/218820b0-a710-4340-9b20-1e5ebb9766d1"