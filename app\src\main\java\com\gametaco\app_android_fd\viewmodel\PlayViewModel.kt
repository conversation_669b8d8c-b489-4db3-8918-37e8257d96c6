package com.gametaco.app_android_fd.viewmodel

import android.os.Looper
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.APIGameTournamentEntryTicketsInfo
import com.gametaco.app_android_fd.data.entity.APIGameTournamentsGroupV2
import com.gametaco.app_android_fd.data.entity.APIGameTournamentsResponseV2
import com.gametaco.app_android_fd.data.entity.APIGamesResponseGame
import com.gametaco.app_android_fd.data.entity.APITournament
import com.gametaco.app_android_fd.data.entity.APITournamentGameMode
import com.gametaco.app_android_fd.data.entity.APITournamentGroupType
import com.gametaco.app_android_fd.data.entity.APITournamentsResponse
import com.gametaco.app_android_fd.data.entity.EntryFee
import com.gametaco.app_android_fd.data.entity.GAME_NAME
import com.gametaco.app_android_fd.data.entity.GCGameData
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.DeepLinkManager
import com.gametaco.app_android_fd.manager.ErrorManager
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.manager.analytics.TournamentAnalytics
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.utils.toDate
import com.gametaco.utilities.ResourceState
import io.sentry.Sentry
import io.sentry.SentryEvent
import io.sentry.SentryLevel
import io.sentry.protocol.Message
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

data class OnReLoadPlayDataEvent(val refresh: Boolean)
class PlayViewModel : ViewModel() {
    init {
        Logger.d(TAG, "Init PlayViewModel")

        if(!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this)
    }

    companion object {
        const val TAG = "PlayViewModel"
        val instance: PlayViewModel by lazy { PlayViewModel() }
    }

    private val tournamentManager: TournamentManager by lazy {
        TournamentManager.instance
    }

    private val _contestData : MutableStateFlow<ResourceState<APITournamentsResponse>> = MutableStateFlow(
        ResourceState.Loading())
    val contestData : StateFlow<ResourceState<APITournamentsResponse>> = _contestData

    private val _groups : MutableStateFlow<List<APIGameTournamentsGroupV2>> = MutableStateFlow(listOf())
    val groups = _groups.asStateFlow()

    var practiceTournamentId : String? = null

    private val _currentGroup:MutableStateFlow<String?> = MutableStateFlow(null)
    val currentGroup = _currentGroup.asStateFlow()
    fun changeCurrentGroup(group:String?){
        if(group != _currentGroup.value){
            _currentGroup.value = group
        }
    }

    private fun fixupTournamentData(data : APIGameTournamentsResponseV2) {
        data.groups.forEach{group ->
            group.tournaments.forEach{tournament ->
                //todo: fix the brand
//                tournament.brand = brand.name
//                tournament.brand_description = brand.description
                tournament.game_display_name = data.display_name
                tournament.game_mode_name = group.name
                tournament.group_type = group.group_type
                tournament.is_free_entry = data.tournament_entry_tickets.contains(tournament.id)
                val free_tickets = data.tournament_entry_tickets_info?.filter { it.tournament_id == tournament.id }?.sortedBy { it.expires_at?.toDate()?.time }
                val free_tickets_withExpiry = free_tickets?.filter { it.expires_at != null }?.sortedBy { it.expires_at?.toDate()?.time } ?: listOf()
                val free_tickets_withOutExpiry = free_tickets?.filter { it.expires_at == null } ?: listOf()
                tournament.free_entry_ticket_without_expiry_number = free_tickets_withOutExpiry.size
                if(free_tickets_withOutExpiry.isNotEmpty()){
                    tournament.free_tickets = free_tickets_withExpiry + free_tickets_withOutExpiry.first()
                }else if(free_tickets_withExpiry.isNotEmpty()){
                    tournament.free_tickets = free_tickets_withExpiry
                }
            }
        }
    }

    private suspend fun getContestDataForGameId(gameId: String) {
        if(AuthenticationManager.instance.isGuest) {
            val tournamentsDataResponse = WorldWinnerAPI.instance.getGuestTournamentsByGameID(gameId)

            if(tournamentsDataResponse.isSuccess()) {
                _contestData.value = tournamentsDataResponse
                practiceTournamentId = null
                updateTournamentsToUtilityManager(tournamentsDataResponse, gameId)
            } else if (tournamentsDataResponse.isError()) {
                val errorResponse = tournamentsDataResponse as ResourceState.Error
                _contestData.value = ResourceState.Error(errorResponse.error, errorResponse.code)

                ErrorManager.instance.handleResourceStateError(tournamentsDataResponse)
            }
        } else {
            val tournamentsDataResponse = WorldWinnerAPI.instance.getTournamentsByGameIdSortedV2(gameId)
            if(tournamentsDataResponse.isSuccess()){
                val res = (tournamentsDataResponse as ResourceState.Success).data

                practiceTournamentId = res.practice_tournament_id

                fixupTournamentData(res)
                
                _groups.value = res.groups.sortedBy { it.order }
                if(_groups.value.size > 0){
                    _currentGroup.value =_groups.value[0].groupKey
                }else{
                    _currentGroup.value = null
                }

                val list:MutableList<APITournament> = mutableListOf()
                res.groups.forEach{group ->
                    list += group.tournaments
                }
                val all:ResourceState<APITournamentsResponse> = ResourceState.Success(
                    data = APITournamentsResponse(
                        results = list,
                        count = list.size
                    )
                )
                _contestData.value = all
                updateTournamentsToUtilityManager(all, gameId)
            } else if (tournamentsDataResponse.isError()) {
                val errorResponse = tournamentsDataResponse as ResourceState.Error
                _contestData.value = ResourceState.Error(errorResponse.error, errorResponse.code)
                ErrorManager.instance.handleResourceStateError(tournamentsDataResponse)
            }
        }

    }

    private fun updateTournamentsToUtilityManager(tournamentResponse : ResourceState<APITournamentsResponse>, game_id : String) {

        if(tournamentResponse.isSuccess()) {
            val responseData = (tournamentResponse as ResourceState.Success).data

            for (tournament in responseData.results) {
                tournamentManager.addTournamentsIdsForGameId(tournament, game_id)
            }
        }
    }

    private fun getContestDataForTournamentIds(tournamentIds: String){
        viewModelScope.launch (Dispatchers.IO){
            if(AuthenticationManager.instance.isGuest) {
                val tournamentsDataResponse = WorldWinnerAPI.instance.getGuestTournamentsByTournamentIDs(tournamentIds)
                _contestData.value = tournamentsDataResponse
            } else {
                val tournamentsDataResponse = WorldWinnerAPI.instance.getTournamentsByTournamentIDs(tournamentIds)
                _contestData.value = tournamentsDataResponse
            }
        }
    }

    private val _gameModes: MutableList<APITournamentGameMode> = mutableListOf()
    private val _tournamentsByGameModeId: MutableMap<String, MutableList<APITournament>> = mutableMapOf()


    fun getTournamentsByGameModeId (
        gameModeId: String
    ): List<APITournament>? {
        if (_tournamentsByGameModeId.containsKey(gameModeId)) {
            return _tournamentsByGameModeId[gameModeId]
        }

        return null
    }

    fun getGameModes(): List<APITournamentGameMode> {
        return _gameModes
    }

    fun processTournamentData(
        tournaments: List<APITournament>
    ) {
        _gameModes.clear()
        val gameModeIds: MutableSet<String> = mutableSetOf()

        for (tournament in tournaments){
            val gameMode = tournament.game_mode
            if(gameMode != null){
                if (!gameModeIds.contains(gameMode.id)){
                    _gameModes.add(gameMode)
                    gameModeIds.add(gameMode.id)

                    _tournamentsByGameModeId[gameMode.id] = mutableListOf(tournament)
                }
                else {
                    _tournamentsByGameModeId[gameMode.id]?.add(tournament)
                }
            }
        }

        _gameModes.sortedBy { mode -> mode.display_order }
    }

    private val _gameData: MutableStateFlow<GCGameData?> = MutableStateFlow(null)
    val gameData:StateFlow<GCGameData?> = _gameData.asStateFlow()

    private val _game: MutableStateFlow<APIGamesResponseGame?> = MutableStateFlow(null)
    val game:StateFlow<APIGamesResponseGame?> = _game.asStateFlow()

    private val _forceShowPreview: MutableStateFlow<Boolean> = MutableStateFlow(false)
    val forceShowPreview:StateFlow<Boolean> = _forceShowPreview.asStateFlow()

    fun setForceShowPreview(force : Boolean) {
        _forceShowPreview.value = force
    }

    fun setGameData(
        gameData: GCGameData?,
        game: APIGamesResponseGame?
    ){
        Logger.d(TAG, "setGameData ${gameData}\n  ${game}")

        this._gameData.value = gameData
        this._game.value = game

        if (gameData != null) {
            viewModelScope.launch {
                getContestDataForGameId(gameData.game_id)
            }
        }
    }


    fun joinTournament(tournamentId: String, gameId: String, tournament: APITournament? = null) {
        Logger.d(TAG, "joinTournament tournamentId:${tournamentId} gameId:${gameId}")

        val tournamentAnalytics = tournament?.let {
            TournamentAnalytics(contestId = tournament.id, tournamentInstanceEntryId = tournamentId ?: "Unknown",
                gameId = gameId, gameName = tournament.game_display_name ?: "Unknown",
                gameMode = tournament.brand ?: "Unknown", stake = tournament.entry_fee ?: "0.00")
        }

        TournamentManager.instance.joinTournament(
            tournamentId = tournamentId,
            gameId = gameId,
            tournamentAnalytics = tournamentAnalytics,
            entryFee = if (tournament?.is_free_entry == true) EntryFee("0.00",tournament.entry_fee) else EntryFee(tournament?.entry_fee),
        )
    }

    fun logP1EscapeHatchEvent(gameId : String?) {
        val event = SentryEvent().apply {
            level = SentryLevel.INFO
            message = Message().apply { formatted = "P1 ESCAPE HATCH" }
            setTag("game_id", gameId ?: "unknown")
            setTag("user_id", AuthenticationManager.instance.apiMe?.id ?: "unknown")
        }

        Sentry.captureEvent(event)

    }

    fun logJoinFailedEvent(reason: String, tournament: APITournament) {
        val properties = mutableMapOf<String, Any>()
        properties["Error Summary"] = reason
        properties["Tournament Instance ID"] = tournament.id
        properties["Game Name"] = tournament.game_display_name ?: "Unknown"
        properties["Stake"] = tournament.entry_fee ?: "0.00"
        properties["Game Mode"] = tournament.game_mode?.name ?: "Unknown"
        properties["Tournament Instance Entry ID"] = tournament.id

        AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Game_Failed.value,
            properties = properties))
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReLoadPlayDataEventHandler(event: OnReLoadPlayDataEvent) {
        refreshData()
    }

    fun refreshData() {
        if (gameData.value != null) {
            viewModelScope.launch(Dispatchers.IO) {
                _gameData.value?.let { gameData ->
                    getContestDataForGameId(gameData.game_id)
                }
            }
        }
    }

    fun jumpToPlayView(gameId:String){
        viewModelScope.launch(Dispatchers.Main){
            val selectedGameData = GamesViewModel.instance.getGameDataById(gameId)
            val selectedGameCatalogData = GamesViewModel.instance.getGCGameDataForGameId(gameId)

            if (selectedGameData == null || selectedGameCatalogData == null) {
                DeepLinkManager.instance.alertGameIsNotAvailable()
            } else {
                setGameData(
                    gameData = selectedGameCatalogData,
                    game = selectedGameData
                )
                setForceShowPreview(false)
                NavManager.instance.navigateClearBackStack(Routes.GAMES_SCREEN)
                NavManager.instance.navigate(Routes.CONTEST_SCREEN)

                if(LeaderboardViewModel.instance.isShowing()){
                    LeaderboardViewModel.instance.closeLeaderboard()
                }
            }
        }
    }
    fun jumpToPlayerViewByName(gameName: String){
        val game = GamesViewModel.instance.getGameDateByName(gameName)
        if(game?.id != null){
            jumpToPlayView(game.id)
        }else{
            Logger.e(TAG,"jumpToPlayerViewByName error, no game found for ${gameName} ")
        }
    }
    fun getModeIcon(groupType: APITournamentGroupType,gameModeName:String,gameName:String):Int{
        if(groupType.name == APITournamentGroupType.TURBOS.name){
            return resources.R.drawable.ic_popular_lightning
        }else if(groupType.name == APITournamentGroupType.REPEAT_AND_BEAT.name){
            return resources.R.drawable.ic_spin_back
//        }else if(groupType.name == APITournamentGroupType.FEATURED.name){
        }else if(gameModeName?.uppercase()?.contains("DAILY") == true){
            return resources.R.drawable.ic_seasonal_checklist
        }else{
            if(gameName == GAME_NAME.SWISH_SHOWDOWN.value){
                if(gameModeName == "Expert"){
                    return resources.R.drawable.ic_game_expert
                }else if(gameModeName == "Three Point Contest"){
                    return resources.R.drawable.ic_game_ssd_three_point_contest
                }else if(gameModeName == "Quick Rack"){
                    return resources.R.drawable.ic_popular_lightning
                }else if(gameModeName == "Daily Shot" || gameModeName == "Daily Shot (Expert)"){
                    return resources.R.drawable.ic_seasonal_checklist
                }
            }else if(gameName == GAME_NAME.BOGGLE.value){
                if(gameModeName == "Single Round 4x4"){
                    return resources.R.drawable.ic_game_boggle_4x4
                }else if(gameModeName == "Single Round 5x5"){
                    return resources.R.drawable.ic_game_boggle_5x5
                }
            }else if(gameName == GAME_NAME.PRO_POPPER.value){
                if(gameModeName == "Beginner"){
                    return resources.R.drawable.ic_game_beginner
                }else if(gameModeName == "Daily"){
                    return resources.R.drawable.ic_seasonal_checklist
                }else if(gameModeName == "Puzzle"){
                    return resources.R.drawable.ic_game_puzzle
                }else if(gameModeName == "Blitz"){
                    return resources.R.drawable.ic_popular_lightning
                }else if(gameModeName == "Sudden Death"){
                    return resources.R.drawable.ic_game_sudden_death
                }
            }else if(gameName == GAME_NAME.PUZZLE_PYRAMID.value){
                if(gameModeName == "Upside Down"){
                    return resources.R.drawable.ic_game_upside_down
                }else if(gameModeName == "More Matches"){
                    return resources.R.drawable.ic_game_more_matches
                }else if(gameModeName == "Quick Round"){
                    return resources.R.drawable.ic_popular_lightning
                }
            }else if(gameName == GAME_NAME.FAIRWAY_FRENZY.value){
                if(gameModeName == "Daily Putt"){
                    return resources.R.drawable.ic_seasonal_checklist
                }else if(gameModeName == "Putting Green"){
                    return resources.R.drawable.ic_game_putting_green
                }else if(gameModeName == "Calm Weather"){
                    return resources.R.drawable.ic_game_calm_weather
                }else if(gameModeName == "Practice Game"){
                    return resources.R.drawable.ic_game_practice_game
                }else if(gameModeName == "Three Hole Showdown"){
                    return resources.R.drawable.ic_game_3_hole
                }else if(gameModeName == "Nine Hole Showdown"){
                    return resources.R.drawable.ic_game_9_hole
                }
            }else if(gameName == GAME_NAME.CUE_MASTERS.value){
                if(gameModeName == "One Life"){
                    return resources.R.drawable.ic_game_one_life
                }else if(gameModeName == "Practice"){
                    return resources.R.drawable.ic_game_practice_game
                }else if(gameModeName == "Aim Low"){
                    return resources.R.drawable.ic_game_aim_low
                }else if(gameModeName == "Bonus Pocket"){
                    return resources.R.drawable.ic_game_bonus_pocket
                }
            }else if(gameName == GAME_NAME.SCRABBLE_CUBES.value){
                if(gameModeName == "Survival"){
                    return resources.R.drawable.ic_game_countdown
                }else if(gameModeName == "One Use Cubes"){
                    return resources.R.drawable.ic_game_one_use_cube
                }
            }else if(gameName == GAME_NAME.BLOCK_TRAIL.value){
                if(gameModeName == "Blitz"){
                    return resources.R.drawable.ic_popular_lightning
                }else if(gameModeName == "2X Scoring"){
                    return resources.R.drawable.ic_game_2x_score
                }
            }else if(gameName == GAME_NAME.WHEEL_OF_FORTUNE.value){
                if(gameModeName == "Quick Solve" || gameModeName == "Quick Solve: Warm Up"){
                    return resources.R.drawable.ic_popular_lightning
                }else if(gameModeName == "Toss Up" || gameModeName == "Toss Up: Warm Up"){
                    return resources.R.drawable.ic_game_countdown
                }
            }else if(gameName == GAME_NAME.GRAND_SLAM_SUPERSTAR.value){
                if(gameModeName == "Quick Round"){
                    return resources.R.drawable.ic_game_standard
                }else if(gameModeName == "Slugfest"){
                    return resources.R.drawable.ic_game_slugfest
                }else if(gameModeName == "Single Inning Showdown"){
                    return resources.R.drawable.ic_game_single_inning_showdown
                }else if(gameModeName == "Survival"){
                    return resources.R.drawable.ic_game_countdown
                }else if(gameModeName == "Daily Swing!"){
                    return resources.R.drawable.ic_seasonal_checklist
                }
            }else if(gameName == GAME_NAME.FIELD_GOAL_FACEOFF.value){
                if(gameModeName == "Sudden Death"){
                    return resources.R.drawable.ic_game_sudden_death
                }else if(gameModeName == "Survival"){
                    return resources.R.drawable.ic_game_countdown
                }else if(gameModeName == "Daily Kick"){
                    return resources.R.drawable.ic_seasonal_checklist
                }else if(gameModeName == "Quick Kick"){
                    return resources.R.drawable.ic_popular_lightning
                }
            }else if(gameName == GAME_NAME.SOLITAIRE_SPRINT.value){
                if(gameModeName == "Any Card"){
                    return resources.R.drawable.ic_game_any_card
                }else if(gameModeName == "Aces Loaded"){
                    return resources.R.drawable.ic_game_aces_loaded
                }else if(gameModeName == "Queens Rule"){
                    return resources.R.drawable.ic_game_queens_rule
                }else if(gameModeName == "1-Card Draw"){
                    return resources.R.drawable.ic_game_1_card_show
                }else if(gameModeName == "⚡\uFE0FSpeedy Sprint"){
                    return resources.R.drawable.ic_popular_lightning
                }
            }else if(gameName == GAME_NAME.DEEP_SEA_SEARCH.value){
                if(gameModeName == "Meander"){
                    return resources.R.drawable.ic_game_meander
                }else if(gameModeName == "Fireworks Frenzy"){
                    return resources.R.drawable.ic_game_fireworks_frenzy
                }else if(gameModeName == "Holiday Hunt"){
                    return resources.R.drawable.ic_game_holiday_hunt
                }else if(gameModeName == "Spooky Search"){
                    return resources.R.drawable.ic_game_spooky_search
                }else if(gameModeName == "Turkey Day Treasures"){
                    return resources.R.drawable.ic_game_turkey_day_treasures
                }
            }else if(gameName == GAME_NAME.GRONK_SPIKE_CORNHOLE.value){
                if(gameModeName == "Two Rounds"){
                    return resources.R.drawable.ic_game_two_rounds
                }
            }else if(gameName == GAME_NAME.BUENA_MATCH.value){
                if(gameModeName == "1-Board"){
                    return resources.R.drawable.ic_game_1_board
                }else if(gameModeName == "2-Board"){
                    return resources.R.drawable.ic_game_2_board
                }else if(gameModeName == "3-Board"){
                    return resources.R.drawable.ic_game_3_board
                }
            }
            return resources.R.drawable.ic_game_standard
        }
    }
}