package com.gametaco.app_android_fd.ui.screens

import <PERSON><PERSON><PERSON><PERSON>
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Divider
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.gametaco.app_android_fd.LocalErrorManager
import com.gametaco.app_android_fd.LocalModalPopupManager
import com.gametaco.app_android_fd.LocalNavManager
import com.gametaco.app_android_fd.data.api.APIGuestMe
import com.gametaco.app_android_fd.data.api.APIGuestMeMessage
import com.gametaco.app_android_fd.data.entity.APIActiveGoal
import com.gametaco.app_android_fd.data.entity.APIGoalType
import com.gametaco.app_android_fd.data.entity.APIGoalsTask
import com.gametaco.app_android_fd.data.entity.APIRefundReason
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntry
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntryExpandedResultScoreItem
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntryExpandedResultStatItem
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntryResult
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceStatus
import com.gametaco.app_android_fd.data.entity.APITournamentPlayerProfile
import com.gametaco.app_android_fd.data.entity.APITournamentUser
import com.gametaco.app_android_fd.data.entity.APITournamentUserStatus
import com.gametaco.app_android_fd.data.entity.entryFeeLabel
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.BrazeEventName
import com.gametaco.app_android_fd.manager.BrazeManager
import com.gametaco.app_android_fd.manager.ErrorManager
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.ModalPopupManager
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.models.LeaderboardDataModel
import com.gametaco.app_android_fd.models.ModalPopupData
import com.gametaco.app_android_fd.ui.components.ArrowMode
import com.gametaco.app_android_fd.ui.components.BottomSheetModal
import com.gametaco.app_android_fd.ui.components.CommonButton
import com.gametaco.app_android_fd.ui.components.ContentBox
import com.gametaco.app_android_fd.ui.components.ContentListBody
import com.gametaco.app_android_fd.ui.components.ContentTitle
import com.gametaco.app_android_fd.ui.components.CustomAnimationView
import com.gametaco.app_android_fd.ui.components.ExpandableContestDetails
import com.gametaco.app_android_fd.ui.components.ExpandableScoringBreakdown
import com.gametaco.app_android_fd.ui.components.GetRewardTypeString
import com.gametaco.app_android_fd.ui.components.GuestBlockComponent
import com.gametaco.app_android_fd.ui.components.KeyStatsItemView
import com.gametaco.app_android_fd.ui.components.LeaderboardPlayerView
import com.gametaco.app_android_fd.ui.components.LeaderboardResultType
import com.gametaco.app_android_fd.ui.components.LeaderboardTournamentReEntryView
import com.gametaco.app_android_fd.ui.components.PlayerScoreMarquee
import com.gametaco.app_android_fd.ui.components.TooltipWithBorder
import com.gametaco.app_android_fd.ui.components.VideoComponent
import com.gametaco.app_android_fd.ui.components.getActiveRewardElement
import com.gametaco.app_android_fd.ui.modifiers.clickableWithoutRipple
import com.gametaco.app_android_fd.ui.modifiers.onAppear
import com.gametaco.app_android_fd.ui.modifiers.onDisappear
import com.gametaco.app_android_fd.ui.popups.CompeteForCashPopUp
import com.gametaco.app_android_fd.ui.popups.MaxPlaysPopUpJoinCTA
import com.gametaco.app_android_fd.ui.popups.MaxPlaysPopUpLeaderboard
import com.gametaco.app_android_fd.ui.theme.AppFont
import com.gametaco.app_android_fd.ui.theme.AppandroidwwTheme
import com.gametaco.app_android_fd.ui.theme.BlueMinimalBackgroundSubtle
import com.gametaco.app_android_fd.ui.theme.ShadowFade
import com.gametaco.app_android_fd.ui.theme.theme_light_primary
import com.gametaco.app_android_fd.ui.theme.theme_light_tertiary
import com.gametaco.app_android_fd.utils.format
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.utils.toDate
import com.gametaco.app_android_fd.utils.toDollarWithCommaString
import com.gametaco.app_android_fd.utils.toNumberWithCommaString
import com.gametaco.app_android_fd.viewmodel.GoalsViewModel
import com.gametaco.app_android_fd.viewmodel.LeaderboardViewModel
import com.gametaco.app_android_fd.viewmodel.LoginViewModel
import com.gametaco.app_android_fd.viewmodel.OnReLoadGoalsDataEvent
import com.gametaco.utilities.ResourceState
import com.gametaco.utilities.STR
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import resources.R
import toDp

const val TAG_LEADERBOARD_SCREEN = "LeaderboardScreen"

@Composable
fun LeaderboardScreen(leaderboardViewModel: LeaderboardViewModel) {
    val dataModel = leaderboardViewModel.leaderboardDataModel.collectAsState()
    LeaderboardScreen(dataModel = dataModel.value)
}

@Composable
fun LeaderboardScreen(dataModel: LeaderboardDataModel) {
    val navManager = LocalNavManager.current
    val modalPopupManager = LocalModalPopupManager.current
    val errorManager = LocalErrorManager.current
    val isGuest = LocalInspectionMode.current || AuthenticationManager.instance.isGuest

    val tournamentInstanceEntryId = dataModel.tournamentInstanceEntryId
    val isLoading = dataModel.isLoading
    val data = dataModel.data
    val connectedGoals = dataModel.connectedGoals
    val placeholderOpponentCount = LeaderboardViewModel.instance.remainingOpponentSpots(dataModel)
    val showFtue = dataModel.showFtue && !isGuest
    val onCloseFtue = dataModel.onCloseFtue
    val onPlayAgain = dataModel.onPlayAgain
    val isAfterGame = dataModel.isAfterGame

    val showRewardsProgress = remember{ mutableStateOf(false) }
    val goalInfoModalExpanded = remember { mutableStateOf(false) }
    val replayURL = LeaderboardViewModel.instance.replayURL.collectAsState()
    val showProfile = LeaderboardViewModel.instance.profileId.collectAsState()
    val profileData = LeaderboardViewModel.instance.profileData.collectAsState()
    val localPlayerReplayPath by TournamentManager.instance.getReplayFilePathWithTimeout(tournamentInstanceEntryId).collectAsState(
        initial = null
    )
    if(tournamentInstanceEntryId == null){
        showRewardsProgress.value = false
        goalInfoModalExpanded.value = false
    }
    val activeRewardInfo:MutableState<APIActiveGoal?> = remember { mutableStateOf(null) }
    val activeClusterRewardTotalTitle:MutableState<String?> = remember { mutableStateOf(null) }
    val allGoals = dataModel.goals
//    val allGoals by GoalsViewModel.instance.goals.collectAsState()
    val relatedGoals: MutableState<List<APIActiveGoal>> = remember { mutableStateOf(listOf()) }
    if(connectedGoals.isNotEmpty()){
        if(allGoals.isSuccess()){
            val goalDataModel = (allGoals as ResourceState.Success).data
            relatedGoals.value = goalDataModel.activeGoals.filter {goal ->
                connectedGoals.any { id ->
                    goal.containsTask(id)
                }
            }.sortedBy { it.displayOrder }
        }
    } else {
        relatedGoals.value = listOf()
    }

    tournamentInstanceEntryId?.let{
        LaunchedEffect(Unit) {
            //check and show popups for guests that have exceeded their allowed number of tournaments
            if (isGuest) {
                configureGuestPopups(modalPopupManager, errorManager, navManager, true)
            }
        }
    }

//    var enterTransition: EnterTransition = slideInVertically(initialOffsetY = { fullHeight ->  fullHeight})
//    var exitTransition: ExitTransition = slideOutVertically(targetOffsetY = { fullHeight ->  fullHeight})

    BottomSheetModal(
        title = "Leaderboard",
        showBackButton = false,
        showDivider = true,
        expanded = tournamentInstanceEntryId != null,
        closeAction = {
            if (dataModel.showFtue) {
                dataModel.onCloseFtue(data?.game_id)
                navManager.navigate(Routes.GAMES_SCREEN)
            } else if (isGuest) {
                navManager.navigate(Routes.GAMES_SCREEN)
            }
            
            dataModel.onClose()

            AnalyticsManager.instance.logEvent(
                AnalyticsEvent(analyticsEvent = AnalyticsEventName.Modal_Closed.value,
                properties = mapOf("Modal Name" to "Leaderboard Bottom Sheet"))
            )

        },
        footer = {
            tournamentInstanceEntryId?.let {
                LeaderboardFooter(
                    navManager = navManager,
                    isLoading = isLoading,
                    isGuest = isGuest,
                    data = data,
                    showFtue = showFtue,
                    onCloseFtue = {onCloseFtue(data?.game_id)},
                    onPlayAgain = onPlayAgain,
                    showRewardTab = !isGuest && !dataModel.showFtue && isAfterGame,
                    showRewardsProgress = showRewardsProgress,
                    relatedGoals = relatedGoals,
                    goalInfoModalExpanded = goalInfoModalExpanded,
                    activeRewardInfo = activeRewardInfo
                )
            }
        },
        contentBackgroundColor = Color.White,
    ) {
        if (isLoading) {
            Spacer(modifier = Modifier.height(200.dp))
            LoadingScreen(backgroundColor = MaterialTheme.colorScheme.onPrimary)
        } else {
            // TODO: REMOVE PLACEHOLDER DATA //
//            val placeholderData = data?.copy(
//                expanded_results = mapOf(
//                    AuthenticationManager.instance.apiMe?.fanduel_id.toString() to APITournamentInstanceEntryExpandedResult(
//                        keyStats = listOf(
//                            APITournamentInstanceEntryExpandedResultStatItem(
//                                "STONES CLEARED",
//                                "28"
//                            ),
//                            APITournamentInstanceEntryExpandedResultStatItem(
//                                "AVG STREAK",
//                                "3"
//                            )
//                        ),
//                        subScores = listOf(
//                            APITournamentInstanceEntryExpandedResultScoreItem(
//                                "MATCH SCORE",
//                                1000,
//                                "20 X 50PTS",
//                            ),
//                            APITournamentInstanceEntryExpandedResultScoreItem(
//                                "STONES REMAINING",
//                                750,
//                                "15 X 50PTS",
//                            ),
//                            APITournamentInstanceEntryExpandedResultScoreItem(
//                                "REDRAW COST",
//                                -150,
//                                "3 X -50PTS",
//                            )
//                        )
//                    ),
//                    "PLACEHOLDER OPPONENT ID" to APITournamentInstanceEntryExpandedResult(
//                        keyStats = listOf(
//                            APITournamentInstanceEntryExpandedResultStatItem(
//                                "STONES CLEARED",
//                                "24"
//                            ),
//                            APITournamentInstanceEntryExpandedResultStatItem(
//                                "AVG STREAK",
//                                "4"
//                            )
//                        ),
//                        subScores = listOf(
//                            APITournamentInstanceEntryExpandedResultScoreItem(
//                                "MATCH SCORE",
//                                1100,
//                                "22 X 50PTS",
//
//                            ),
//                            APITournamentInstanceEntryExpandedResultScoreItem(
//                                "STONES REMAINING",
//                                0,
//                                "0 X 50PTS",
//
//                            ),
//                            APITournamentInstanceEntryExpandedResultScoreItem(
//                                "REDRAW COST",
//                                -150,
//                                "3 X -50PTS",
//                            )
//                        )
//                    )
//                )
//            )
            // TODO: REMOVE PLACEHOLDER DATA //
            LeaderboardContent(data, isGuest, dataModel, placeholderOpponentCount,localPlayerReplayPath)
        }
    }

    LeaderboardRewardsProgress(
        navManager = navManager,
        showRewardsProgress = showRewardsProgress,
        isLoading = isLoading,
        isGuest = isGuest,
        data = data,
        showFtue = showFtue,
        onCloseFtue = {onCloseFtue(data?.game_id)},
        onPlayAgain = onPlayAgain,
        relatedGoals = relatedGoals,
        goalInfoModalExpanded = goalInfoModalExpanded,
        activeRewardInfo = activeRewardInfo,
        activeClusterRewardTotalTitle = activeClusterRewardTotalTitle
    )

    GoalsInfoModal(goalInfoModalExpanded, activeRewardInfo, activeClusterRewardTotalTitle)
    LeaderboardPlayerProfile(!showProfile.value.isNullOrEmpty(),profileData.value,data)
    LeaderboardReplay(replayURL = replayURL.value,data,tournamentInstanceEntryId)
}

suspend fun configureGuestPopups(
    modalPopupManager: ModalPopupManager,
    errorManager: ErrorManager,
    navManager: NavManager,
    pullFreshGuestMe : Boolean
) {
    val TAG = TAG_LEADERBOARD_SCREEN

    if(pullFreshGuestMe) {
        val guestMeResponse = AuthenticationManager.instance.getGuestMe()

        when (guestMeResponse) {
            is ResourceState.Loading -> {
            }

            is ResourceState.Success -> {

                showGuestPopups(
                    modalPopupManager,
                    navManager,
                    (guestMeResponse as ResourceState.Success).data,
                    false
                )
            }

            is ResourceState.Error -> {
                Logger.d(TAG, "getGuestMe error: ${guestMeResponse.error}")
                errorManager.handleResourceStateError(guestMeResponse)
            }
        }

    } else {
        showGuestPopups(
            modalPopupManager,
            navManager,
            AuthenticationManager.instance.apiGuestMe,
            false)
    }

}

suspend fun showGuestPopups(
    modalPopupManager: ModalPopupManager,
    navManager: NavManager,
    apiGuestMe: APIGuestMe?,
    fromJoinCTA: Boolean
) {

    if (apiGuestMe?.message == APIGuestMeMessage.hard.rawValue) {
        if(fromJoinCTA) {
            modalPopupManager.showModal(
                modalData = ModalPopupData(
                    composeFunction = { modalData -> MaxPlaysPopUpJoinCTA(modalData) },
                    onConfirm = { LoginViewModel.instance.fanduelSignupFromGuestMode() },
                    onCancel = { }
                )
            )
        } else {
            modalPopupManager.showModal(
                modalData = ModalPopupData(
                    composeFunction = { modalData -> MaxPlaysPopUpLeaderboard(modalData) },
                    onConfirm = { LoginViewModel.instance.fanduelSignupFromGuestMode() },
                    onCancel = { navManager.navigate(Routes.GAMES_SCREEN) }
                )
            )
        }
    } else if (apiGuestMe?.message == APIGuestMeMessage.soft.rawValue) {
        modalPopupManager.showModal(
            modalData = ModalPopupData(
                composeFunction = { modalData -> CompeteForCashPopUp(modalData) },
                onConfirm = { LoginViewModel.instance.fanduelSignupFromGuestMode() }
            )
        )
    }
}


@Composable
private fun LeaderboardContent(
    data: APITournamentInstanceEntry?,
    isGuest: Boolean,
    dataModel: LeaderboardDataModel,
    placeholderOpponentCount: Int,
    localPlayerReplayPath:String?,
) {
    Logger.d("tournament_instance_status:${data?.tournament_instance_status} is_winner:${data?.is_winner} ")

    val resultType = leaderboardResultType(isGuest, data)

    Box(
        modifier = Modifier
            .fillMaxSize()
            .onAppear {
                val event = JSONObject()
                event.put("user_id", AuthenticationManager.instance.apiMe?.id)
                event.put("external_user_id", AuthenticationManager.instance.apiMe?.fanduel_id)
                event.put("fees", data?.entry_fee)
                event.put("game_id", data?.game_id)
                event.put("game_name", data?.game_display_name)
                event.put("is_winner", data?.is_winner)
                event.put("prize_amount", data?.prize_amount)
                event.put("score", data?.score)
                event.put("status", data?.tournament_instance_status)
                event.put("tournament_id", data?.tournament_id)
                event.put("tournament_name", data?.game_display_name)
                BrazeManager.instance.logEvent(BrazeEventName.Contest_Results.value, event)
            }
    ) {
        Box {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.White),
            ) {
                val entryFee = data?.entry_fee?.toDouble()
                val isPaidContest = entryFee != null && entryFee > 0
                LeaderboardHeader(dataModel, resultType, isPaidContest)

                val expandedResultSize = data?.expanded_results?.size ?: 0
                val hasExpandedResults = expandedResultSize > 0

                var expandedResultsSameVersion = hasExpandedResults
                if(hasExpandedResults && expandedResultSize == 2){
                    var index = 0;
                    var result1 = listOf<String>()
                    var result2 = listOf<String>()

                    data?.expanded_results?.forEach{
                        val list =  it.value.subScores.map { score->
                            score.name
                        }.sorted()
                        if(index == 0){
                            result1 = list
                            index ++
                        }else{
                            result2 = list
                        }
                    }
                    expandedResultsSameVersion = result1 == result2
                }
                val isExpandedLeaderboard = data?.maximum_slots == 2 && hasExpandedResults && expandedResultsSameVersion

//                println("isExpandedLeaderboard:${isExpandedLeaderboard}")
                // Standard leaderboard
                if (!isExpandedLeaderboard) {
                    LeaderboardContestants(isGuest, data, placeholderOpponentCount)

                    if (data?.hasBeenRefunded() == true) {
                        LeaderboardRefunded(data)
                    }

                    if (!isGuest && !dataModel.showFtue) {
                        LeaderboardContestDetails(data, isExpandedLeaderboard = false)
                    }
                }
                //expanded leaderboard but refunded due to playing not started
                else if (resultType == LeaderboardResultType.REFUNDED && data?.refund_reason != APIRefundReason.onlyPlayer.rawValue){

                    LeaderboardRefunded(data)

                    if (!isGuest && !dataModel.showFtue) {
                        LeaderboardContestDetails(data = data, isExpandedLeaderboard = false)
                    }
                }
                // expanded leaderboard
                else if (data != null) {
                    val keyStats: MutableMap<String, Pair<APITournamentInstanceEntryExpandedResultStatItem?, APITournamentInstanceEntryExpandedResultStatItem?>> = mutableMapOf()
                    val subScores: MutableMap<String, Pair<APITournamentInstanceEntryExpandedResultScoreItem?, APITournamentInstanceEntryExpandedResultScoreItem?>> = mutableMapOf()

                    val playerId = AuthenticationManager.instance.apiMe?.id.toString()
                    var opponentId:String? = null
                    for (result in data.expanded_results?.entries!!) {
                        for (stat in result.value.keyStats) {
                            if (!keyStats.containsKey(stat.name)){
                                if (result.key == playerId) {
                                    keyStats[stat.name] = Pair(stat, null)
                                }
                                else {
                                    keyStats[stat.name] = Pair(null, stat)
                                }
                            }
                            else {
                                if (keyStats[stat.name] != null) {
                                    if (result.key == playerId) {
                                        keyStats[stat.name] = Pair(stat, keyStats[stat.name]!!.second)
                                    }
                                    else {
                                        keyStats[stat.name] = Pair(keyStats[stat.name]!!.first, stat)
                                    }
                                }
                            }
                        }
                        for (stat in result.value.subScores) {
                            if (!subScores.containsKey(stat.name)){
                                if (result.key == playerId) {
                                    subScores[stat.name] = Pair(stat, null)
                                }
                                else {
                                    subScores[stat.name] = Pair(null, stat)
                                }
                            }
                            else {
                                if (subScores[stat.name] != null) {
                                    if (result.key == playerId) {
                                        subScores[stat.name] = Pair(stat, subScores[stat.name]!!.second)
                                    }
                                    else {
                                        subScores[stat.name] = Pair(subScores[stat.name]!!.first, stat)
                                    }
                                }
                            }
                        }
                    }


                    var playerResults: APITournamentInstanceEntryResult? = null
                    var opponentResults: APITournamentInstanceEntryResult? = null
                    for (result in data.results) {
                        if (result.user != null && result.getUserName() == AuthenticationManager.instance.apiMe?.username) {
                            playerResults = result
                        }
                        else {
                            opponentResults = result
                            opponentId = result.user?.id
                        }
                    }

                    if (resultType == LeaderboardResultType.REFUNDED){
                        LeaderboardRefunded(data)
                    }
                    Box(
                        modifier = Modifier
                            .padding(horizontal = 16.dp)
                            .padding(bottom = 16.dp)
                            .background(MaterialTheme.colorScheme.surface)
                    ) {
                        ContentBox (
                            bodyPaddingValues = PaddingValues(),
                            cornerRoundness = 6.dp
                        ){
                            val opponentName: String
                            val opponentScore: String
                            var opponentPlacement: Int? = 1
                            var opponentAvatarUrl: String? = null
                            val opponentStatus = opponentResults?.status
                            when (resultType){
                                LeaderboardResultType.IN_PROGRESS -> {
                                    opponentName = if(opponentStatus == APITournamentUserStatus.NOT_STARTED) "Waiting for Opponent" else opponentResults?.getUserName() ?:"Waiting for Opponent"
                                    opponentScore = if(opponentStatus == APITournamentUserStatus.NOT_STARTED) "Not Started" else if (opponentResults != null) "Playing" else "Open"
                                    opponentAvatarUrl = if(opponentStatus == APITournamentUserStatus.NOT_STARTED) null else opponentResults?.user?.avatar_url
                                }
                                LeaderboardResultType.EXPIRED -> {
                                    opponentName = "No Opponent"
                                    opponentScore = "Expired"
                                }
                                LeaderboardResultType.REFUNDED -> {
                                    opponentName = "No Opponent"
                                    opponentScore = "Expired"
                                }
                                LeaderboardResultType.DECLINED -> {
                                    opponentName = "No Opponent"
                                    opponentScore = "Declined"
                                }
                                else -> {
                                    opponentName = opponentResults?.getUserName() ?:"Opponent"
                                    opponentScore = (opponentResults?.score ?: 0)
                                        .toNumberWithCommaString()
                                    opponentPlacement = opponentResults?.order
                                    opponentAvatarUrl = opponentResults?.user?.avatar_url
                                }
                            }

                            Column {

                                // Player scores
                                PlayerScoreMarquee(
                                    player1Id = playerId,
                                    player1Name = if (isGuest) "Faceoff guest"
                                        else AuthenticationManager.instance.apiMe?.username ?: "",
                                    player1Score = (playerResults?.score?: 0)
                                        .toNumberWithCommaString(),
                                    player1Placement = playerResults?.order,
                                    player1Icon = if (isGuest) null
                                        else AuthenticationManager.instance.apiMe?.avatar_url,
                                    player2Id = opponentId,
                                    player2Name = opponentName,
                                    player2Score = opponentScore,
                                    player2Placement = opponentPlacement,
                                    player2Icon = opponentAvatarUrl,
                                    player2Status = opponentStatus,
                                    game_mode_id = data.game_mode_id
                                )


                                if(ExperimentManager.instance.videoReplaysUI?.isAvailable() == true && ExperimentManager.instance.videoReplaysUI?.isActive() == true) {

                                    Logger.d("dataModel.localPlayerReplayPath:${localPlayerReplayPath} \nplayerResults?.replay_url: ${playerResults?.replay_url}")
                                    //only show replay when it is supported
                                    if((!localPlayerReplayPath.isNullOrEmpty() || playerResults?.replay_url != null) && opponentResults?.replay_url != null){
                                        LeaderboardViewModel.instance.viewedReplayButtons = true

                                        //add replay entries
                                        KeyStatsItemView(
                                            statTitle = "VIDEO REPLAYS",
                                            value1 = if(localPlayerReplayPath.isNullOrEmpty()) playerResults?.replay_url else localPlayerReplayPath,
                                            value2 = opponentResults?.replay_url
                                        )
                                    }
                                }

                                // Show winnings if game is complete and not guest
                                // TODO: Do they want winnings shown when both players have no winnings?
                                // TODO: For example: Free Entry events
                                if (!isGuest &&
                                    isPaidContest &&
                                    ((playerResults?.prize_amount ?: 0) != 0 ||
                                        (opponentResults?.prize_amount ?: 0) != 0)
                                ) {
                                    KeyStatsItemView(
                                        statTitle = "WINNINGS",
                                        value1 = playerResults?.prize_amount?.toDollarWithCommaString(),
                                        value2 = opponentResults?.prize_amount?.toDollarWithCommaString()
                                    )
                                }

                                // Key Stats
                                for (stat in keyStats) {
                                    KeyStatsItemView(
                                        statTitle = stat.key,
                                        value1 = stat.value.first?.value,
                                        value2 = stat.value.second?.value
                                    )
                                }

                                // Scoring Breakdown
                                LeaderboardScoringBreakdown(subScores)


                                // TODO: Find out how the expanded results are to work with the FTUE
                                if (!isGuest && !dataModel.showFtue) {
                                    if (resultType != LeaderboardResultType.IN_PROGRESS) {
                                        HorizontalDivider(
                                            modifier = Modifier.fillMaxWidth(),
                                            thickness = 1.dp,
                                            color = MaterialTheme.colorScheme.outlineVariant
                                        )
                                    }
                                    LeaderboardContestDetails(data, isExpandedLeaderboard = true)
                                }
                            }
                        }
                    }
                }
            }

            if (dataModel.showFtue && !isGuest) {
                ToolTipNiceWork(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(end = 16.dp, top = 272.dp),
                )
            }
        }
    }
}

@Composable
private fun LeaderboardHeader(
    dataModel: LeaderboardDataModel,
    resultType: LeaderboardResultType,
    isPaidContest: Boolean
) {
    val isGuest = LocalInspectionMode.current || AuthenticationManager.instance.isGuest
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
//                            .height(180.dp)
    ) {
        val animation = when (resultType) {
            LeaderboardResultType.DEFAULT -> R.raw.anim_lb_results
            LeaderboardResultType.WON -> R.raw.anim_lb_youwin
            LeaderboardResultType.IN_PROGRESS -> R.raw.anim_lb_inprogress
            LeaderboardResultType.TIED -> R.raw.anim_lb_youtied
            LeaderboardResultType.REFUNDED -> if (isPaidContest) R.raw.anim_lb_refunded
            else R.raw.anim_lb_results

            LeaderboardResultType.EXPIRED -> R.raw.anim_lb_expired
            LeaderboardResultType.DECLINED -> R.raw.anim_lb_declined
        }

        if (dataModel.data?.gradient_bottom_color != null && dataModel.data?.gradient_top_color != null) {
            val bottomColor =
                Color(android.graphics.Color.parseColor(dataModel.data?.gradient_bottom_color))
            val topColor =
                Color(android.graphics.Color.parseColor(dataModel.data?.gradient_top_color))
            val backgroundBrush = Brush.verticalGradient(listOf(topColor, bottomColor))

            CustomAnimationView(
                animation = R.raw.anim_lb_background_expanded,
                modifier = Modifier
                    .fillMaxSize()
                    .background(backgroundBrush)
            )
        } else {
            CustomAnimationView(
                animation = R.raw.anim_lb_background,
                modifier = Modifier
                    .fillMaxSize()
            )
        }
        CustomAnimationView(animation = animation, modifier = Modifier.fillMaxSize())
    }
}

@Composable
private fun ToolTipNiceWork(
    modifier: Modifier,
) {
    TooltipWithBorder(
        modifier = modifier,
        borderWidth = 1.dp,
        gapWidth = 20.dp,
        arrowPositionXNormalized = 0.75f,
        arrowMode = ArrowMode.Top,
        padding = 16.dp,
        cornerRadius = 8.dp,
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
                .width(216.dp)
                .defaultMinSize(minHeight = 60.dp)
        ) {
            Text(
                text = STR(R.string.tooltip_nice_work),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.secondary
            )
            Text(
                text = STR(R.string.tooltip_you_played_your_first_game),
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.onSurface,
                softWrap = true
            )
        }
    }
}

@Composable
private fun leaderboardResultType(
    isGuest: Boolean,
    data: APITournamentInstanceEntry?,
): LeaderboardResultType {
    var resultType = LeaderboardResultType.DEFAULT
    if (isGuest) {
        resultType = LeaderboardResultType.DEFAULT
    } else if (data?.hasBeenRefunded() == true) {
        resultType = LeaderboardResultType.REFUNDED
    } else if (data?.is_tie == true) {
        resultType = LeaderboardResultType.TIED
    } else {
        if (data?.tournament_instance_status == APITournamentInstanceStatus.Closed.rawValue) {
            if (data.is_winner == true) {
                resultType = LeaderboardResultType.WON
            } else {
                resultType = LeaderboardResultType.DEFAULT
            }
        } else if (data?.tournament_instance_status == APITournamentInstanceStatus.Refunded.rawValue) {
            resultType = LeaderboardResultType.REFUNDED
        } else {
            resultType = LeaderboardResultType.IN_PROGRESS
        }
    }
    return resultType
}

@Composable
private fun LeaderboardContestants(
    isGuest: Boolean,
    data: APITournamentInstanceEntry?,
    placeholderOpponentCount: Int,
) {
    Column() {
        if (!isGuest) {
            if(data?.tournament_instance_status == APITournamentInstanceStatus.Refunded.rawValue){//this should be refund case as no player is joining the tournament in 24hs
                if(data?.refund_reason == APIRefundReason.onlyPlayer.rawValue){
                    LeaderboardPlayerView(
                        model = APITournamentInstanceEntryResult(
                            user = APITournamentUser(
                                id = AuthenticationManager.instance.apiMe?.id ?:"",
                                username = AuthenticationManager.instance.apiMe?.username ?:"",
                                avatar_url = AuthenticationManager.instance.apiMe?.avatar_url),
                            score = data?.score,
                            order = 1,
                            prize_amount = null,
                            status = APITournamentUserStatus.COMPLETED,
                            replay_url = null),
                        game_mode_id = data.game_mode_id
                        )
                }
            }else{
                data?.results?.forEach {
                    LeaderboardPlayerView(model = it,game_mode_id = data.game_mode_id)
                }

                if (data?.can_reenter ?: false) {
                    LeaderboardTournamentReEntryView(data)
                }

                for (i in 1..placeholderOpponentCount) {
                    LeaderboardPlayerView(model = waitingPlayerHolderRank,game_mode_id = data?.game_mode_id)
                }
            }
        } else {

            LeaderboardPlayerView(
                APITournamentInstanceEntryResult(
                    user = APITournamentUser(
                        id = "1",
                        username = "Faceoff guest",
                        avatar_url = null
                    ),
                    score = TournamentManager.instance.guestScore,
                    prize_amount = "0.00",
                    order = 1,
                    status = APITournamentUserStatus.COMPLETED,
                    replay_url = null
                ),
            )
            var size by remember {
                mutableStateOf(IntSize(0, 0))
            }
            Box(modifier = Modifier.onSizeChanged {
                size = it
            })
            {
                Image(
                    painter = painterResource(id = R.drawable.img_guest_leaderboards),
                    contentDescription = null,
                    contentScale = ContentScale.Fit,
                    modifier = Modifier.fillMaxWidth()
                )
                Box(Modifier.height(size.toDp().height))
                {
                    GuestBlockComponent(
                        title = "Join for free and play real people!",
                        desc = "When you join Faceoff, you can compete for cash in tournaments" +
                                " or go head-to-head with players of similar skill.",
                        label = "Join now for free",
                        removeFade = false,
                        onClick = {
                            LoginViewModel.instance.fanduelSignupFromGuestMode()
                        }
                    )
                }
            }
            HorizontalDivider()
        }
    }
}

@Composable
private fun LeaderboardRefunded(data: APITournamentInstanceEntry?) {
    val entryFee = data?.entry_fee?.toDouble()
    val isPaidContest = entryFee != null && entryFee > 0

    var refundTitle = "This entry was not matched"
    var refundMsg =
        if (isPaidContest) "Your entry was not matched with another player before the contest expired, 24 hours after the start time. Your entry fee has already been put back in your wallet. Check out the lobby to play more games!"
        else "Your entry was not matched with another player before the contest expired, 24 hours after the start time. Check out the lobby to play more games!"

    when (data?.refund_reason) {
        APIRefundReason.onlyPlayer.rawValue -> {
            refundTitle = "This entry was not matched"
            refundMsg =
                if (isPaidContest) "Your entry was not matched with another player before the contest expired, 24 hours after the start time. Your entry fee has already been put back in your wallet. Check out the lobby to play more games!"
                else "Your entry was not matched with another player before the contest expired, 24 hours after the start time. Check out the lobby to play more games!"
        }

        APIRefundReason.autoBooted.rawValue -> {
            refundTitle = "Sorry, this entry was not started"
            refundMsg =
                if (isPaidContest) "You did not start your entry in time, but your entry fee has already been put back in your wallet. Next time, make sure to start your entry within 45 minutes to avoid automatic removal and a refund."
                else "You did not start your entry in time. Next time, make sure to start your entry within 45 minutes to avoid automatic removal."
        }

        APIRefundReason.playerServices.rawValue -> {
            //fixme: this copy is incorrect
            refundTitle = "Sorry, this entry was not started"
            refundMsg =
                if (isPaidContest) "You did not start your entry in time, but your entry fee has already been put back in your wallet. Next time, make sure to start your entry within 45 minutes to avoid automatic removal and a refund."
                else "You did not start your entry in time. Next time, make sure to start your entry within 45 minutes to avoid automatic removal."
        }
    }
    Column(modifier = Modifier.padding(16.dp)) {
        Text(text = refundTitle, style = MaterialTheme.typography.bodyMedium)
        Text(
            text = refundMsg,
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Left,
            modifier = Modifier.padding(top = 8.dp)
        )
    }
}

@Composable
private fun LeaderboardContestDetails(
    data: APITournamentInstanceEntry?,
    isExpandedLeaderboard: Boolean
) {
    var showContestDetails: Boolean by rememberSaveable { mutableStateOf(false) }
    if (data?.hasBeenRefunded() != true) {
        Spacer(modifier = Modifier.height(if(isExpandedLeaderboard) 6.dp else 16.dp))
        //Contest Details
        Row(
            modifier = Modifier
                .padding(8.dp, 0.dp)
                .height(30.dp)
                .fillMaxWidth()
                .clickableWithoutRipple {
                    showContestDetails = !showContestDetails
                },
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        )
        {
            val details = if(data?.maximum_slots == 2){
                if(showContestDetails)"Hide Contest Details" else "Show Contest Details"
            }else{
                if(showContestDetails)"Hide Contest Details and Prizes" else "Show Contest Details and Prizes"
            }
            Text(
                text = details,
                color = Color(0xFF005FC8),
                style = MaterialTheme.typography.bodySmall,
            )
            Image(
                painter = painterResource(id = R.drawable.ic_arrow_down_24dp),
                contentDescription = null,
                colorFilter = ColorFilter.tint(Color(0xFF005FC8)),
                modifier = Modifier
                    .rotate(
                        if (showContestDetails) {
                            180.0f
                        } else {
                            0.0f
                        }
                    )
                    .size(16.dp)
            )
        }

        if(isExpandedLeaderboard /*&& !showContestDetails*/){
            Spacer(modifier = Modifier.height(6.dp))
        }
    }

    if (data != null) {
        ExpandableContestDetails(
            padding = if (isExpandedLeaderboard) PaddingValues(8.dp) else PaddingValues(16.dp),
            isExpanded = showContestDetails || data.hasBeenRefunded(),
            data = data
        )
    }
}

@Composable
private fun LeaderboardScoringBreakdown(
    subScores: MutableMap<String, Pair<APITournamentInstanceEntryExpandedResultScoreItem?, APITournamentInstanceEntryExpandedResultScoreItem?>>
) {
    var showScoringBreakdown: Boolean by rememberSaveable { mutableStateOf(true) }

    //Scoring breakdown
    Row(
        modifier = Modifier
            .padding(8.dp, 6.dp)
            .height(30.dp)
            .fillMaxWidth()
            .clickableWithoutRipple {
                showScoringBreakdown = !showScoringBreakdown
            },
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    )
    {
        Text(
            text = if(showScoringBreakdown)"Hide scoring breakdown" else "Show scoring breakdown",
            color = Color(0xFF005FC8),
            style = MaterialTheme.typography.bodySmall,
        )
        Image(
            painter = painterResource(id = R.drawable.ic_arrow_down_24dp),
            contentDescription = null,
            colorFilter = ColorFilter.tint(Color(0xFF005FC8)),
            modifier = Modifier
                .rotate(
                    if (showScoringBreakdown) {
                        180.0f
                    } else {
                        0.0f
                    }
                )
                .size(16.dp)
        )
    }

    ExpandableScoringBreakdown(
        isExpanded = showScoringBreakdown,
        subScores = subScores
    )
}

@Composable
fun LeaderboardFooter(
    navManager: NavManager,
    isLoading:Boolean,
    isGuest:Boolean,
    data: APITournamentInstanceEntry?,
    showFtue: Boolean,
    onCloseFtue: () -> Unit,
    onPlayAgain: () -> Unit,
    showRewardTab:Boolean = true,
    showRewardsProgress: MutableState<Boolean>,
    relatedGoals: MutableState<List<APIActiveGoal>>,
    goalInfoModalExpanded: MutableState<Boolean>,
    activeRewardInfo: MutableState<APIActiveGoal?>
){
    val showPlayAgain = when {
        data?.tournament_id == null -> false
        //Hide "Play Again" Button on Leaderboard after using a ticket to enter a tournament that is only shown to ticket holders
        data.ticket_info!= null && !data.has_additional_ticket -> false
        !data.is_joinable -> false
        else -> true
    }

    if(!isLoading){
        Surface(
            modifier = Modifier
                .navigationBarsPadding()
//                .shadow(
//                    4.dp,
//                    //spotColor = MaterialTheme.colorScheme.surface
//                )
//                .padding(top = 8.dp)
                .background(Color.Transparent)
                .fillMaxWidth()
        )
        {
            Column {
                if(showRewardTab && relatedGoals.value.size > 0){
                    var offsetY by remember { mutableStateOf(0f) }
                    var showTabInit by remember { mutableStateOf(true) }
                    var showTab by remember { mutableStateOf(true) }
                    val fullHeight = 200.dp
                    val minHeight = 29.dp
                    var columnHeight by remember { mutableStateOf(fullHeight) }
                    val density = LocalDensity.current
                    Column(modifier = Modifier
                        .onAppear { offsetY = 0f }
                        .pointerInput(Unit) {
                            detectVerticalDragGestures(
                                onVerticalDrag = { change, dragAmount ->
                                    offsetY += dragAmount

                                    if (showTab) {
                                        offsetY = Math.max(0f, offsetY)
//                                        offsetY = Math.min(250f, offsetY)
                                    } else {
//                                        offsetY = Math.max(-250f, offsetY)
                                        offsetY = Math.min(0f, offsetY)
                                    }
                                    with(density) {
                                        columnHeight = fullHeight - offsetY.toDp()
                                    }
                                    change.consume()
                                },
                                onDragStart = {
                                    showTab = true
                                },
                                onDragEnd = {
                                    if (showTab) {
                                        if (offsetY > 0 && showTabInit) {
                                            showTab = false
                                            columnHeight = minHeight
                                        }
                                    } else {
                                        if (offsetY < 0) {
                                            showTab = true
                                            columnHeight = fullHeight
                                        }
                                    }
                                    showTabInit = showTab
                                    offsetY = 0f
                                }
                            )
                        }
                    ) {
                        AnimatedVisibility(
                            modifier = Modifier
                                .align(Alignment.CenterHorizontally)
//                                .offset { IntOffset(0, offsetY.roundToInt()) }
                                .height(columnHeight)
                            , visible = true){

                            Column(modifier = Modifier
                                .fillMaxWidth()
                                .clip(
                                    RoundedCornerShape(
                                        topStart = 18.dp,
                                        topEnd = 18.dp
                                    )
                                )
                                .background(MaterialTheme.colorScheme.surface)
                                .shadow(
                                    1.dp,
                                    RoundedCornerShape(
                                        topStart = 18.dp,
                                        topEnd = 18.dp
                                    ),
                                ),
                                horizontalAlignment = Alignment.CenterHorizontally) {
                                Spacer(modifier = Modifier.height(8.dp))

                                Box(modifier = Modifier
                                    .size(36.dp, 5.dp)
                                    .background(Color(0xffc4c4c7), shape = RoundedCornerShape(5.dp))
                                    .padding(vertical = 4.dp))

                                if(showTab){
                                    LeaderboardRewardTab(
                                        showRewardsProgress = showRewardsProgress,
                                        relatedGoals = relatedGoals,
                                        goalInfoModalExpanded = goalInfoModalExpanded,
                                        activeRewardInfo = activeRewardInfo
                                    )
                                }else{
                                    Spacer(modifier = Modifier.height(16.dp))
                                }

                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(2.dp)
                                        .background(ShadowFade)
                                )
                            }
                        }

                    }
                }else{
                    //Shadow
                    if(showPlayAgain)
                    {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(2.dp)
                                .background(ShadowFade)
                        )
                    }
                }
                Box(modifier = Modifier.background(Color.White)){
                    Box(modifier = Modifier.padding(16.dp)) {
                        if (showFtue) {
                            CommonButton(
                                height = 44f,
                                titleStyle = MaterialTheme.typography.bodyLarge,
                                title = "Play more games for fun & prizes!",
                                fillColor = theme_light_primary
                            ) {
                                onCloseFtue()
                                navManager.navigate(Routes.GAMES_SCREEN)
                            }
                        } else if (isGuest) {
                            Text(
                                text = "Play more games as a guest",
                                style = MaterialTheme.typography.bodySmall,
                                textAlign = TextAlign.Center,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        onCloseFtue()
                                        navManager.navigate(Routes.GAMES_SCREEN)
                                    }
                            )
                        } else if (showPlayAgain) {
                            Column {
                                CommonButton(
                                    height = 44f,
                                    titleStyle = MaterialTheme.typography.bodyLarge,
                                    title = "Play Again",
                                    subTitle = data.entryFeeLabel,
                                    fillColor = theme_light_tertiary
                                ) {
                                    onPlayAgain()
                                }
                                val showReferFriend = LeaderboardViewModel.instance.showReferFriend.collectAsState()
                                if (showReferFriend.value) {
                                    Spacer(modifier = Modifier.height(16.dp))

                                    CommonButton(
                                        height = 44f,
                                        titleStyle = MaterialTheme.typography.bodyLarge,
                                        title = "Earn Cash: Refer Friends",
                                        fillColor = theme_light_primary
                                    ) {
                                        FDManager.instance.showWebviewFD(
                                            page = null,
                                            sourceURL = "/account/referrals",
                                            title = "Earn Cash: Refer Friends"
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun LeaderboardRewardTab(
    showRewardsProgress: MutableState<Boolean>,
    relatedGoals: MutableState<List<APIActiveGoal>>,
    goalInfoModalExpanded: MutableState<Boolean>,
    activeRewardInfo: MutableState<APIActiveGoal?>,
){
    val connectedGoals = LeaderboardViewModel.instance.connectedGoals.collectAsState()

    if(relatedGoals.value.size > 0){
//        println("relatedGoals.value.size" + relatedGoals.value.size)

        val goal :APIActiveGoal = relatedGoals.value.get(0)
        var clusterTaskId:String? = null

        if(goal.goalType == APIGoalType.cluster){
            val filteredTasks = goal.sortedTasks.filter {
                connectedGoals.value.contains(it.goal_user_instance_task_id)
            }.sortedByDescending { if(it.isComplete) Int.MAX_VALUE else it.current_progress?.toFloat()?.toInt() }

//            println(filteredTasks)

            if(filteredTasks.size > 0){
                clusterTaskId = filteredTasks[0].goal_user_instance_task_id
            }
        }
        Box(
            modifier = Modifier
                .clickableWithoutRipple {
                    showRewardsProgress.value = true
                }
                .padding(horizontal = 16.dp)
                .padding(top = 15.dp, bottom = 12.dp),
            contentAlignment = Alignment.Center
        ){
            ContentTitle(
                icon = R.drawable.ic_rewards_app,
                title = "Rewards progress",
                style = MaterialTheme.typography.titleSmall,
            )
            Box(
                modifier = Modifier
                    .size(16.dp)
                    .align(Alignment.CenterEnd)
            ) {
                Icon(
                    imageVector = IcBack,
                    tint = MaterialTheme.colorScheme.primary,
                    contentDescription = null,
                    modifier = Modifier
                        .size(10.dp)
                        .rotate(degrees = 180f)
                        .align(Alignment.Center)
                        .offset(0.dp, 1.dp),
                )
            }
        }

        HorizontalDivider()

        getActiveRewardElement(
            goalDataModel = goal,
            onClaimClicked = { callback ->
                if(clusterTaskId != null){
                    val taskData = goal.sortedTasks.find { it.goal_user_instance_task_id == clusterTaskId }
                    if(taskData != null){
                        GoalsViewModel.instance.collectGoal(
                            goalUserInstanceTaskId = taskData.goal_user_instance_task_id!!,
                            goalType = APIGoalType.cluster,
                            rewardAmount = taskData.reward?.toFloat()?:0f,
                            rewardTitle = taskData.title,
                            callback = callback
                        )
                    }
                }else if (goal.goalUserInstanceTaskId != null) {
                    GoalsViewModel.instance.collectGoal(
                        goalUserInstanceTaskId = goal.goalUserInstanceTaskId!!,
                        goalType = goal.goalType,
                        rewardAmount = goal.rewardValue,
                        rewardTitle = goal.titleString,
                        callback = callback
                    )
                }
            },
            onClaimComplete = {
                EventBus.getDefault().post(OnReLoadGoalsDataEvent(true))
            },
            onInfoClicked = {
                activeRewardInfo.value = goal
                goalInfoModalExpanded.value = true
            },
            fromLeaderboard = true,
            clusterTaskId = clusterTaskId
        )()

    }
}
//Fake rank shown below guest
private val waitingPlayerHolderRank = APITournamentInstanceEntryResult(user = APITournamentUser(username = "Waiting for opponent",avatar_url = null,id = "1"),score = null,prize_amount = null,order = null, status = APITournamentUserStatus.OPEN, replay_url = null)

@Composable
fun LeaderboardRewardsProgress(
    navManager: NavManager,
    showRewardsProgress: MutableState<Boolean>,
    isLoading:Boolean,
    isGuest:Boolean,
    data: APITournamentInstanceEntry?,
    showFtue: Boolean,
    onCloseFtue: () -> Unit,
    onPlayAgain: () -> Unit,
    relatedGoals: MutableState<List<APIActiveGoal>>,
    goalInfoModalExpanded: MutableState<Boolean>,
    activeRewardInfo: MutableState<APIActiveGoal?>,
    activeClusterRewardTotalTitle: MutableState<String?>
){
    val connectedGoals = LeaderboardViewModel.instance.connectedGoals.collectAsState()

    BottomSheetModal(
        title = "Rewards Progress",
        showBackButton = true,
        backButtonSize = 14.dp,
        showDivider = true,
        expanded = showRewardsProgress.value,
        closeAction = { showRewardsProgress.value = false;
            AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Modal_Closed.value,
                properties = mapOf("name" to "Rewards Progress Bottom Sheet"))
            )
        },
        enterTransition= slideInHorizontally(initialOffsetX = { fullWidth ->  fullWidth}),
        exitTransition = slideOutHorizontally(targetOffsetX = { fullWidth ->  fullWidth}),
        footer =
        {
            LeaderboardFooter(
                navManager = navManager,
                isLoading = isLoading,
                isGuest = isGuest,
                data = data,
                showFtue = showFtue,
                onCloseFtue = onCloseFtue,
                onPlayAgain = onPlayAgain,
                showRewardTab = false,
                showRewardsProgress = showRewardsProgress,
                relatedGoals = relatedGoals,
                goalInfoModalExpanded,
                activeRewardInfo
            )
        },
    ){
        Text(
            text = "Nice work! You're one step closer to completing these rewards.",
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF445058),
            modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 16.dp)
        )
        Box {
            val listElements: MutableList<@Composable ()->Unit> = mutableListOf()
            var claimable = false
            val array:MutableList<Triple<APIActiveGoal, APIGoalsTask?,Double>> = mutableListOf()
            relatedGoals.value.forEach{ goal ->
                val isCluster = goal.goalType == APIGoalType.cluster
                if(isCluster){
                    val filteredTasks = goal.sortedTasks.filter {
                        connectedGoals.value.contains(it.goal_user_instance_task_id)
                    }
                    filteredTasks.forEach{
                        array.add(Triple(goal,it,it.progressRatio))
                    }
                }else{
                    val currentProgress  = goal.currentProgress.toDouble()
                    val requiredProgress = goal.requiredProgress.toDouble()

                    val ratio = if(requiredProgress > 0 )currentProgress / requiredProgress else 0.0
                    array.add(Triple(goal,null,ratio))
                }
            }
            val sortedGoals= array.sortedByDescending { it.third }

            sortedGoals.forEach{ (goal,clusterTask,progressRatio) ->
                claimable = claimable || goal.isComplete
                val isCluster = goal.goalType == APIGoalType.cluster
                var totalReward = 0.0
                val valueTitle = STR(
                    GetRewardTypeString(goal.rewardType),
                    "in"
                )

                if (isCluster){
                    for (task in goal.sortedTasks){
                        totalReward += task.reward?.toDouble() ?: 0.0
                    }
                    listElements.add(
                        getActiveRewardElement(
                            goalDataModel = goal,
                            onClaimClicked = { callback ->
                                if(clusterTask?.goal_user_instance_task_id != null){
                                    GoalsViewModel.instance.collectGoal(
                                        goalUserInstanceTaskId = clusterTask.goal_user_instance_task_id ?: "",
                                        goalType = APIGoalType.cluster,
                                        rewardAmount = clusterTask.reward?.toFloat()?:0f,
                                        rewardTitle = clusterTask.title,
                                        callback = callback
                                    )
                                }
                            },
                            onClaimComplete = {
                                EventBus.getDefault().post(OnReLoadGoalsDataEvent(true))
                            },
                            onInfoClicked = {
                                activeRewardInfo.value = goal

                                val totalTitle = "Earn ${totalReward.toDollarWithCommaString()} ${valueTitle}!"
                                activeClusterRewardTotalTitle.value = totalTitle
                                goalInfoModalExpanded.value = true
                            },
                            fromLeaderboard = true,
                            clusterTaskId = clusterTask?.goal_user_instance_task_id
                        )
                    )
                }else{
                    listElements.add(
                        getActiveRewardElement(
                            goalDataModel = goal,
                            onClaimClicked = { callback ->
                                if (goal.goalUserInstanceTaskId != null) {
                                    GoalsViewModel.instance.collectGoal(
                                        goalUserInstanceTaskId = goal.goalUserInstanceTaskId!!,
                                        goalType = goal.goalType,
                                        rewardAmount = goal.rewardValue,
                                        rewardTitle = goal.titleString,
                                        callback = callback
                                    )
                                }
                            },
                            onClaimComplete = {
                                EventBus.getDefault().post(OnReLoadGoalsDataEvent(true))
                            },
                            onInfoClicked = {
                                activeRewardInfo.value = goal
                                activeClusterRewardTotalTitle.value = null
                                goalInfoModalExpanded.value = true
                            },
                            fromLeaderboard = true,
                            clusterTaskId = null
                        )
                    )
                }
            }
            if (claimable){
                Spacer(modifier = Modifier.height(8.dp))
            }
            ContentListBody(
                listElements = listElements,
                elementSpacingHeight = 0.dp,
                showDivider = false
            )
        }
    }
}

@Composable
fun LeaderboardPlayerProfile(showProfile:Boolean,profileData:APITournamentPlayerProfile?,entryData: APITournamentInstanceEntry?){

    BottomSheetModal(
        title = "Profile",
        showBackButton = true,
        backButtonSize = 14.dp,
        showDivider = true,
        expanded = showProfile,
        closeAction = {
            LeaderboardViewModel.instance.closeProfile()
        },
        enterTransition= slideInHorizontally(initialOffsetX = { fullWidth ->  fullWidth}),
        exitTransition = slideOutHorizontally(targetOffsetX = { fullWidth ->  fullWidth}),
    ){
        if(profileData == null){
            Spacer(modifier = Modifier.height(200.dp))
            LoadingScreen(backgroundColor = MaterialTheme.colorScheme.onPrimary)
        }else{
            //todo: fill the ui here
            //val hasExtraInfo = profileData.joinDate != null || profileData.gamesPlayed != null
            val isMe = profileData.username == AuthenticationManager.instance.apiMe?.username

            Column(modifier = Modifier
                .fillMaxWidth()
                .onAppear {
                    AnalyticsManager.instance.logEvent(
                        AnalyticsEvent(
                            analyticsEvent = AnalyticsEventName.Modal_Open.value,
                            properties = mapOf("Modal Name" to "player_profile")
                        )
                    )
                }
                .onDisappear {

                }
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(150.dp)
                        .background(BlueMinimalBackgroundSubtle)
                ) {
                    Row(
                        modifier = Modifier.fillMaxHeight(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
//Player Info Text - Not requested for MVP
//                        Column(
//                            modifier = Modifier.weight(1f),
//                            horizontalAlignment = Alignment.CenterHorizontally
//                        ) {
//                            // Join date
//                            Text(
//                                text = "FanDuel player since:",
//                                style = MaterialTheme.typography.titleSmall,
//                                color = MaterialTheme.colorScheme.onPrimary
//                            )
//                            Spacer(modifier = Modifier.height(8.dp))
//                            Text(
//                                text = (profileData.member_since.toDate() ?: Date()).format("MMMM yyyy"),
//                                style = MaterialTheme.typography.bodyLarge,
//                                color = MaterialTheme.colorScheme.onPrimary
//                            )
//
//                            Spacer(modifier = Modifier.height(12.dp))
//
//                            // Games played
//                            Text(
//                                text = "Games played:",
//                                style = MaterialTheme.typography.titleSmall,
//                                color = MaterialTheme.colorScheme.onPrimary
//                            )
//                            Spacer(modifier = Modifier.height(8.dp))
//
//                            Text(
//                                text = (profileData.game_mode_stats?.total_games_played ?: 0).toString(),
//                                style = MaterialTheme.typography.bodyLarge,
//                                color = MaterialTheme.colorScheme.onPrimary
//                            )
//                        }

                        Column(
                            modifier = Modifier.weight(1f, fill = true),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(6.dp)
                        ) {
                            // Player avatar
                            val iconBorderThickness = if (isMe) 4.dp else 2.dp
                            Box(
                                modifier = Modifier
                                    .padding(iconBorderThickness)
                                    .border(
                                        iconBorderThickness,
                                        MaterialTheme.colorScheme.surface,
                                        CircleShape
                                    )
                            ) {
                                if (profileData.avatar_url != null) {
                                    AsyncImage(
                                        model = profileData.avatar_url,
                                        contentDescription = null,
                                        error = painterResource(R.drawable.ic_default_account),
                                        contentScale = ContentScale.Crop,
                                        modifier = Modifier
                                            .size(84.dp)
                                            .clip(CircleShape)
                                    )
                                } else {
                                    Image(
                                        alignment = Alignment.Center,
                                        painter = painterResource(
                                            if (AuthenticationManager.instance.isGuest)
                                                R.drawable.ic_fanduel_account else R.drawable.ic_default_account
                                        ), //Players Profile Img
                                        contentDescription = null,
                                        modifier = Modifier.size(84.dp)
                                    )
                                }
                            }

                            // Username
                            Text(
                                text = profileData.username,
                                style = MaterialTheme.typography.titleSmall.copy(
                                    fontWeight = if (isMe) FontWeight.Bold else FontWeight.Normal
                                ),
                                color = MaterialTheme.colorScheme.onPrimary
                            )
                        }
                    }
                }

                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    // Game-specific stats
                    ContentBox (
                        bodyPaddingValues = PaddingValues(),
                        cornerRoundness = 6.dp
                    ){
                        Column() {
                            Row(
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier
                                    .fillMaxSize()
                                    .background(Color(0xFFF7FBFF))
                                    .padding(12.dp, 6.dp, 12.dp, 4.dp)
                            ) {
                                Text(text = "GAME MODE",
                                    letterSpacing = 1.0.sp,
                                    style = MaterialTheme.typography.headlineSmall
                                )
                                Text(text = "SCORES",
                                    letterSpacing = 1.0.sp,
                                    style = MaterialTheme.typography.headlineSmall
                                )
                            }
                            HorizontalDivider(
                                modifier = Modifier.fillMaxWidth(),
                                thickness = 1.dp,
                                color = MaterialTheme.colorScheme.outlineVariant
                            )
                            val gamePlayed = profileData.game_mode_stats?.total_games_played ?:0

                            Row(modifier = Modifier
                                .fillMaxSize()
                                .padding(8.dp),
                                verticalAlignment = if(gamePlayed < 10) Alignment.CenterVertically else Alignment.Top,
                                horizontalArrangement = Arrangement.SpaceBetween) {

                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.weight(3f)
                                ){
                                    AsyncImage(model = entryData?.game_icon,
                                        contentDescription = null,
                                        modifier = Modifier
                                            .size(40.dp)
                                            .clip(RoundedCornerShape(4.dp))
                                    )

                                    Spacer(modifier = Modifier.width(8.dp))

                                    Text(
                                        text = entryData?.game_mode_name ?: "",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = Color(0xFF005FC8),
                                    )
                                }


                                Column(
                                    modifier = Modifier.weight(2f)
                                ){
                                    Column(
                                        horizontalAlignment = Alignment.End,
                                        verticalArrangement = Arrangement.Center,
                                        modifier = Modifier.padding(top = if(gamePlayed >= 10)4.dp else 0.dp,end = 8.dp)
                                    ) {
                                        Row(modifier = Modifier.fillMaxWidth(),horizontalArrangement = Arrangement.SpaceBetween){
                                            Spacer(modifier = Modifier.width(4.dp))

                                            Text(
                                                modifier = Modifier.weight(1f),
                                                textAlign = TextAlign.Left,
                                                text = "High",
                                                style = MaterialTheme.typography.headlineMedium,
                                            )

                                            Text(
                                                text = profileData.game_mode_stats?.highest_score?.toNumberWithCommaString() ?:"-",
                                                style = MaterialTheme.typography.bodyMedium,
                                                color = Color(0xFF131314)
                                            )
                                        }

                                        Spacer(modifier = Modifier.height(4.dp))

                                        Text(
                                            text = profileData.game_mode_stats?.highest_score_achieved_at?.toDate()
                                                ?.format("MM/dd/yy") ?: "",
                                            style = MaterialTheme.typography.labelSmall,
                                            letterSpacing = 1.sp,
                                            color = Color(0xFF6A6F73)
                                        )
                                    }
                                    if(gamePlayed >= 10){
                                        Column(
                                            horizontalAlignment = Alignment.End,
                                            verticalArrangement = Arrangement.Center,
                                            modifier = Modifier.padding(top = 28.dp,end = 8.dp, bottom = 4.dp)
                                        ) {
                                            Row(modifier = Modifier.fillMaxWidth(),horizontalArrangement = Arrangement.SpaceBetween){
                                                Spacer(modifier = Modifier.width(4.dp))

                                                Text(
                                                    modifier = Modifier.weight(1f),
                                                    textAlign = TextAlign.Left,
                                                    text = "Avg",
                                                    style = MaterialTheme.typography.headlineMedium,
                                                )

                                                Text(
                                                    text = profileData.game_mode_stats?.average_score?.toNumberWithCommaString() ?:"-",
                                                    style = MaterialTheme.typography.bodyMedium,
                                                    color = MaterialTheme.colorScheme.secondary
                                                )
                                            }

                                            Spacer(modifier = Modifier.height(4.dp))

                                            Text(
                                                text = gamePlayed.toNumberWithCommaString() + " GAMES PLAYED",
                                                style = MaterialTheme.typography.labelSmall,
                                                letterSpacing = 1.sp,
                                                color = Color(0xFF6A6F73)

                                            )
                                        }
                                    }
                                }
                            }


                            if(gamePlayed < 10) {
                                Column(
                                    modifier = Modifier
                                        .height(41.dp)
                                        .fillMaxWidth()
                                        .background(Color(0xFFEAF4FF)),
                                    verticalArrangement = Arrangement.Center,
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    val leftTimes = 10 - gamePlayed
                                    val gameStr = if (leftTimes > 1) "games" else "game"
                                    val desc = if(profileData.username == AuthenticationManager.instance.apiMe?.username){
                                        "Play ${leftTimes} more ${gameStr} to view avg score"
                                    }else{
                                        "Player needs ${leftTimes} more ${gameStr} to unlock avg score"
                                    }
                                    Text(
                                        text = desc,
                                        style = MaterialTheme.typography.labelMedium.copy(
                                            fontWeight = FontWeight.Normal,
                                            color = Color(0xFF05285A),
                                            fontSize = 14.sp
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
@Composable
fun LeaderboardReplay(replayURL:String?,data: APITournamentInstanceEntry?,tournamentInstanceEntryId:String?){
    BottomSheetModal(
        title = "Replay Video",
        showBackButton = false,
        backButtonSize = 14.dp,
        showDivider = true,
        expanded = replayURL != null,
        closeAction = {
            LeaderboardViewModel.instance.closeReplay()
        },
        footer = {
            Row (modifier = Modifier
                .navigationBarsPadding()
                .fillMaxWidth()){
                // safe area for bottom nav
            }
        }
//        leftTopContent = {
//            Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Start,modifier = Modifier
//                .fillMaxHeight()
//                .clickable {
//                    println("report...")
//                }){
//                Image(painter = painterResource(R.drawable.ic_warning), modifier = Modifier
//                    .size(16.dp)
//                    .padding(end = 2.dp),contentDescription = null)
//                Text("Report", color = Color(0xFF005FC8))
//            }
//        }
//        enterTransition= slideInHorizontally(initialOffsetX = { fullWidth ->  fullWidth}),
//        exitTransition = slideOutHorizontally(targetOffsetX = { fullWidth ->  fullWidth}),
    ){
        if(replayURL != null) {
            val density = LocalDensity.current
            val configuration = LocalConfiguration.current
            val screenWidthDp = configuration.screenWidthDp
            val screenHeightDp = configuration.screenHeightDp

            val navigationBarPadding = WindowInsets.navigationBars.asPaddingValues()
            var navigationBarHeight = navigationBarPadding.calculateBottomPadding()

            //add more paddings if the navbar not showing
            if(navigationBarHeight <= 24.dp){
                navigationBarHeight += 10.dp
            }

            val videoMaxHeight = screenHeightDp.dp - 60.dp - navigationBarHeight
            val maxRatio = screenWidthDp.dp / videoMaxHeight

//            println("screen size: ${screenWidthDp} x ${screenHeightDp} navigationBarHeight == ${navigationBarHeight}")
//            println("videoMaxHeight::${videoMaxHeight}")
//            println("maxRatio::${maxRatio}")

            val error = remember { mutableStateOf(false) }
            var ratio by remember { mutableStateOf(maxRatio) }

            Box() {
                VideoComponent(
                    videoUrl = replayURL!!,//"https://prod-s3-cdn-games.worldwinner.com/gameAssets/replaysTesting/StreamingGameVideos/7.mp4",//"https://qa1-s3-assets-cdn.wwqa-fd.com/games/6537614d-7ac6-49ab-a9a6-96bc0cc7ab7d.mp4",
                    aspectRatio = ratio,
                    showController = true,
                    successCallback = { isSuccess,err ->
                        Logger.d("isSuccess:" +isSuccess)
                        error.value = !isSuccess
                        LeaderboardViewModel.instance.replayAttempted(isSuccess,err)
                    },
                    videoSizeCallback = {
//                        val widthInDp = with(density) { it.width.toDp() }
//                        val heightInDp = with(density) { it.height.toDp() }
//                        val videoRatio = widthInDp / heightInDp

                        //https://worldwinner.atlassian.net/browse/FX-3078
                        //This is really tricky to reproduce and it would be auto resolved after playing once
                        //This fix is to prevent ExoPlayer not loaded the video resolution yet when the view is first composed.
                        //This means PlayerView doesn't know how to size itself correctly on the first frame, so it gets the wrong height or width and ends up cropping.
                        //So we manually make it re-compose to refresh just in case
                        ratio -= 0.001f
                    }
                )
                if(error.value){
                    Box(modifier = Modifier
                        .fillMaxWidth()
                        .height(111.dp)
                        .background(Color(0x4C000000))
                        .align(Alignment.Center)
                    , contentAlignment = Alignment.Center){
                        Column {
                            Image(
                                painter = painterResource(R.drawable.ic_replays_error),
                                contentDescription = null,
                                modifier = Modifier
                                    .size(50.dp, 44.dp)
                                    .align(Alignment.CenterHorizontally),
                            )

                            Spacer(modifier = Modifier.height(15.dp))

                            Text("Video failed to load.\nPlease try again.",
                                style = TextStyle(
                                    fontFamily = AppFont.ProximaNova,
                                    fontWeight = FontWeight.W700,
                                    fontSize = 16.sp,
                                    lineHeight = 20.sp
                                ),
                                color = Color.White, textAlign = TextAlign.Center,
                            )
                        }
                    }
                }
            }

        }
    }
}

@Preview
@Composable
fun LeaderboardScreenPreview(){
    PreviewRoot {
        LeaderboardScreen(LeaderboardViewModel.instance)
    }
    AppandroidwwTheme {
        Box(
            modifier = Modifier.wrapContentHeight()
        )
        {

        var guestPretendRank = APITournamentInstanceEntryResult(user = APITournamentUser(username = "Potential opponent",avatar_url = null,id = "1"),score = TournamentManager.instance.guestScore,prize_amount = "0.00",order = 1, status = APITournamentUserStatus.COMPLETED, replay_url = null)

        LeaderboardPlayerView(model = guestPretendRank)
        GuestBlockComponent(
            title = "Join for free and play real people!",
            label = "Join now for free",
            desc = "When you join Faceoff, you can compete for cash in tournaments or go head-to-head with players of similar skill.",
            modifier = Modifier.height(250.dp),
            onClick = {
                LoginViewModel.instance.fanduelSignupFromGuestMode()
            }
        )
        }
        Divider()
    }
}
