package com.gametaco.app_android_fd.data.api

//import com.perimeterx.mobile_sdk.main.PXInterceptor
//import com.perimeterx.mobile_sdk.main.PXTimeoutInterceptor
import com.fanduel.coremodules.px.CorePx
import com.gametaco.app_android_fd.BuildConfig
import com.gametaco.app_android_fd.data.AppConstants
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.data.isNonProd
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.DeviceManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.utils.log.HttpLoggingInterceptor
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import retrofit2.Invocation
import retrofit2.Retrofit
import retrofit2.converter.moshi.MoshiConverterFactory
import java.util.concurrent.TimeUnit

class WorldWinnerNetworkService() {

    private var retrofit: Retrofit? = null




    private fun getRetrofit(): Retrofit {
        if (retrofit == null) {
            val httpLoggingInterceptor = if (BuildConfig.DEBUG) {
                HttpLoggingInterceptor().apply {
//            level = HttpLoggingInterceptor.Level.BASIC
//            level = HttpLoggingInterceptor.Level.HEADERS
                    level = HttpLoggingInterceptor.Level.BODY
                }
            } else {
                null
            }

            //this is here to prime the result of DeviceManager.instance.getUserAgent() because it can't be called on the
            //headerInterceptor thread
            DeviceManager.instance.getUserAgent()

            val headerInterceptor = Interceptor { chain ->
                val builder = chain.request().newBuilder()
                builder.header("Content-Type", "application/json")
                builder.header("x-application-id", AppConstants.API_APP_ID)
                builder.header("x-application-version-id", AppConstants.API_APP_VERSION)
//                builder.header("x-user-location", "US,Texas")
                builder.header("x-device-native-id", DeviceManager.instance.getDeviceId()!!)
                builder.header("User-Agent", DeviceManager.instance.getUserAgent()?:"Unknown User Agent")
                // Only add this header if the app is not in production
                if (AppEnv.current.env.isNonProd) {
                    builder.header("x-fanduel-env", "ww")
                }

//                for (entry in PXManager.httpHeaders().entries) {
//                    val key: String = entry.key
//                    val value: String = entry.value
//                    reqBuilder.addHeader(key, value)
//                }

                // Conditionally add special headers based on annotations
                val caller = chain.call()
                val requestMethod = caller.request().tag(Invocation::class.java)?.method()
                requestMethod?.annotations?.forEach { annotation ->
                    if (annotation is WithAuthentication) {
                        builder.header("Authorization", "Token " + AuthenticationManager.instance.authToken)

                        val sessionDataToken = FDManager.instance.sessionDataToken
                        if (sessionDataToken != null) {
                            builder.header(
                                "x-fanduel-auth-token",
                                sessionDataToken
                            )
                        }
                    }
                }

                chain.proceed(builder.build())
            }

            val httpClient = OkHttpClient.Builder().apply {
                addInterceptor(headerInterceptor)
                if (httpLoggingInterceptor != null) {
                    addInterceptor(httpLoggingInterceptor)
                }
                addInterceptor(UnauthorizedInterceptor())
                readTimeout(60, TimeUnit.SECONDS)
                addInterceptor(CorePx.instance.getPxInterceptor())

//                addInterceptor(PXTimeoutInterceptor())
//                addInterceptor(PXInterceptor())
            }

            val moshi = Moshi.Builder()
                .add(KotlinJsonAdapterFactory())
                .build()

            retrofit = Retrofit.Builder()
                .baseUrl(AppEnv.current.api_endpoint)
                .client(httpClient.build())
                .addConverterFactory(MoshiConverterFactory.create(moshi))
                .build()
        }
        return retrofit!!
    }

    fun invalidRetrofit(){
        retrofit = null
    }

    fun getApiService(): ApiService {
        return getRetrofit().create(ApiService::class.java)
    }
}