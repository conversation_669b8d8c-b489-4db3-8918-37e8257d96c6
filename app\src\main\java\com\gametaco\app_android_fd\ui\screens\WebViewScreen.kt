package com.gametaco.app_android_fd.ui.screens

import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.viewinterop.AndroidView
import com.gametaco.app_android_fd.ui.theme.AppandroidwwTheme
import com.gametaco.app_android_fd.viewmodel.WebViewViewModel

@Composable
fun WebViewScreen(viewModel: WebViewViewModel) {
    val isVisible by viewModel.isVisible.collectAsState()
    val url by viewModel.url.collectAsState()

    if (isVisible && url != null) {
        AppandroidwwTheme {
            Box(modifier = Modifier.fillMaxSize()) {
                AndroidView(
                    factory = { context ->
                        WebView(context).
                        apply {
                            settings.javaScriptEnabled = true
                            settings.domStorageEnabled = true;
                            settings.loadsImagesAutomatically = true
                            settings.useWideViewPort = true
                            settings.setSupportZoom(true)
                            settings.builtInZoomControls = true
                            webViewClient = WebViewClient()
                            loadUrl(url!!)
                        }
                    },
                    modifier = Modifier.matchParentSize()
                )
            }
        }
    }
}
@Preview
@Composable
fun WebViewScreen(){
    PreviewRoot {
        WebViewScreen(viewModel = WebViewViewModel.instance)
    }
}