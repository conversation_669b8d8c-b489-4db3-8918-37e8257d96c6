package com.gametaco.app_android_fd.ui.screens

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.gametaco.app_android_fd.BuildConfig
import com.gametaco.app_android_fd.LocalAlertDialogManager
import com.gametaco.app_android_fd.LocalNavManager
import com.gametaco.app_android_fd.LocalUIManager
import com.gametaco.app_android_fd.data.api.APIMe
import com.gametaco.app_android_fd.data.entity.APIWalletResponse
import com.gametaco.app_android_fd.data.navigation.NavigationData
import com.gametaco.app_android_fd.manager.AlertDialogManager
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.FDWebviewPage
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.OnSessionChangedEvent
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.SystemBarState
import com.gametaco.app_android_fd.manager.WalletManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.ui.components.AccountOption
import com.gametaco.app_android_fd.ui.components.AccountOptionBorderDivider
import com.gametaco.app_android_fd.ui.components.BalanceDetailsPopup
import com.gametaco.app_android_fd.ui.components.ContentListBody
import com.gametaco.app_android_fd.ui.components.NavBar
import com.gametaco.app_android_fd.ui.components.NoRippleButton
import com.gametaco.app_android_fd.ui.modifiers.onAppear
import com.gametaco.app_android_fd.ui.popups.AlertDialogButtonsSideBySide
import com.gametaco.app_android_fd.ui.theme.AppandroidwwTheme
import com.gametaco.app_android_fd.ui.theme.BlueSingleBackground
import com.gametaco.app_android_fd.ui.theme.theme_light_background
import com.gametaco.app_android_fd.ui.utils.getNavigationBarHeight
import com.gametaco.app_android_fd.ui.utils.getStatusBarHeight
import com.gametaco.app_android_fd.utils.toDollarString
import com.gametaco.app_android_fd.utils.toNumberWithCommaString
import com.gametaco.app_android_fd.viewmodel.AccountViewModel
import com.gametaco.app_android_fd.viewmodel.LoginViewModel
import com.gametaco.utilities.STR
import org.greenrobot.eventbus.EventBus
import resources.R

var isHidden = mutableStateOf(PreferencesManager.instance.getIsBalanceHidden())


@Composable
fun AccountScreen(
    accountViewModel: AccountViewModel
){
    val uiManager = LocalUIManager.current
    LaunchedEffect(Unit) {
        uiManager.setSystemBarState(SystemBarState.TransparentLightStatusBarIcons)
    }

    var showHapticsScreen by remember { mutableStateOf(false) }
    var showInfoModal by remember { mutableStateOf(false) }
    val isGuest = LocalInspectionMode.current || AuthenticationManager.instance.isGuest
    val wallet by WalletManager.instance.wallet.collectAsState()
    val apiMe by AuthenticationManager.instance.apiMeFlow.collectAsState()
    val alertDialogManager = LocalAlertDialogManager.current
    val navManager = LocalNavManager.current

    val accountOptionsFirstSection: List<@Composable () -> Unit> = createFirstSectionOptions()
    val accountOptionsSecondSection: List<@Composable () -> Unit> = createSecondSectionOptions(
        onShowHapticsScreen = { showHapticsScreen = true }
    )
    val accountOptionsThirdSection: List<@Composable () -> Unit> = createThirdSectionOptions()
    val accountOptionsFourthSection: List<@Composable () -> Unit> = createFourthSectionOptions(
        isGuest = isGuest,
        alertDialogManager = alertDialogManager
    )

    AccountScreen(
        navManager,
        isGuest,
        apiMe,
        wallet,
        accountOptionsFirstSection,
        accountOptionsSecondSection,
        accountOptionsThirdSection,
        accountOptionsFourthSection,
        onShowModal = { showInfoModal = true }
    )

//    HapticsScreen(
//        isExpanded = showHapticsScreen,
//        onClose = { showHapticsScreen = false }
//    )

    BalanceDetailsPopup(isExpanded = showInfoModal) {
        showInfoModal = false
        AnalyticsManager.instance.logEvent(
            AnalyticsEvent(analyticsEvent = AnalyticsEventName.Modal_Closed.value,
            properties = mapOf("name" to "Balance Details Popup"))
        )
    }
}

@Composable
private fun createFirstSectionOptions(): List<@Composable () -> Unit> {
    return mutableListOf<@Composable () -> Unit>().apply {
            add {
                AccountOption(
                    gameIcon = R.drawable.ic_transaction_history,
                    title = STR(R.string.transaction_history),
                    isAvailableForGuest = false,
                    webviewPage = FDWebviewPage.Transactions
                )
            }
            add {
                AccountOption(
                    gameIcon = R.drawable.ic_tax_info,
                    title = STR(R.string.tax_information_forms),
                    isAvailableForGuest = false,
                    webviewPage = FDWebviewPage.TaxInfo
                )
            }
            add {
                AccountOption(
                    gameIcon = R.drawable.ic_activity_statement,
                    title = STR(R.string.activity_statement),
                    isAvailableForGuest = false,
                    webviewPage = FDWebviewPage.Activity
                )
            }
            add {
                AccountOption(
                    gameIcon = R.drawable.ic_refer_friends,
                    title = STR(R.string.earn_cash_refer_friends),
                    isAvailableForGuest = false,
                    webviewPage = FDWebviewPage.None,
                    hasdivider = false,
                    webviewURL = "/account/referrals"
                )
            }
        }
}

@Composable
private fun createSecondSectionOptions(
    onShowHapticsScreen: () -> Unit,
): List<@Composable () -> Unit> {
    return mutableListOf<@Composable () -> Unit>().apply {
        add{
            AccountOption(
                gameIcon = R.drawable.ic_acc_settings,
                title = STR(R.string.account_settings),
                isAvailableForGuest = false,
                webviewPage = FDWebviewPage.Settings
            )
        }
        add{
            AccountOption(
                gameIcon = R.drawable.ic_noitification_settings,
                title = STR(R.string.notification_settings),
                isAvailableForGuest = false,
                webviewPage = FDWebviewPage.None,
                webviewURL = "/account/notifications"
            )
        }
//        add{
//            AccountOption(
//                gameIcon = R.drawable.ic_sound_and_haptics,
//                title = STR(R.string.haptics),
//                isAvailableForGuest = true,
//                hasdivider = false,
//                webviewPage = FDWebviewPage.None,
//                clickFunction = { onShowHapticsScreen() }
//            )
//        }
    }
}

@Composable
fun createThirdSectionOptions(): MutableList<@Composable () -> Unit> {
    return mutableListOf<@Composable () -> Unit>().apply {
        add{
            AccountOption(
                gameIcon = R.drawable.ic_support,
                title = STR(R.string.support),
                isAvailableForGuest = true,
                webviewPage = FDWebviewPage.Support,
                webviewURL = null,
            )
        }
        add{
            AccountOption(
                gameIcon = R.drawable.ic_my_spend,
                title = STR(R.string.my_spend),
                isAvailableForGuest = false,
                webviewPage = FDWebviewPage.None,
                webviewURL = "/my-spend"
            )
        }
        add{
            AccountOption(
                gameIcon = R.drawable.ic_responsible_play,
                title = STR(R.string.responsible_play),
                isAvailableForGuest = true,
                webviewPage = FDWebviewPage.ResponsiblePlay
            )
        }
        add{
            AccountOption(
                gameIcon = R.drawable.ic_training_guides,
                title = STR(R.string.training_guides),
                isAvailableForGuest = true,
                webviewPage = FDWebviewPage.None,
                webviewURL = "https://www.fanduel.com/faceoff-training-guide"
            )
        }
        add{
            AccountOption(
                gameIcon = R.drawable.ic_rules_and_scoring,
                title = STR(R.string.rules_and_scoring),
                isAvailableForGuest = true,
                webviewPage = FDWebviewPage.None,
                webviewURL = "https://www.fanduel.com/faceoff-rules"
            )
        }
        add{
            AccountOption(
                gameIcon = R.drawable.ic_about_faceoff,
                title = STR(R.string.about_faceoff),
                isAvailableForGuest = true,
                webviewPage = FDWebviewPage.None,
                webviewURL = "https://www.fanduel.com/faceoff"
            )
        }
        add{
            AccountOption(
                gameIcon = R.drawable.ic_terms_of_use,
                title = STR(R.string.terms_of_use),
                isAvailableForGuest = true,
                webviewPage = FDWebviewPage.None,
                webviewURL = "https://www.fanduel.com/faceoff-terms"
            )
        }
        add{
            AccountOption(
                gameIcon = R.drawable.ic_privacy,
                title = STR(R.string.privacy),
                isAvailableForGuest = true,
                hasdivider = false,
                webviewPage = FDWebviewPage.None,
                webviewURL = "https://www.fanduel.com/privacy"
            )
        }
    }
}

@Composable
fun createFourthSectionOptions(
    isGuest: Boolean,
    alertDialogManager: AlertDialogManager,
): MutableList<@Composable () -> Unit> {
    return mutableListOf<@Composable () -> Unit>().apply {
        if(!isGuest)
        {
            add{
                AccountOption(
                    gameIcon = R.drawable.ic_logout,
                    title = STR(R.string.log_out),
                    isAvailableForGuest = false,
                    webviewPage = FDWebviewPage.None,
                    hasdivider = false,
                    clickFunction = {
                        alertDialogManager.showDialogCustom(composeFunction = @Composable {
                            AlertDialogButtonsSideBySide(title = "Are you sure you want to logout?",
                                aButtonText = "No",
                                onAPressed = {
                                },
                                bButtonText = "Yes",
                                onBPressed = {
                                    EventBus.getDefault().post(OnSessionChangedEvent(false, "AccountScreen/createFourthSectionOptions"))
                                })
                        })
                    }
                )
            }
        }
    }
}

@Composable
private fun AccountScreen(
    navManager: NavManager,
    isGuest: Boolean,
    apiMe: APIMe,
    wallet: APIWalletResponse,
    accountOptionsFirstSection: List<@Composable () -> Unit>,
    accountOptionsSecondSection: List<@Composable () -> Unit>,
    accountOptionsThirdSection: List<@Composable () -> Unit>,
    accountOptionsFourthSection: List<@Composable () -> Unit>,
    onShowModal: () -> Unit,
) {
    val statusBarHeight = getStatusBarHeight()

    AppandroidwwTheme {
//        Background(BlueMinimalBackground)

        Scaffold(
            containerColor = Color.Transparent,
            contentColor = theme_light_background,
            modifier = Modifier
                .background(Color.Transparent),
            topBar = {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(statusBarHeight)
                        .background(BlueSingleBackground),
                ) {
                }
            },
            bottomBar = {
                NavBar(navData = NavigationData.instance, navManager = navManager)
            }
        )
        { paddingValues ->
            Surface(
                color = theme_light_background,
                modifier = Modifier
                    .fillMaxSize(),
            ) {
                Column(
                    modifier = Modifier
                        .background(Color.Transparent)
                        .fillMaxHeight()
                        .verticalScroll(rememberScrollState())
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(paddingValues.calculateTopPadding())
                            .background(BlueSingleBackground),
                    ) {
                    }

                    AccountHeader(
                        isGuest,
                        apiMe,
                        wallet,
                        onShowModal = onShowModal,
                    )
                    Column(
                        modifier = Modifier
                            .background(theme_light_background)
                    )
                    {
                        if (accountOptionsFirstSection.isNotEmpty()) {
                            PrintAccountOptions(listElements = accountOptionsFirstSection)
                        }
                        if (accountOptionsSecondSection.isNotEmpty()) {
                            PrintAccountOptions(listElements = accountOptionsSecondSection)
                        }
                        if (accountOptionsThirdSection.isNotEmpty()) {
                            PrintAccountOptions(listElements = accountOptionsThirdSection)
                        }
                        if (accountOptionsFourthSection.isNotEmpty()) {
                            PrintAccountOptions(listElements = accountOptionsFourthSection)
                        }

                        Spacer(modifier = Modifier.size(16.dp))
                        // Version number
                        Text(
                            text = "Version ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})",
                            style = MaterialTheme.typography.labelMedium,
                            textAlign = TextAlign.Center,
                            color = Color.Black,
                            modifier = Modifier.fillMaxWidth()
                        )

                        // nav bar item height + system nav bar height + a bit extra
                        val bottomSpacingHeight = 80.dp + getNavigationBarHeight() + 16.dp
                        Spacer(modifier = Modifier.height(bottomSpacingHeight))
                    }
                }
            }
        }
    }
}

@Composable
private fun AccountHeader(
    isGuest: Boolean,
    apiMe: APIMe,
    wallet: APIWalletResponse,
    onShowModal: () -> Unit,
) {
    var isExpanded by rememberSaveable {
        mutableStateOf(false)
    }
    val expandedValue by animateFloatAsState(
        targetValue = if(isExpanded) 1.0f else 0.0f,
        animationSpec = tween(),
        label = ""
    )

    Column(
        Modifier.background(BlueSingleBackground)
    )
    {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Transparent)
                .padding(4.dp, 48.dp, 4.dp, 16.dp)
        )
        {
            Column(
                //modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Top
            ) {
                AccountProfile(isGuest, apiMe)
                Spacer(Modifier.height(4.dp))
                Text(
                    text = if (isGuest) STR(R.string.faceoff_guest) else AuthenticationManager.instance.apiMe?.username?.uppercase()
                        ?: STR(R.string.username),
                    style = MaterialTheme.typography.headlineSmall.copy(color = MaterialTheme.colorScheme.onPrimary, letterSpacing = 1.sp)
                )
                Spacer(modifier = Modifier.height(24.dp))
                if (!isGuest) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalAlignment = Alignment.CenterVertically

                    )
                    {
                        Text(
                            text = STR(R.string.playable_balance),
                            style = MaterialTheme.typography.labelMedium.copy(
                                color = MaterialTheme.colorScheme.onPrimary,
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp
                            )
                        )
                        Text(
                            text = " | ",
                            style = MaterialTheme.typography.labelMedium.copy(
                                color = MaterialTheme.colorScheme.onPrimary,
                                fontSize = 24.sp
                            )
                        )
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxHeight()
                                .clickable {
                                    isHidden.value = !isHidden.value
                                    NavigationData.instance.setAccountValueHidden(
                                        isHidden.value
                                    )
                                    PreferencesManager.instance.setIsBalanceHidden(
                                        isHidden.value
                                    )
                                    isExpanded = false
                                }
                        )
                        {
                            Image(
                                painter = painterResource(
                                    if (!isHidden.value) {
                                        R.drawable.ic_hide
                                    } else {
                                        R.drawable.ic_show
                                    }
                                ),
                                contentDescription = null,
                                modifier = Modifier.size(24.dp, 24.dp)
                            )
                            Spacer(modifier = Modifier.width(2.dp))
                            Text(
                                text = if (isHidden.value) STR(R.string.show) else STR(R.string.hide),
                                style = MaterialTheme.typography.labelMedium.copy(
                                    color = MaterialTheme.colorScheme.onPrimary,
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 14.sp
                                ),
                                modifier = Modifier
                                    .width(48.dp)
                            )
                        }
                    }
                    Spacer(Modifier.height(12.dp))

                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(
                            12.dp,
                            Alignment.CenterHorizontally
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(44.dp)
                    )
                    {
                        if (!isHidden.value) {

                            Text(
                                text = wallet.balance.toNumberWithCommaString(includeDecimals = true).toDollarString(),
                                style = MaterialTheme.typography.titleLarge.copy(color = MaterialTheme.colorScheme.onPrimary, fontSize = 40.sp),
                                modifier = Modifier.onAppear {
                                    WalletManager.instance.refreshWallet()
                                }
                            )
                            Image(
                                painter = painterResource(R.drawable.ic_expand_arrow),
                                contentDescription = null,
                                Modifier
                                    .clickable { isExpanded = !isExpanded }
                                    .size(36.dp)
                                    .rotate(
                                        if (isExpanded) {
                                            180.0f
                                        } else {
                                            0.0f
                                        }
                                    )
                            )
                        } else {
                            HorizontalDivider(
                                modifier = Modifier.fillMaxWidth(.3f),
                                thickness = 4.dp,
                                color = MaterialTheme.colorScheme.onPrimary
                            )
                        }
                    }
                } else {
                    Column(
                        modifier = Modifier.padding(18.dp, 0.dp)
                    )
                    {
                        Spacer(Modifier.height(12.dp))
                        Text(
                            text = STR(R.string.join_now_to_access_your_full_account),
                            style = MaterialTheme.typography.bodyMedium.copy(color = MaterialTheme.colorScheme.onPrimary)
                        )
                        Spacer(modifier = (Modifier.height(4.dp)))//LineHeight - Fontsize
                        Text(
                            text = STR(R.string.register_to_play_cash_games),
                            style = MaterialTheme.typography.bodySmall.copy(color = MaterialTheme.colorScheme.onPrimary)
                        )
                        Spacer(modifier = Modifier.height(12.dp))
                        OutlinedButton(
                            onClick = { LoginViewModel.instance.fanduelSignupFromGuestMode() },
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(4.dp)
                        )
                        {
                            Text(
                                text = STR(R.string.join_now_free),
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onPrimary
                            )
                        }
                    }
                }
            }
        }
        ExpandedBalance(wallet, expandedValue, onShowModal)
        //Deposit/Withdraw
        if (!isGuest) {
            Box(
                modifier = Modifier
                    .wrapContentHeight()
                    .fillMaxWidth()
                    .background(Color.Transparent)
            )
            {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(0.dp, (expandedValue * 16).dp, 0.dp, 16.dp),
                    horizontalArrangement = Arrangement.Absolute.Center,
                    verticalAlignment = Alignment.CenterVertically
                )
                {
                    Spacer(modifier = Modifier.weight(1f))
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.weight(1f)
                    )
                    {
                        FilledIconButton(
                            onClick = {
                                FDManager.instance.showWebviewFD(FDWebviewPage.Deposit)
                            },
                            modifier = Modifier.size(48.dp),
                            colors = IconButtonDefaults.filledIconButtonColors(Color(0xFF127F00))
                        )
                        {
                            Image(
                                painter = painterResource(R.drawable.ic_deposit),
                                contentDescription = null,
                                colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.onPrimary),
                                modifier = Modifier.size(18.dp)
                            )
                        }
                        Spacer(modifier = Modifier.size(8.dp))
                        Text(
                            text = STR(R.string.deposit),
                            style = MaterialTheme.typography.headlineSmall.copy(color = MaterialTheme.colorScheme.onPrimary, letterSpacing = 1.sp)
                        )
                    }
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.weight(1f)
                    )
                    {
                        FilledIconButton(
                            onClick = {
                                FDManager.instance.showWebviewFD(page = FDWebviewPage.Withdrawal, title = "Withdraw")
                            },
                            modifier = Modifier.size(48.dp),
                            colors = IconButtonDefaults.filledIconButtonColors(MaterialTheme.colorScheme.onPrimary)
                        ) {
                            Image(
                                painter = painterResource(R.drawable.ic_withdraw),
                                contentDescription = null,
                                colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.primary),
                                modifier = Modifier.size(18.dp)
                            )
                        }
                        Spacer(modifier = Modifier.size(8.dp))
                        Text(
                            text = STR(R.string.withdraw),
                            style = MaterialTheme.typography.headlineSmall.copy(color = MaterialTheme.colorScheme.onPrimary, letterSpacing = 1.sp)
                        )
                    }
                    Spacer(modifier = Modifier.weight(1f))
                }
            }
        }
    }
}

@Composable
private fun ExpandedBalance(
    wallet: APIWalletResponse,
    expandedValue: Float,
    onShowModal: () -> Unit,
) {
    if (expandedValue == 0f) return

    Box(
        modifier = Modifier
            .height((220 * expandedValue).dp)
            .fillMaxWidth()
            .background(Color(0xFF0C498F))
    )
    {
        Column(
            verticalArrangement = Arrangement.spacedBy(20.dp),
            modifier = Modifier
                .padding(16.dp, 20.dp, 16.dp, 8.dp)
                .fillMaxSize()
        )
        {
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            )
            {
                Text(
                    text = STR(R.string.playable_balance),
                    color = MaterialTheme.colorScheme.onPrimary,
                    style = MaterialTheme.typography.bodyMedium
                )
                Image(
                    painter = painterResource(R.drawable.ic_info),
                    contentDescription = null,
                    colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.onPrimary),
                    modifier = Modifier
                        .size(16.dp)
                        .clickable {
                            println("expandedValue: $expandedValue")
                            onShowModal()
                        }
                )
            }
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            )
            {
                Text(
                    text = STR(R.string.shared_deposits),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimary
                )
                Text(
                    wallet.shared_deposit_balance.toNumberWithCommaString(true).toDollarString(),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimary
                )
            }
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            )
            {
                Text(
                    text = STR(R.string.bonuses),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimary
                )
                Text(
                    wallet.bonus_balance.toNumberWithCommaString(true).toDollarString(),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimary
                )
            }
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            )
            {
                Text(
                    text = STR(R.string.shared_winnings),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimary
                )
                Text(
                    wallet.shared_winnings_balance.toNumberWithCommaString(true).toDollarString(),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimary
                )
            }
            OutlinedButton(
                onClick = { FDManager.instance.showWebviewFD(FDWebviewPage.Home) },
                border = BorderStroke(1.dp, Color.White),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(44.dp),
                shape = RoundedCornerShape(4.dp)
            )
            {
                Text(
                    text = STR(R.string.view_balance_details),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onPrimary
                )
            }
        }
    }
}

@Composable
private fun AccountProfile(isGuest: Boolean, apiMe: APIMe) {
    NoRippleButton(onClick =
    {
        if (!isGuest) {
            FDManager.instance.showWebviewFD(FDWebviewPage.Home)
        } else {
            LoginViewModel.instance.fanduelSignupFromGuestMode()
        }
    })
    {
        if (isGuest) {
            Image(
                painter = painterResource(R.drawable.ic_fanduel_account),
                contentDescription = null,
                modifier = Modifier.size(36.dp)
            )
        } else {
            AsyncImage(
                model = apiMe.avatar_url,
                contentDescription = null,
                error = painterResource(R.drawable.ic_default_account),
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .size(36.dp)
                    .clip(CircleShape)
            )
        }
    }
}

@Composable
fun PrintAccountOptions(listElements: List<@Composable ()->Unit>) {
    Spacer(modifier = Modifier.height(16.dp))
    AccountOptionBorderDivider()
    ContentListBody(listElements = listElements, precedingSpacerHeight = 0.dp, elementSpacingHeight = 0.dp, showDivider = false)
    AccountOptionBorderDivider()
}

@Preview
@Composable
fun AccountScreenPreview(){
    PreviewRoot {
        AccountScreen(AccountViewModel.instance)
    }
}
