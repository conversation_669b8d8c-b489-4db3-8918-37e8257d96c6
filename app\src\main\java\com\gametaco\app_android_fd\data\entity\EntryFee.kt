package com.gametaco.app_android_fd.data.entity

import com.gametaco.app_android_fd.utils.toDollarString

sealed interface EntryFee {

    val string: String
    val value: Float

    val originalFee:String
    val originalValue:Float

    val isFree: Boolean
        get() = value == 0f

    val isFeeWaived:<PERSON>olean
        get() = originalValue > 0 && isFree

    data object Unknown : EntryFee {
        override val string: String
            get() = "0.00"
        override val value: Float
            get() = 0f
        override val originalFee: String
            get() = "0.00"
        override val originalValue: Float
            get() = 0f
    }

    data class Defined(override val string: String, override val originalFee: String) : EntryFee {
        override val value: Float by lazy { string.toFloatOrNull() ?: 0f }
        override val originalValue: Float by lazy { originalFee.toFloatOrNull() ?: 0f }
    }

    // User-facing label for entry fee
    val label: String
        get() = entryFeeLabel(isFree = isFree, value = string)
}

fun EntryFee(string: String? = null,originalFee: String? = null): EntryFee {
    if (string.isNullOrEmpty()) {
        return EntryFee.Unknown
    }
    return EntryFee.Defined(string,originalFee ?: string)
}

fun entryFeeLabel(isFree: Boolean, value: String,ext:String = "ENTRY"): String {
    return if (isFree) {
        "FREE ${ext}"
    } else {
        "${value.toDollarString()} ${ext}"
    }
}
