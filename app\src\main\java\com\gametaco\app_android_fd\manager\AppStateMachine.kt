package com.gametaco.app_android_fd.manager
import APIWallet
import TournamentEntryState
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.utils.CoroutineScopes
import kotlinx.coroutines.launch
import java.net.URL

// Interfaces for event handling

interface TournamentEventHandling {
    val isGameLoading: Boolean
    fun didAcknowledgeGameFailure()
    fun failedToJoinTournament(message: String)
    fun failedToSubmitScore()
    fun gameCanceled()
    fun gameIsLaunched()
    fun gameFinished(tournamentId: String?, tournamentInstanceEntryId: String?)
    fun didAcknowledgeFinishedGame()
    fun gameLoading(tournamentId: String, state: TournamentEntryState)
    fun unityErrorOccurred()
}

interface VideoEventHandling {
    fun didStartPlayingVideo(videoUrl: URL)
    fun didFinishPlayingVideo()
}

interface WalletStateHandling {
    fun walletIsMissing()
    fun walletIsValid(wallet: APIWallet)
    fun walletIsStale()
    fun walletTransactionDidBegin()
}

// AppStateMachine Implementation

class AppStateMachine(
    private val coroutineScopes: CoroutineScopes,
) : TournamentEventHandling,
    VideoEventHandling,
    WalletStateHandling {

    // States
    var currentAppState: AppState =
        AppState.AppHome
    var currentWalletState: WalletState =
        WalletState.Missing

    init {
    }

    // Implementation of interfaces

    override val isGameLoading: Boolean
        get() = currentAppState is com.gametaco.app_android_fd.manager.AppState.GameLoading

    override fun didAcknowledgeGameFailure() {
        updateState { currentAppState =
            AppState.AppHome
        }
    }

    override fun gameCanceled() {
        updateState {
            currentAppState =
                AppState.GameFinished(
                    null,
                    null
                )
        }
    }

    override fun gameIsLaunched() {
        updateState { currentAppState =
            AppState.GameIsBeingPlayed
        }
    }

    override fun failedToJoinTournament(message: String) {
        updateState { currentAppState =
            AppState.FailedToJoinTournament(
                message
            )
        }
    }

    override fun failedToSubmitScore() {
        updateState { currentAppState =
            AppState.FailedToSubmitScore
        }
    }

    override fun gameFinished(tournamentId: String?, tournamentInstanceEntryId: String?) {
        updateState {
            currentWalletState =
                WalletState.Stale
            currentAppState =
                AppState.GameFinished(
                    tournamentId,
                    tournamentInstanceEntryId
                )
        }
    }

    override fun gameLoading(tournamentId: String, state: TournamentEntryState) {
        updateState { currentAppState =
            AppState.GameLoading(
                tournamentId,
                state
            )
        }
    }

    override fun unityErrorOccurred() {
        updateState { currentAppState =
            AppState.UnityError
        }

        val properties = mutableMapOf<String, Any>()
        properties["Error Summary"] = "Unity Error"

        val tournamentAnalytics = TournamentManager.instance.getCurrentTournamentStats()
        if (tournamentAnalytics != null) {
            properties["Tournament Instance ID"] = tournamentAnalytics.contestId
            properties["Game Name"] = tournamentAnalytics.gameName
            properties["Stake"] = tournamentAnalytics.stake
            properties["Game Mode"] = tournamentAnalytics.gameMode
            properties["Tournament Instance Entry ID"] = tournamentAnalytics.tournamentInstanceEntryId
        }
        AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Game_Failed.value,
            properties = properties))
    }

    // WalletStateHandling
    override fun walletIsMissing() {
        updateState { currentWalletState =
            WalletState.Missing
        }
    }

    override fun walletIsValid(wallet: APIWallet) {
        updateState { currentWalletState =
            WalletState.Valid(wallet)
        }
    }

    override fun walletIsStale() {
        updateState { currentWalletState =
            WalletState.Stale
        }
    }

    override fun walletTransactionDidBegin() {
        updateState { currentWalletState =
            WalletState.TransactionInProgress
        }
    }

    // VideoEventHandling
    override fun didStartPlayingVideo(videoUrl: URL) {
        updateState { currentAppState =
            AppState.VideoIsPlaying(videoUrl)
        }
    }

    override fun didFinishPlayingVideo() {
        updateState { if (currentAppState !is AppState.GameLoading && currentAppState !is AppState.GameIsBeingPlayed) currentAppState =
            AppState.AppHome
        }
    }

    private fun updateState(action: () -> Unit) {
        coroutineScopes.main.launch {
            action()
        }
    }

    override fun didAcknowledgeFinishedGame() {

        updateState {
            when (currentAppState) {
                is AppState.GameIsBeingPlayed, is AppState.GameLoading -> {
                    // do nothing
                }

                else -> {
                    currentAppState =
                        AppState.AppHome
                }
            }
        }
    }

}

// Supporting data classes and enums

sealed class AppState {
    object AppHome : AppState()
    object FailedToSubmitScore : AppState()
    data class FailedToJoinTournament(val message: String) : AppState()
    data class GameFinished(val tournamentId: String?, val tournamentInstanceEntryId: String?) : AppState()
    object GameIsBeingPlayed : AppState()
    data class GameLoading(val tournamentId: String, val state: TournamentEntryState) : AppState()
    object UnityError : AppState()
    data class VideoIsPlaying(val videoUrl: URL) : AppState()
    object AlertIsShowing : AppState()
}

sealed class WalletState {
    object Missing : WalletState()
    object Stale : WalletState()
    object TransactionInProgress : WalletState()
    data class Valid(val wallet: APIWallet) : WalletState()
}

sealed class MaintenanceModeState {
    object NotUnderMaintenance : MaintenanceModeState()
    data class ForceUpdate(val message: String?, val imgUrl: String?) : MaintenanceModeState()
    data class DownForMaintenance(val message: String?, val imgUrl: String?) : MaintenanceModeState()
}
